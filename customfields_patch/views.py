import configparser

from django.shortcuts import render, redirect
from .forms import patchclientForm,patchloansForm,patchusersForm, patchclientFormPlatug
from .tasks import patch_fields, patch_fields_momentum, patch_fields_premier, patch_fields_premierug, patch_fields_platinumug
import xlrd
import xlsxwriter
from django.http import HttpResponse, HttpResponseRedirect
from mambu.utils import Mambu, MambuError, MambuAPIError
from mambu.models import Credential, Environment
import base64
import json
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.clickjacking import xframe_options_exempt
from django.core.mail import send_mail, BadHeaderError,EmailMessage
import datetime
import pickle
import requests

# Create your views here.

@csrf_exempt
@xframe_options_exempt
def customfield_index(request):
    user_role =""
    mambu_user_email =""
    environment_name =""
    bulk_posting_form = patchclientForm(request.POST, request.FILES)
    
    signed_request = request.POST.get('signed_request')
    if signed_request is not None:
        encoded_string = signed_request.split('.')[1]
        encoded_string += "=" * ((4 - len(encoded_string) % 4) % 4)
        decoded_string = base64.b64decode(encoded_string).decode("utf-8")
        obj = json.loads(decoded_string)
        mambu_user_key = obj['USER_KEY']
        environment_name = obj["DOMAIN"].split('.mambu.com', 1)[0]
        # print("Environment Name: ",environment_name)
        env = Environment.objects.get(url=environment_name)
        # auth_user = Credential.objects.get(username="customfieldpatch", environment=env)
        auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
        # mambu = Mambu(auth_user.username, auth_user.password, auth_user.environment.url)
        mambu = Mambu(auth_user.apikey, auth_user.generatedkey, auth_user.environment.url)
        mambu_user = mambu.get_users(username=mambu_user_key, fullDetails=True)
        mambu_user_email = mambu_user["email"]
        request.session['mambu_user'] = mambu_user

        user_role = mambu_user["role"]["encodedKey"] if "role" in mambu_user else ""
        ##System Admin Keys, Portfolio Manager

    if request.session.get('mambu_user'):
        c = {'form': bulk_posting_form, 'signed_request': signed_request}
        allowed_roles = ["8a858e5257d85fa40157f5827a2416e0", "8a858edb5de51fa9015deb09ef6267f9",
                         "8a858f5659cb44e30159cbe844281635","8a858f8659db725c0159dbd4c28e3a2d"]

        if user_role in allowed_roles:
            if (request.POST):
                bulk_posting_form = patchclientForm(request.POST, request.FILES)

                if bulk_posting_form.is_valid():
                    entity = request.POST.get('entity')
                    file_name = request.FILES['file'].name
                    try:

                        patch_fields.delay(pickle.dumps(request.FILES['file'].read()), mambu_user_email, entity,
                                           environment_name, file_name, updateClear)
                    except patch_fields.OperationalError as exc:
                        logger.exception('Sending task raised: %r', exc)

                    return HttpResponseRedirect('/customfields_patch/progress?email=' + mambu_user_email)
            else:
                bulk_posting_form = patchclientForm()
            return render(request, 'pcl_customfields_update/bulk_clients_patch.html',c)
        else:
            return redirect('customfields_patch:rolenotallowedplatkenya')
    else:
       return render(request,'pcl_customfields_update/404.html')



def role_not_allowed(request):
    return render(request,'pcl_customfields_update/access_denied.html')


##New Patch Custom FIeld Module
@csrf_exempt
def update_client_new(request):
    status=""
    mambu_user_email = request.session.get('mambu_user')['email']
    environment_name = request.session["environment_name"]

    errors = []
    if (request.POST):
        bulk_posting_form = patchclientForm(request.POST, request.FILES)
        
        if bulk_posting_form.is_valid():
            entity = request.POST.get('entity')
            file_name = request.FILES['file'].name
            try:
                
                patch_fields.delay(pickle.dumps(request.FILES['file'].read()),mambu_user_email,entity,environment_name,file_name)
            except patch_fields.OperationalError as exc:
                logger.exception('Sending task raised: %r', exc)

            return HttpResponseRedirect('/customfields_patch/progress?email='+mambu_user_email)
    else:
        bulk_posting_form = patchclientForm()
    return render(request, 'pcl_customfields_update/bulk_clients_patch.html', {"form": bulk_posting_form, 'errors': errors})


def progress(request):
    if request.method == 'GET':
        status = "processing ...."

        return render(request, 'pcl_customfields_update/progress.html', {'data': status, "emailadd":request.GET.get('email')})


@csrf_exempt
@xframe_options_exempt
def premiercustomfield_index(request):
    bulk_posting_form = patchclientFormPlatug(request.POST, request.FILES)
    user_role = ""
    mambu_user_email = ""
    environment_name = ""
    signed_request = request.POST.get('signed_request')
    if signed_request is not None:
        encoded_string = signed_request.split('.')[1]
        encoded_string += "=" * ((4 - len(encoded_string) % 4) % 4)
        decoded_string = base64.b64decode(encoded_string).decode("utf-8")
        obj = json.loads(decoded_string)
        mambu_user_key = obj['USER_KEY']
        environment_name = obj["DOMAIN"].split('.mambu.com', 1)[0]
        env = Environment.objects.get(url=environment_name)
        auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
        mambu = Mambu(auth_user.apikey, auth_user.generatedkey, auth_user.environment.url)
        mambu_user = mambu.get_users(username=mambu_user_key, fullDetails=True)

        user_role = mambu_user["role"]["encodedKey"] if "role" in mambu_user else ""
        mambu_user_email = mambu_user["email"]


        ##MIS KEY,"Loan Administrator


        # if role_key in allowed_roles:
        #     request.session['mambu_user'] = mambu_user
        #     request.session['environment_name'] = environment_name
        #     return redirect('customfields_patch:premier_update-client')
        # else:
        #     return redirect('customfields_patch:rolenotallowedplatkenya')
    c = {'form': bulk_posting_form, 'signed_request': signed_request}
    allowed_roles = ["8a725e1346caaee20146cc5f29240c52","8a858f7e5c213ef4015c2f2a75bc6d3e",
                     "8a81886d52134ded015217aea25b3c82","8a1a184a46dc1d950146f2090f155e8d",
                     "8ada76a1438f611e014394ceea035d86","8a93865477b3aeeb0177ba90d0390f82",
                    "8a93865477b3aeeb0177ba90d0390f82","8a9387de867f37dc01867fbe2c970d16"]

    if user_role in allowed_roles:
        if (request.POST):
            bulk_posting_form = patchclientForm(request.POST, request.FILES)

            if bulk_posting_form.is_valid():
                entity = request.POST.get('entity')
                updateClear = request.POST.get('updateClear')
                file_name = request.FILES['file'].name
                try:

                    patch_fields_premier.delay(pickle.dumps(request.FILES['file'].read()), mambu_user_email, entity,
                                       environment_name, file_name,updateClear)
                except patch_fields_premier.OperationalError as exc:
                    logger.exception('Sending task raised: %r', exc)

                return HttpResponseRedirect('/customfields_patch/progress?email=' + mambu_user_email)
        else:
            bulk_posting_form = patchclientForm()
        return render(request, 'pcl_customfields_update/bulk_clients_patch.html',c)
    else:
        return redirect('customfields_patch:rolenotallowedplatkenya')


@csrf_exempt
def premier_update_client(request):
    status=""
    mambu_user_email = request.session.get('mambu_user')['email']
    environment_name = request.session["environment_name"]

    errors = []
    if (request.POST):
        bulk_posting_form = patchclientForm(request.POST, request.FILES)
        
        if bulk_posting_form.is_valid():
            entity = request.POST.get('entity')
            file_name = request.FILES['file'].name
            try:
                
                patch_fields_premier.delay(pickle.dumps(request.FILES['file'].read()),mambu_user_email,entity,environment_name,file_name)
            except patch_fields.OperationalError as exc:
                logger.exception('Sending task raised: %r', exc)

            return HttpResponseRedirect('/customfields_patch/progress?email='+mambu_user_email)
    else:
        bulk_posting_form = patchclientForm()
    return render(request, 'pcl_customfields_update/bulk_clients_patch.html', {"form": bulk_posting_form, 'errors': errors})


@csrf_exempt
@xframe_options_exempt
def momentumcustomfield_index(request):
    mambu_user = {}
    
    signed_request = request.POST.get('signed_request')
    if signed_request is not None:
        encoded_string = signed_request.split('.')[1]
        encoded_string += "=" * ((4 - len(encoded_string) % 4) % 4)
        decoded_string = base64.b64decode(encoded_string).decode("utf-8")
        obj = json.loads(decoded_string)
        mambu_user_key = obj['USER_KEY']
        environment_name = obj["DOMAIN"].split('.mambu.com', 1)[0]
        # print("Environment Name: ",environment_name)
        env = Environment.objects.get(url=environment_name)
        auth_user = Credential.objects.get(username="customfieldPatch", environment=env)
        mambu = Mambu(auth_user.username, auth_user.password, auth_user.environment.url)
        mambu_user = mambu.get_users(username=mambu_user_key, fullDetails=True)

        role_key = mambu_user["role"]["encodedKey"] if "role" in mambu_user else ""
        ##System Admin Keys
        allowed_roles = ["8a818e485b7d992d015b84f900ff60a4",]

        if role_key in allowed_roles:
            request.session['mambu_user'] = mambu_user
            request.session['environment_name'] = environment_name
            return redirect('customfields_patch:momentum_update-client')
        else:
            return redirect('customfields_patch:rolenotallowedplatkenya')
    else:
        return render(request, 'pcl_customfields_update/404.html')


@csrf_exempt
def momentum_update_client(request):
    status=""
    mambu_user_email = request.session.get('mambu_user')['email']
    environment_name = request.session["environment_name"]

    errors = []
    if (request.POST):
        bulk_posting_form = patchclientForm(request.POST, request.FILES)
        
        if bulk_posting_form.is_valid():
            entity = request.POST.get('entity')
            file_name = request.FILES['file'].name
            try:
                
                patch_fields_momentum.delay(pickle.dumps(request.FILES['file'].read()),mambu_user_email,entity,environment_name,file_name)
            except patch_fields.OperationalError as exc:
                logger.exception('Sending task raised: %r', exc)

            return HttpResponseRedirect('/customfields_patch/progress?email='+mambu_user_email)
    else:
        bulk_posting_form = patchclientForm()
    return render(request, 'pcl_customfields_update/bulk_clients_patch.html', {"form": bulk_posting_form, 'errors': errors})


@csrf_exempt
@xframe_options_exempt
def premierugcustomfield_index(request):
    mambu_user = {}

    signed_request = request.POST.get('signed_request')
    bulk_posting_form = patchclientFormPlatug(request.POST, request.FILES)
    mambu_user_email = ''
    environment_name =''
    user_role = ''
    if signed_request is not None:
        encoded_string = signed_request.split('.')[1]
        encoded_string += "=" * ((4 - len(encoded_string) % 4) % 4)
        decoded_string = base64.b64decode(encoded_string).decode("utf-8")
        obj = json.loads(decoded_string)
        mambu_user_key = obj['USER_KEY']
        environment_name = obj["DOMAIN"].split('.mambu.com', 1)[0]

        config = configparser.ConfigParser()
        config.read('secrets.ini')
        customFieldPatch = config.get('Credentials', 'PREMIERUGANDA_PROD_APIKEY_CUSTOMFIELDPATCH')
        mambu = Mambu("ApiKey", customFieldPatch, environment_name)
        mambu_user = mambu.get_users(username=mambu_user_key, fullDetails=True)
        request.session['mambu_user'] = mambu_user
        mambu_user_email = mambu_user["email"]
        user_role = mambu_user["role"]["encodedKey"] if "role" in mambu_user else ""

    c = {'form': bulk_posting_form, 'signed_request': signed_request}
    allowed_roles = ["8abc1aea45eb1c2a0145ef7b21155074", "8a9386ab6bd02d4b016bd10f978c18d6","8a1a184a46dc1d950146f2090f155e8d"]

    if request.session.get('mambu_user'):
        if user_role in allowed_roles:
            if (request.POST):
                if bulk_posting_form.is_valid():
                    entity = request.POST.get('entity')
                    updateClear = request.POST.get('updateClear')
                    file_name = request.FILES['file'].name
                    try:

                        patch_fields_premierug.delay(pickle.dumps(request.FILES['file'].read()), mambu_user_email, entity,
                                                     environment_name, file_name, updateClear)
                    except patch_fields.OperationalError as exc:
                        logger.exception('Sending task raised: %r', exc)

                    return HttpResponseRedirect('/customfields_patch/progress?email=' + mambu_user_email)
            else:
                bulk_posting_form = patchclientForm()
            return render(request, 'pcl_customfields_update/bulk_clients_patch.html',c)

        return redirect('customfields_patch:rolenotallowedplatkenya')
    else:
        return render(request, 'pcl_customfields_update/404.html')


@csrf_exempt
def premierug_update_client(request):
    status=""
    mambu_user_email = request.session.get('mambu_user')['email']
    environment_name = request.session["environment_name"]

    errors = []
    if (request.POST):
        bulk_posting_form = patchclientForm(request.POST, request.FILES)
        
        if bulk_posting_form.is_valid():
            entity = request.POST.get('entity')
            file_name = request.FILES['file'].name
            try:
                
                patch_fields_premierug.delay(pickle.dumps(request.FILES['file'].read()),mambu_user_email,entity,environment_name,file_name)
            except patch_fields.OperationalError as exc:
                logger.exception('Sending task raised: %r', exc)

            return HttpResponseRedirect('/customfields_patch/progress?email='+mambu_user_email)
    else:
        bulk_posting_form = patchclientForm()
    return render(request, 'pcl_customfields_update/bulk_clients_patch.html', {"form": bulk_posting_form, 'errors': errors})


@csrf_exempt
@xframe_options_exempt
def platinumugcustomfield_index(request):
    mambu_user = {}
    environment_name = ""
    mambu_user_email = ""
    role_key = ""
    bulk_posting_form = patchclientFormPlatug(request.POST, request.FILES)

    signed_request = request.POST.get('signed_request')
    # signed_request ="704b5d71be3e61b48206da8e97880edb354c99f3a88ef7978b4dbd658163187c.eyJET01BSU4iOiJwbGF0aW51bXVnYW5kYS5zYW5kYm94Lm1hbWJ1LmNvbSIsIkFMR09SSVRITSI6ImhtYWNTSEEyNTYiLCJURU5BTlRfSUQiOiJwbGF0aW51bXVnYW5kYSIsIlVTRVJfS0VZIjoiOGE5Mzg3ZmU2ODVmMzU2MjAxNjg1ZmE2NDQ0YjA3Y2UifQ"

    if signed_request is not None:
        encoded_string = signed_request.split('.')[1]
        encoded_string += "=" * ((4 - len(encoded_string) % 4) % 4)
        decoded_string = base64.b64decode(encoded_string).decode("utf-8")
        obj = json.loads(decoded_string)
        mambu_user_key = obj['USER_KEY']
        environment_name = obj["DOMAIN"].split('.mambu.com', 1)[0]
        print("Environment Name: ",environment_name)
        env = Environment.objects.get(url=environment_name)
        auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
        mambu = Mambu(auth_user.apikey, auth_user.generatedkey, auth_user.environment.url)
        mambu_user = mambu.get_users(username=mambu_user_key, fullDetails=True)
        mambu_user_email = mambu_user["email"]
        environment_name =environment_name
        request.session['mambu_user'] = mambu_user



        role_key = mambu_user["role"]["encodedKey"] if "role" in mambu_user else ""

    c = {'form': bulk_posting_form, 'signed_request': signed_request}
    environment_name = environment_name
    allowed_roles = ["8a858ee159de9b420159debe33340dda", "8a9387627018d51501701a19f093238d",
                     "8a858fd159dfee9c0159e040bce42db4", "8a858fc159e004c60159e018e81809ff"]

    if request.session.get('mambu_user'):

        if role_key in allowed_roles:
            if request.method == "POST":
                if bulk_posting_form.is_valid():
                    file_name = request.FILES['file'].name
                    updateClear = request.POST.get('updateClear')
                    entity = request.POST.get('entity')

                    try:
                        patch_fields_platinumug.delay(pickle.dumps(request.FILES['file'].read()), mambu_user_email, entity,environment_name, file_name,updateClear)
                    except patch_fields_platinumug.OperationalError as exc:
                        logger.exception('Sending task raised: %r', exc)
                    return HttpResponseRedirect('/customfields_patch/progress?email=' + mambu_user_email)
            else:
                bulk_posting_form = patchclientForm()
            return render(request, 'pcl_customfields_update/bulk_clients_patch.html',c)
        else:
            return redirect('customfields_patch:rolenotallowedplatkenya')
    else:
        return render(request, 'pcl_customfields_update/404.html')


@csrf_exempt
def platinumug_update_client(request):
    status=""
    mambu_user_email = request.session.get('mambu_user')['email']
    environment_name = request.session["environment_name"]
    if request.session.get('mambu_user'):

        errors = []
        if (request.POST):
            bulk_posting_form = patchclientForm(request.POST, request.FILES)

            if bulk_posting_form.is_valid():
                entity = request.POST.get('entity')
                updateClear = request.POST.get('updateClear')
                file_name = request.FILES['file'].name
                try:

                    patch_fields_platinumug.delay(pickle.dumps(request.FILES['file'].read()),mambu_user_email,entity,environment_name,file_name,updateClear)
                except patch_fields.OperationalError as exc:
                    logger.exception('Sending task raised: %r', exc)

                return HttpResponseRedirect('/customfields_patch/progress?email='+mambu_user_email)
        else:
            bulk_posting_form = patchclientForm()
        return render(request, 'pcl_customfields_update/bulk_clients_patch.html', {"form": bulk_posting_form, 'errors': errors})

    else:
        return render(request, 'pcl_customfields_update/404.html')
