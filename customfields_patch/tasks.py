from __future__ import absolute_import, unicode_literals

import configparser

from newugandalms.celery import app
from mambu.models import Credential, Environment
import pickle
import xlsxwriter
from django.core.mail import EmailMessage
from io import BytesIO
import redis
from datetime import datetime
import xlrd
import json
import os
from django.conf import settings
import requests
from requests.exceptions import RequestException

def get_config():
    config = configparser.ConfigParser()
    config.read('secrets.ini')
    return config

config = get_config()
platinumUgandaKeySandbox = config.get('Credentials', 'PLATINUMUGANDA_SANDBOX_APIKEY_CUSTOMFIELDPATCH')
platinumUgandaKeyProduction = config.get('Credentials', 'PLATINUMUGANDA_PRODUCTION_APIKEY_CUSTOMFIELDPATCH')

##Pcl Kenya credentials
pclurl = "https://platinumkenya.mambu.com/api/"
pcl_sandbox_url = "https://platinumkenya.sandbox.mambu.com/api/"

premurl = "https://premierkenya.mambu.com/api/"
premurlke_sandbox = "https://premierkenya.sandbox.mambu.com/api/"
momurl = "https://momentumcreditltd.sandbox.mambu.com/api/"

premugurl = "https://premieruganda.mambu.com/api/"

pclugurl = "https://platinumuganda.mambu.com/api/"
pclugurl_sandbox = "https://platinumuganda.sandbox.mambu.com/api/"



###Final customfield patch
@app.task
def patch_fields(s_file,mambu_user_email,entity,environment_name,file_name, updateClear):
    env = Environment.objects.get(url=environment_name)
    # auth_user = Credential.objects.get(username="customfieldpatch", environment=env)
    auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
    # passwordpcl=(auth_user.username, auth_user.password)
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/vnd.mambu.v2+json',
        auth_user.apikey: auth_user.generatedkey
    }

    start_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    file = pickle.loads(s_file)
    emailaddress=mambu_user_email
    
    workbook = xlrd.open_workbook(file_contents=file)
    sheet = workbook.sheet_by_index(0)

    receipt_store = redis.StrictRedis(host='localhost', port=6379, db=1, decode_responses=True)
    receipt_store.config_get('databases')
    receipt_store.flushall()

    file_object=BytesIO() # create a file-like object
    report=xlsxwriter.Workbook(file_object)
    results_to_post=report.add_worksheet('Patch Results')
    results_to_post_count=0
    results_to_post.write_row(0,0,('ID','VALUE','FIELDSET ID','CUSTOM FIELD ID','RETURN CODE','RETURN STATUS','ERROR SOURCE'))

    receipts_count=0
    return_code=''
    return_status=''
    error_source=''
    TaskId=[]
    field_id=[]
    fieldset_id=[]
    params=[]


    for row in range(1, sheet.nrows):
        try:
            Id = int(sheet.cell(row, 0).value)
        except ValueError:
            Id = sheet.cell(row, 0).value

        try:
            value_id = sheet.cell(row, 1).value
        except ValueError:
            value_id = sheet.cell(row, 1).value

        try:
            customfield_id = sheet.cell(row, 2).value
        except ValueError:
            customfield_id = sheet.cell(row, 2).value

        try:
            fieldset_id_value = sheet.cell(row, 3).value
        except ValueError:
            fieldset_id_value = sheet.cell(row, 3).value

        # print('Id',Id)
        # print('value',value_id)
        # print('customfield_id',customfield_id)
        # print('fieldset_id',fieldset_id_value)

        TaskId = [Id]
        field_id = [customfield_id]
        fieldset_id = [fieldset_id_value]
        params = [value_id]
        

        payload = [
            {
                "op": "REPLACE",
                "path": f"/{fieldset_id[0]}/{field_id[0]}",
                "value": params[0]
            }
            ]

        if entity is not None:
            root_url = pcl_sandbox_url if environment_name.endswith('sandbox') else pclurl
            url = root_url + f"{entity}/{TaskId[0]}"
            # print("TASKSURL: ",url)
            try:
                # res = requests.patch(url, json=payload, auth=passwordpcl)
                res = requests.patch(url, json=payload, headers=headers)

            except RequestException as err:
                raise err
            else:
                if 'errors' in res.json() and res.json()['errors']:
                    error_info = res.json()['errors'][0]
                    return_code = error_info.get('errorCode', 0)
                    return_status = error_info.get('errorReason', 'SUCCESS')
                    error_source = error_info.get('errorSource', '')
                else:
                    return_code = 0
                    return_status = 'SUCCESS'
                    error_source = ''

                receipts_count+=1
                receipt_store.lpush(TaskId[0],'{params[0],fieldset_id[0],field_id[0],return_code,return_status,error_source}')

        # print ("REDIS STORE: ",receipt_store.scan_iter())
        count = 0
        for key in receipt_store.scan_iter(TaskId[0]):
            # print("KEY: ",key)
            count = count + 1
            results_to_post_count = results_to_post_count + 1

            dat = receipt_store.lrange(key, 0, -1)
            # print("REDIS DATA: ",dat)
            for row in dat:
                row_values = eval(row)
                if params[0] in row_values:
                    redis_params = params[0]
                if fieldset_id[0] in row_values:
                    redis_fieldset = fieldset_id[0]
                if field_id[0] in row_values:
                    redis_field = field_id[0]
                if return_code in row_values:
                    redis_code = return_code
                if return_status in row_values:
                    redis_status = return_status
                if error_source in row_values:
                    redis_error = error_source

            data=(TaskId[0],redis_params, redis_fieldset, redis_field, redis_code, redis_status, redis_error)
            # print("DATA FROM REDIS: ",data)
            results_to_post.write_row(results_to_post_count,0,data)
            # print("counted: {} keys".format(count))
    report.close()
        
    ##send email
    try:
        emailaddresses=['<EMAIL> ']

        emailaddresses.append(emailaddress)
        # print("EmailAddress: ",emailaddresses)
        
        email = EmailMessage('CustomField update report for '+entity, 'Please find attached report for '+entity +'\n Start time :'+start_time+'\n Finished: '+datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '<EMAIL>',emailaddresses)
        # email = EmailMessage('CustomField update report for '+entity, 'Please find attached report for '+entity +'\n Start time :'+start_time+'\n Finished: '+datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '<EMAIL>',emailaddresses)
        email.content_subtype = "html"
        email.attach('Results for '+file_name, file_object.getvalue(), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        emailsent=email.send()
    except Exception as e:
        print ("ERROREMAILMESSAGE: ",e)


@app.task
def patch_fields_momentum(s_file,mambu_user_email,entity,environment_name,file_name):
    env = Environment.objects.get(url=environment_name)
    # auth_user = Credential.objects.get(username="customfieldPatch", environment=env)
    auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
    # passwordpcl=(auth_user.username, auth_user.password)
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/vnd.mambu.v2+json',
        auth_user.apikey: auth_user.generatedkey
    }

    start_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    file = pickle.loads(s_file)
    emailaddress=mambu_user_email
    
    workbook = xlrd.open_workbook(file_contents=file)
    sheet = workbook.sheet_by_index(0)

    receipt_store = redis.StrictRedis(host='localhost', port=6379, db=1, decode_responses=True)
    receipt_store.config_get('databases')
    receipt_store.flushall()

    file_object=BytesIO() # create a file-like object
    report=xlsxwriter.Workbook(file_object)
    results_to_post=report.add_worksheet('Patch Results')
    results_to_post_count=0
    results_to_post.write_row(0,0,('ID','VALUE','FIELDSET ID','CUSTOM FIELD ID','RETURN CODE','RETURN STATUS','ERROR SOURCE'))

    receipts_count=0
    return_code=''
    return_status=''
    error_source=''
    TaskId=[]
    field_id=[]
    fieldset_id=[]
    params=[]


    for row in range(1, sheet.nrows):
        try:
            Id = int(sheet.cell(row, 0).value)
        except ValueError:
            Id = sheet.cell(row, 0).value

        try:
            value_id = sheet.cell(row, 1).value
        except ValueError:
            value_id = sheet.cell(row, 1).value

        try:
            customfield_id = sheet.cell(row, 2).value
        except ValueError:
            customfield_id = sheet.cell(row, 2).value

        try:
            fieldset_id_value = sheet.cell(row, 3).value
        except ValueError:
            fieldset_id_value = sheet.cell(row, 3).value

        # print('Id',Id)
        # print('value',value_id)
        # print('customfield_id',customfield_id)
        # print('fieldset_id',fieldset_id_value)

        TaskId = [Id]
        field_id = [customfield_id]
        fieldset_id = [fieldset_id_value]
        params = [value_id]

        payload = [
            {
                "op": "REPLACE",
                "path": f"/{fieldset_id[0]}/{field_id[0]}",
                "value": params[0]
            }
        ]

        if entity is not None:
            url = momurl + f"{entity}/{TaskId[0]}"
            # print("TASKSURL: ",url)
            try:
                # res = requests.patch(url, json=payload, auth=passwordpcl)
                res = requests.patch(url, json=payload, headers=headers)
            except RequestException as err:
                raise err
            else:
                if 'errors' in res.json() and res.json()['errors']:
                    error_info = res.json()['errors'][0]
                    return_code = error_info.get('errorCode', 0)
                    return_status = error_info.get('errorReason', 'SUCCESS')
                    error_source = error_info.get('errorSource', '')
                else:
                    return_code = 0
                    return_status = 'SUCCESS'
                    error_source = ''

                receipts_count+=1
                receipt_store.lpush(TaskId[0],'{params[0],fieldset_id[0],field_id[0],return_code,return_status,error_source}')

        # print ("REDIS STORE: ",receipt_store.scan_iter())
        count = 0
        for key in receipt_store.scan_iter(TaskId[0]):
            # print("KEY: ",key)
            count = count + 1
            results_to_post_count = results_to_post_count + 1

            dat = receipt_store.lrange(key, 0, -1)
            # print("REDIS DATA: ",dat)
            for row in dat:
                row_values = eval(row)
                if params[0] in row_values:
                    redis_params = params[0]
                if fieldset_id[0] in row_values:
                    redis_fieldset = fieldset_id[0]
                if field_id[0] in row_values:
                    redis_field = field_id[0]
                if return_code in row_values:
                    redis_code = return_code
                if return_status in row_values:
                    redis_status = return_status
                if error_source in row_values:
                    redis_error = error_source

            data=(TaskId[0],redis_params, redis_fieldset, redis_field, redis_code, redis_status, redis_error)
            # print("DATA FROM REDIS: ",data)
            results_to_post.write_row(results_to_post_count,0,data)
            # print("counted: {} keys".format(count))
    report.close()
        
    ##send email
    try:
        emailaddresses=['<EMAIL> ']
        # cc=['<EMAIL>']

        emailaddresses.append(emailaddress)
        # print("EmailAddress: ",emailaddresses)

        email = EmailMessage('CustomField update report for '+entity, 'Please find attached report for '+entity +'\n Start time :'+start_time+'\n Finished: '+datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '<EMAIL>',emailaddresses)
        email.content_subtype = "html"
        email.attach('Results for '+file_name, file_object.getvalue(), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        emailsent=email.send()
    except Exception as e:
        print ("ERROREMAILMESSAGE: ",e)

@app.task
def patch_fields_premier(s_file,mambu_user_email,entity,environment_name,file_name,updateClear):
    config = configparser.ConfigParser()
    config.read('secrets.ini')
    customFieldPatch = config.get('Credentials', 'PREMIERKE_PRODUCTION')
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/vnd.mambu.v2+json',
        "apiKey": "{}".format(customFieldPatch)
    }
    print("headers",headers)
    start_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    file = pickle.loads(s_file)
    emailaddress=mambu_user_email
    
    workbook = xlrd.open_workbook(file_contents=file)
    sheet = workbook.sheet_by_index(0)

    receipt_store = redis.StrictRedis(host='localhost', port=6379, db=1, decode_responses=True)
    receipt_store.config_get('databases')
    receipt_store.flushall()

    file_object=BytesIO() # create a file-like object
    report=xlsxwriter.Workbook(file_object)
    results_to_post=report.add_worksheet('Patch Results')
    results_to_post_count=0
    results_to_post.write_row(0,0,('ID','VALUE','FIELDSET ID','CUSTOM FIELD ID','RETURN CODE','RETURN STATUS','ERROR SOURCE'))

    receipts_count=0
    return_code=''
    return_status=''
    error_source=''
    TaskId=[]
    field_id=[]
    fieldset_id=[]
    params=[]


    for row in range(1, sheet.nrows):

        print("ROW",sheet.cell(row, 0).value)
        try:
            Id = sheet.cell(row, 0).value
        except ValueError:
            Id = sheet.cell(row, 0).value
        try:
            value_id = sheet.cell(row, 1).value
        except ValueError:
            value_id = sheet.cell(row, 1).value

        try:
            customfield_id = sheet.cell(row, 2).value
        except ValueError:
            customfield_id = sheet.cell(row, 2).value

        try:
            fieldset_id_value = sheet.cell(row, 3).value
        except ValueError:
            fieldset_id_value = sheet.cell(row, 3).value

        # print('Id',Id)
        # print('value',value_id)
        # print('customfield_id',customfield_id)
        # print('fieldset_id',fieldset_id_value)

        TaskId = [Id]
        field_id = [customfield_id]
        fieldset_id = [fieldset_id_value]
        params = [value_id]

        if updateClear == 'CLEARFIELDS':
            payload = [
            {
                "op": "REMOVE",
                "path": f"/{fieldset_id[0]}/{field_id[0]}",
                "value": ""
            }
            ]
        else:
            payload = [
                {
                    "op": "ADD",
                    "path": f"/{fieldset_id[0]}/{field_id[0]}",
                    "value": params[0]
                }
            ]
            
            
        if entity is not None:
            root_url = premurlke_sandbox if environment_name.endswith('sandbox') else premurl
            url = root_url + f"{entity}/{TaskId[0]}"
            print("TASKPREMIER URL: ",url)
            try:
                res = requests.patch(url, json=payload, headers=headers)
                
                # Check if response is successful
                if res.status_code == 204:  # No Content - successful update
                    # print("PLATINUM UG RESPONSE: Success (No Content)")
                    return_code = 0
                    return_status = 'SUCCESS'
                    error_source = ''
                elif res.status_code >= 400:  # Error status codes
                    print(f"PREMIERKE ERROR: Status {res.status_code}, Response: {res.text}")
                    return_code = res.status_code
                    return_status = f'HTTP_ERROR_{res.status_code}'
                    error_source = res.text[:200] if res.text else 'No error details'
                else:
                    # Try to parse JSON response for other status codes
                    try:
                        response_json = res.json()
                        print("PREMIERKE  RESPONSE", response_json)
                        
                        if 'errors' in response_json and response_json['errors']:
                            error_info = response_json['errors'][0]
                            return_code = error_info.get('errorCode', 0)
                            return_status = error_info.get('errorReason', 'SUCCESS')
                            error_source = error_info.get('errorSource', '')
                        else:
                            return_code = 0
                            return_status = 'SUCCESS'
                            error_source = ''
                    except ValueError as json_err:
                        # Response is not JSON or empty
                        print(f"PREMIERKE JSON Parse Error: {json_err}, Response text: {res.text}")
                        return_code = res.status_code
                        return_status = 'JSON_PARSE_ERROR'
                        error_source = f'Invalid JSON response: {str(json_err)}'

            except RequestException as err:
                print(f"PREMIERKE  Request Error: {err}")
                return_code = 999
                return_status = 'REQUEST_ERROR'
                error_source = str(err)
            
            receipts_count+=1
            receipt_store.lpush(TaskId[0], json.dumps([params[0], fieldset_id[0], field_id[0], return_code, return_status, error_source]))

    # After processing all rows, collect all results from Redis and write to the report
    results_to_post_count = 0
    for key in receipt_store.scan_iter():
        dat = receipt_store.lrange(key, 0, -1)
        for row in dat:
            row_values = json.loads(row)
            # row_values = eval(row)
            redis_params = row_values[0] if len(row_values) > 0 else ''
            redis_fieldset = row_values[1] if len(row_values) > 1 else ''
            redis_field = row_values[2] if len(row_values) > 2 else ''
            redis_code = row_values[3] if len(row_values) > 3 else ''
            redis_status = row_values[4] if len(row_values) > 4 else ''
            redis_error = row_values[5] if len(row_values) > 5 else ''
            data = (key, redis_params, redis_fieldset, redis_field, redis_code, redis_status, redis_error)
            results_to_post_count += 1
            results_to_post.write_row(results_to_post_count, 0, data)
    report.close()
        
    ##send email
    try:
        emailaddresses=['<EMAIL>']
        cc=['<EMAIL>']
        
        email = EmailMessage('CustomField update report for '+entity, 'Please find attached report for '+entity +'\n Start time :'+start_time+'\n Finished: '+datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '<EMAIL>',emailaddresses)
        email.content_subtype = "html"
        email.attach('Results for '+file_name, file_object.getvalue(), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        emailsent=email.send()
    except Exception as e:
        print ("ERROREMAILMESSAGE: ",e)
        


@app.task
def patch_fields_premierug(s_file,mambu_user_email,entity,environment_name,file_name, updateClear):
    config = configparser.ConfigParser()
    config.read('secrets.ini')
    customFieldPatch = config.get('Credentials', 'PREMIERUGANDA_PROD_APIKEY_CUSTOMFIELDPATCH')
    premugurl = config.get('Credentials', 'PREMIERUGANDA_LIVE_URL')

    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/vnd.mambu.v2+json',
        "apiKey":"{}".format(customFieldPatch)
    }

    start_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    file = pickle.loads(s_file)
    emailaddress=mambu_user_email
    
    workbook = xlrd.open_workbook(file_contents=file)
    sheet = workbook.sheet_by_index(0)

    receipt_store = redis.StrictRedis(host='localhost', port=6379, db=1, decode_responses=True)
    receipt_store.config_get('databases')
    receipt_store.flushall()

    file_object=BytesIO() # create a file-like object
    report=xlsxwriter.Workbook(file_object)
    results_to_post=report.add_worksheet('Patch Results')
    results_to_post_count=0
    results_to_post.write_row(0,0,('ID','VALUE','FIELDSET ID','CUSTOM FIELD ID','RETURN CODE','RETURN STATUS','ERROR SOURCE'))

    receipts_count=0
    return_code=''
    return_status=''
    error_source=''
    TaskId=[]
    field_id=[]
    fieldset_id=[]
    params=[]


    for row in range(1, sheet.nrows):
        try:
            Id = sheet.cell(row, 0).value
        except ValueError:
            Id = sheet.cell(row, 0).value
        # else:
            # Id =  "0" + str(int(sheet.cell(row, 0).value))
            # zeros = len(sheet.cell(row, 0).value) - len(sheet.cell(row, 0).value.lstrip('0'))
            # print("NUMBER OF ZEROS: ",zeros)
            #
            # Id = str(int(sheet.cell(row, 0).value)).zfill(zeros + len(str(int(sheet.cell(row, 0).value))))

        try:
            value_id = sheet.cell(row, 1).value
        except ValueError:
            value_id = sheet.cell(row, 1).value

        try:
            customfield_id = sheet.cell(row, 2).value
        except ValueError:
            customfield_id = sheet.cell(row, 2).value

        try:
            fieldset_id_value = sheet.cell(row, 3).value
        except ValueError:
            fieldset_id_value = sheet.cell(row, 3).value

        # print('Id',Id)
        # print('value',value_id)
        # print('customfield_id',customfield_id)
        # print('fieldset_id',fieldset_id_value)

        TaskId = [Id]
        field_id = [customfield_id]
        fieldset_id = [fieldset_id_value]
        params = [value_id]

        if updateClear == 'CLEARFIELDS':
            payload = [
            {
                "op": "REMOVE",
                "path": f"/{fieldset_id[0]}/{field_id[0]}",
                "value": ""
            }
            ]
        else:
            payload = [
                {
                    "op": "ADD",
                    "path": f"/{fieldset_id[0]}/{field_id[0]}",
                    "value": params[0]
                }
            ]
            
        if entity is not None:
            url = premugurl + f"{entity}/{TaskId[0]}"
            print("PREMIERUG TASKSURL: ",url)
            try:
                res = requests.patch(url, json=payload, headers=headers)
                
                # Check if response is successful
                if res.status_code == 204:  # No Content - successful update
                    # print("PLATINUM UG RESPONSE: Success (No Content)")
                    return_code = 0
                    return_status = 'SUCCESS'
                    error_source = ''
                elif res.status_code >= 400:  # Error status codes
                    return_code = res.status_code
                    return_status = f'HTTP_ERROR_{res.status_code}'
                    error_source = res.text[:200] if res.text else 'No error details'
                else:
                    # Try to parse JSON response for other status codes
                    try:
                        response_json = res.json()
                        
                        if 'errors' in response_json and response_json['errors']:
                            error_info = response_json['errors'][0]
                            return_code = error_info.get('errorCode', 0)
                            return_status = error_info.get('errorReason', 'SUCCESS')
                            error_source = error_info.get('errorSource', '')
                        else:
                            return_code = 0
                            return_status = 'SUCCESS'
                            error_source = ''
                    except ValueError as json_err:
                        # Response is not JSON or empty
                        return_code = res.status_code
                        return_status = 'JSON_PARSE_ERROR'
                        error_source = f'Invalid JSON response: {str(json_err)}'

            except RequestException as err:
                return_code = 999
                return_status = 'REQUEST_ERROR'
                error_source = str(err)
            
            receipts_count+=1
            receipt_store.lpush(TaskId[0], json.dumps([params[0], fieldset_id[0], field_id[0], return_code, return_status, error_source]))

    # After processing all rows, collect all results from Redis and write to the report
    results_to_post_count = 0
    for key in receipt_store.scan_iter():
        dat = receipt_store.lrange(key, 0, -1)
        for row in dat:
            row_values = json.loads(row)
            # row_values = eval(row)
            redis_params = row_values[0] if len(row_values) > 0 else ''
            redis_fieldset = row_values[1] if len(row_values) > 1 else ''
            redis_field = row_values[2] if len(row_values) > 2 else ''
            redis_code = row_values[3] if len(row_values) > 3 else ''
            redis_status = row_values[4] if len(row_values) > 4 else ''
            redis_error = row_values[5] if len(row_values) > 5 else ''
            data = (key, redis_params, redis_fieldset, redis_field, redis_code, redis_status, redis_error)
            results_to_post_count += 1
            results_to_post.write_row(results_to_post_count, 0, data)
    report.close()          
    ##send email
    try:
        emailaddresses=['<EMAIL>']
        emailaddresses.append(emailaddress)
        
        email = EmailMessage('CustomField update report for '+entity, 'Please find attached report for '+entity +'\n Start time :'+start_time+'\n Finished: '+datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '<EMAIL>',emailaddresses)
        email.content_subtype = "html"
        email.attach('Results for '+file_name, file_object.getvalue(), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        emailsent=email.send()
    except Exception as e:
        print ("ERROREMAILMESSAGE: ",e)


@app.task
def patch_fields_platinumug(s_file,mambu_user_email,entity,environment_name,file_name,updateClear):
    env = Environment.objects.get(url=environment_name)
    # print("ENV",env)
    auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/vnd.mambu.v2+json',
        auth_user.apikey: auth_user.generatedkey
    }

    # live
    headers_live = {
        'Content-Type': 'application/json',
        'Accept': 'application/vnd.mambu.v2+json',
        "ApiKey": "{}".format(platinumUgandaKeyProduction)
    }

    # sandbox
    headers_sandbox = {
        'Content-Type': 'application/json',
        'Accept': 'application/vnd.mambu.v2+json',
        "ApiKey": "{}".format(platinumUgandaKeySandbox)
    }

    headers = headers_sandbox if environment_name.endswith('sandbox') else headers_live

    start_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    file = pickle.loads(s_file)
    emailaddress=mambu_user_email
    
    workbook = xlrd.open_workbook(file_contents=file)
    sheet = workbook.sheet_by_index(0)

    receipt_store = redis.StrictRedis(host='localhost', port=6379, db=1, decode_responses=True)
    receipt_store.config_get('databases')
    receipt_store.flushall()

    file_object=BytesIO() # create a file-like object
    report=xlsxwriter.Workbook(file_object)
    results_to_post=report.add_worksheet('Patch Results')
    results_to_post_count=0
    results_to_post.write_row(0,0,('ID','VALUE','FIELDSET ID','CUSTOM FIELD ID','RETURN CODE','RETURN STATUS','ERROR SOURCE'))

    receipts_count=0
    return_code=''
    return_status=''
    error_source=''
    TaskId=[]
    field_id=[]
    fieldset_id=[]
    params=[]


    for row in range(1, sheet.nrows):

        try:
            Id = sheet.cell(row, 0).value
        except ValueError:
            Id = sheet.cell(row, 0).value

        try:
            value_id = sheet.cell(row, 1).value
        except ValueError:
            value_id = sheet.cell(row, 1).value

        try:
            customfield_id = sheet.cell(row, 2).value
        except ValueError:
            customfield_id = sheet.cell(row, 2).value

        try:
            fieldset_id_value = sheet.cell(row, 3).value
        except ValueError:
            fieldset_id_value = sheet.cell(row, 3).value

        # print('platinumUG Id',Id)
        # print('platinumUG value',value_id)
        # print('platinumUG customfield_id',customfield_id)
        # print('platinumUG fieldset_id',fieldset_id_value)

        TaskId = [Id]
        field_id = [customfield_id]
        fieldset_id = [fieldset_id_value]
        params = [value_id]
        
        if updateClear == 'CLEARFIELDS':
            payload = [
            {
                "op": "REMOVE",
                "path": f"/{fieldset_id[0]}/{field_id[0]}",
                "value": ""
            }
            ]
        else:
            payload = [
                {
                    "op": "ADD",
                    "path": f"/{fieldset_id[0]}/{field_id[0]}",
                    "value": params[0]
                }
            ]

        print("PayloadCF: ", payload)

        if entity is not None:
            root_url = pclugurl_sandbox if environment_name.endswith('sandbox') else pclugurl

            url = root_url + f"{entity}/{TaskId[0]}"

            print("TASKSURL PLATUG: ",url)
            try:
                res = requests.patch(url, json=payload, headers=headers)
                
                # Check if response is successful
                if res.status_code == 204:  # No Content - successful update
                    print("PLATINUM UG RESPONSE: Success (No Content)")
                    return_code = 0
                    return_status = 'SUCCESS'
                    error_source = ''
                elif res.status_code >= 400:  # Error status codes
                    print(f"PLATINUM UG ERROR: Status {res.status_code}, Response: {res.text}")
                    return_code = res.status_code
                    return_status = f'HTTP_ERROR_{res.status_code}'
                    error_source = res.text[:200] if res.text else 'No error details'
                else:
                    # Try to parse JSON response for other status codes
                    try:
                        response_json = res.json()
                        print("PLATINUM UG RESPONSE", response_json)
                        
                        if 'errors' in response_json and response_json['errors']:
                            error_info = response_json['errors'][0]
                            return_code = error_info.get('errorCode', 0)
                            return_status = error_info.get('errorReason', 'SUCCESS')
                            error_source = error_info.get('errorSource', '')
                        else:
                            return_code = 0
                            return_status = 'SUCCESS'
                            error_source = ''
                    except ValueError as json_err:
                        # Response is not JSON or empty
                        print(f"PLATINUM UG JSON Parse Error: {json_err}, Response text: {res.text}")
                        return_code = res.status_code
                        return_status = 'JSON_PARSE_ERROR'
                        error_source = f'Invalid JSON response: {str(json_err)}'

            except RequestException as err:
                print(f"PLATINUM UG Request Error: {err}")
                return_code = 999
                return_status = 'REQUEST_ERROR'
                error_source = str(err)
            
            receipts_count+=1
            receipt_store.lpush(TaskId[0], json.dumps([params[0], fieldset_id[0], field_id[0], return_code, return_status, error_source]))

    # After processing all rows, collect all results from Redis and write to the report
    results_to_post_count = 0
    for key in receipt_store.scan_iter():
        dat = receipt_store.lrange(key, 0, -1)
        for row in dat:
            row_values = json.loads(row)
            # row_values = eval(row)
            redis_params = row_values[0] if len(row_values) > 0 else ''
            redis_fieldset = row_values[1] if len(row_values) > 1 else ''
            redis_field = row_values[2] if len(row_values) > 2 else ''
            redis_code = row_values[3] if len(row_values) > 3 else ''
            redis_status = row_values[4] if len(row_values) > 4 else ''
            redis_error = row_values[5] if len(row_values) > 5 else ''
            data = (key, redis_params, redis_fieldset, redis_field, redis_code, redis_status, redis_error)
            results_to_post_count += 1
            results_to_post.write_row(results_to_post_count, 0, data)
    report.close()

    # redis-cli --csv your-command > stdout.csv 2> stderr.txt
        
    ##send email
    try:
        emailaddresses=['<EMAIL>']
        emailaddresses.append(emailaddress)
        
        email = EmailMessage('CustomField update report for '+entity, 'Please find attached report for '+entity +'\n Start time :'+start_time+'\n Finished: '+datetime.now().strftime('%Y-%m-%d %H:%M:%S'), '<EMAIL>',emailaddresses)
        email.content_subtype = "html"
        email.attach('Results for '+file_name, file_object.getvalue(), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        emailsent=email.send()
    except Exception as e:
        print ("ERROREMAILMESSAGE: ",e)
