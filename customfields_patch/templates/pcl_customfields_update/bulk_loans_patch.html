{% extends "pcl_customfields_update/main_dashboard.html" %}
{% load bootstrap4 %}
{% load url_replace %}
{% load humanize %}
{% block content %}
{% load static %}
{% block style %}



    <div id="select_item">
      <div class="container" >
        <h2 style="padding:15px;">CustomFields Update</h2>
        <div class="dropdown">
          <button style="margin-left:20px;" type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown">
            Select item to update
          </button>
          <div class="dropdown-menu">
            <div class="dropdown-divider"></div>
            <a class="dropdown-item" href="{% url 'customfields_patch:update-client' %}">Clients</a>
            <div class="dropdown-divider"></div>
            <a class="dropdown-item" href="{% url 'customfields_patch:update-loans' %}">Loans</a>
            <div class="dropdown-divider"></div>
            <a class="dropdown-item" href="{% url 'customfields_patch:update-users' %}">Users</a>
            <div class="dropdown-divider"></div>
            <a class="dropdown-item" href="{% url 'customfields_patch:update-branches' %}">Branches</a>
            <div class="dropdown-divider"></div>
            <a class="dropdown-item" href="#">Tranactions</a>
            <div class="dropdown-divider"></div>
          </div>
      </div>
    </div>
    </div>
<link rel="stylesheet" href="{% static 'css/journals.css' %}"> {% endblock style %}
{% for message in messages %}
<div class="message">
    {{ message }}
    <a href="#" class="del-msg">&times;</a>
</div>
{% endfor %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card" style="height: 100%;
                                    margin: 2%;
                                    border: 1px solid #5ABF5A;
                                    padding: 49px;
                                    overflow: auto;">
                        <h2 style="font-size:20px;font-family: sans-serif;
                        font-size: 20px;text-align:center;">BULK LOANS UPDATE</h2>
                <div class="content">
                    <form method="POST" enctype="multipart/form-data"> {% csrf_token %}
                        <div class="panel-body">
                            {% if errors %}
                            <div class="panel panel-danger">
                                <div class="panel-heading">The file upload has generated the following errors:</div>
                                <ul class="list-group">
                                    {% for row in errors %}
                                    <li class="list-group-item">{{ row }}</li>
                                    {% endfor %}
                                </ul>
                            </div>

                            {% endif %}

                            {{ form }} {% csrf_token %}
                            <div class="text-left">
                                <button type="submit" style="font-size: 15px;
                                font-stretch: extra-expanded;
                                font-family: sans-serif;
                                background-color: #28a745;
                                width: 150px;
                                margin-top: 24px;
                                height: 30px;
                                border-radius: 10px;" name="add" class="btn btn-success btn-fill btn-wd">Upload Accounts</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>

    <link rel="stylesheet" href="https://ajax.aspnetcdn.com/ajax/jquery.ui/1.10.4/themes/smoothness/jquery-ui.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<script src="{% static 'js/messages.js' %}"></script>

{% endblock %}