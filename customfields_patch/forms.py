from django import forms

ENTITY_CHOICES=[
    ('-------','--------'),
    ('clients','CLIENTS'),
    ('loans','LOANS'),
    ('users','USERS'),
    ('transactions','TRANSACTIONS'),
    ('savings', 'DEPOSITS'),
    ('branches', 'BRANCHES'),

]

CLEAR_CHOICES=[
    ('UPDATE','UPDATE'),
    ('CLEARFIELDS','CLEARFIELDS'),
]

class patchclientForm(forms.Form):
    entity = forms.CharField(label='Select an entity to update', widget=forms.Select(choices=ENTITY_CHOICES))
    file = forms.FileField(label='Import Excel File From Your Machine')
class patchclientFormPlatug(forms.Form):
    entity = forms.CharField(label='Select an entity to update', widget=forms.Select(choices=ENTITY_CHOICES))
    updateClear = forms.CharField(label='Select an action', widget=forms.Select(choices=CLEAR_CHOICES))
    file = forms.FileField(label='Import Excel File From Your Machine')    

class patchloansForm(forms.Form):
    entity = forms.CharField(label='Select an entity to update', widget=forms.Select(choices=ENTITY_CHOICES))
    file = forms.FileField(label='Import Excel File From Your Machine')

class patchusersForm(forms.Form):
    file = forms.FileField(label='Import Excel File From Your Machine')