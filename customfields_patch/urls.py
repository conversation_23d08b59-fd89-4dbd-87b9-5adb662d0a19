from django.urls import path
from . import views

app_name = 'customfields_patch'
urlpatterns = [
    path('role_not_allowed/', views.role_not_allowed, name='rolenotallowedplatkenya'),
    path('platke_customfield/$', views.customfield_index, name="platke_custom_index"),
    path('premierke_customfield/$', views.premiercustomfield_index, name="premierke_custom_index"),
    path('momentum_customfield/$', views.momentumcustomfield_index, name="momentum_custom_index"),
    path('premug_customfield/$', views.premierugcustomfield_index, name="premug_custom_index"),
    path('platug_customfield/$', views.platinumugcustomfield_index, name="platug_custom_index"),
    path('update_client_refined',views.update_client_new,name='update-client-refined'),
    path('premierupdate_client',views.premier_update_client,name='premier_update-client'),
    path('momentumupdate_client',views.momentum_update_client,name='momentum_update-client'),
    path('premierugupdate_client',views.premierug_update_client,name='premierug_update-client'),
    path('platinumugupdate_client',views.platinumug_update_client,name='platinumug_update-client'),
    path('progress/', views.progress, name="progress"),

]