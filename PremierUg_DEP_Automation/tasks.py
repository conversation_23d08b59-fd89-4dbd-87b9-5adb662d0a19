from __future__ import absolute_import, unicode_literals

import configparser

from newugandalms.celery import app
import requests


def getClientSavings(clientID,accountID,headers,urls):
    geturl = urls + "clients/{0}/savings?offset=0&limit=1000".format(clientID)
    print("urls",geturl)
    get_client_details = requests.get(geturl,headers=headers)
    # print("savingsDetail",get_client_details.json())

    for obj in get_client_details.json():
        if obj['accountState'] in ["ACTIVE","PENDING_APPROVAL","APPROVED"] and obj['id']!=accountID :
            return False
        else:
            return True
    return None


    # return get_client_details.json()

@app.task
def updateClientReference(clientID, accountID,environment,clientReference):
    urls = "https://premieruganda.sandbox.mambu.com/api/" if environment.endswith(
        'sandbox') else 'https://premieruganda.mambu.com/api/'

    config = configparser.ConfigParser()
    config.read('secrets.ini')
    lgfCreation = config.get('Credentials', 'PREMIERUGANDA_PROD_APIKEY_LGFCREATION')
    headers = {"ApiKey": "{}".format(lgfCreation)}

    if clientReference:
        payload = {
            'type': "withdraw",
            'notes': "double creation",
        }
        geturl = urls + "savings/{0}/transactions".format(accountID)
        lgf_close_details = requests.post(geturl, json=payload, headers=headers)
        print("lgf_close_details",lgf_close_details.json())

    else:
        # check client savings accounts
        savingsExist = getClientSavings(clientID,accountID,headers,urls)
        print("savingsExist",savingsExist)
        if savingsExist:
            payload = {
                "customInformation": [
                    {
                    "customFieldID" : "customer_reference",
                    "value" : str(accountID)
                    }]
                }


            postfield = urls + "clients/{0}/customInformation".format(clientID)
            print('postfield',postfield)
            fieldpost = requests.patch(postfield, json=payload,headers=headers)
            data = fieldpost.json()

            print("PatchResults",data)


@app.task
def ClientCreation(clientID,environment):
    urls = "https://premieruganda.sandbox.mambu.com/api/" if environment.endswith(
        'sandbox') else 'https://premieruganda.mambu.com/api/'
    config = configparser.ConfigParser()
    config.read('secrets.ini')
    lgfCreation = config.get('Credentials', 'PREMIERUGANDA_PROD_APIKEY_LGFCREATION')
    headers = {"ApiKey": "{}".format(lgfCreation)}



    geturl = urls + "clients/{0}/".format(clientID)
    get_client_details = requests.get(geturl,headers=headers)

    print("clientDetails",get_client_details.json())

    # product key for the bad debt account
    productTypeKey="8aaaeaff45ba73920145bc2370cf1b43"

    bad_debt_payload = {
        "savingsAccount": {
            "accountHolderKey": get_client_details.json()['encodedKey'],
            "accountHolderType": "CLIENT",
            "productTypeKey": productTypeKey,
            "accountType": "REGULAR_SAVINGS",
            "accountState": "APPROVED",
            "name": "LGF"
        }
    }
    geturl = urls + "clients/{0}/savings".format(clientID)
    create_bad_debt_details = requests.post(geturl, json=bad_debt_payload, headers=headers)
    print("lgf",create_bad_debt_details.json())
    # return create_bad_debt_details.json()

@app.task
def clearClientReferenceCF(clientID,environment,customerReference,accountID):

    if customerReference == accountID:
        urls = "https://premieruganda.sandbox.mambu.com/api/" if environment.endswith(
            'sandbox') else 'https://premieruganda.mambu.com/api/'

        config = configparser.ConfigParser()
        config.read('secrets.ini')
        lgfCreation = config.get('Credentials', 'PREMIERUGANDA_PROD_APIKEY_LGFCREATION')
        headers = {"ApiKey": "{}".format(lgfCreation)}

        geturl = urls + "clients/{0}/custominformation/customer_reference".format(clientID)
        get_client_details = requests.delete(geturl,headers=headers)
        print("clearFields",get_client_details.json())
    else:
        pass