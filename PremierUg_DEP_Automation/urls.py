from django.urls import path
from . import views
app_name = 'PremierUg_DEP_Automation'
urlpatterns = [
    # first demand notice
    # path('firstDemandNoticeActivity/$', views.firstDemandNoticeActivity.as_view(), name="demand-activity"),
    path('firstDemandNoticeActivity/$', views.first_demand_letter, name='demand_letter'),

    # second demand notice
    # path('secondDemandNoticeActivity/$', views.secondDemandNoticeActivity.as_view(), name="demand-activity"),
    path('secondDemandNoticeActivity/$', views.second_demand_letter, name='demand_letter'),

    # loan recall
    # path('loanRecallActivity/$', views.loanRecallActivity.as_view(), name="loan-recall"),
    path('loanRecallActivity/$', views.loan_recall_notice, name="loan_recall_notice"),

    # guarantor one notice
    # path('guarantorOneNoticeActivity/$', views.guarantorOneNoticeActivity.as_view(), name="guarantor_one_notice"),
    path('guarantorOneNoticeActivity/$', views.guarantor_one_notice, name='guarantor_notice'),

    # guarantor two notice
    # path('guarantorTwoNoticeActivity/$', views.guarantorTwoNoticeActivity.as_view(), name="guarantor_two_notice"),
    path('guarantorTwoNoticeActivity/$', views.guarantor_two_notice, name='guarantor_two_notice'),

    # notice of default
    # path('noticeDefaultActivity/$', views.noticeDefaultActivity.as_view(), name="notice-default"),
    path('noticeDefaultActivity/$', views.notice_of_default, name="notice_of_default"),
    # path('test_dep',views.guarantor_one_notice)

    # client Reference
    path('clientCreation/', views.clientCreation),

    path('clientReference/', views.clientReference),

    # clear cf
    path('clearCF/', views.clearClientReference),

    # Notice of Default
    path('noticeDefaultV2/', views.noticeDefaultV2, name="noticeDefaultV2"),

]
