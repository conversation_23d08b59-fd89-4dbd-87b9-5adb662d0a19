from django.shortcuts import render
# from django.http import HttpResponse
from django.shortcuts import redirect, HttpResponse
from django.core.mail import EmailMessage

from django.template.loader import render_to_string
from django.conf import settings
from django.utils import dateformat
from django.utils.dateformat import DateFormat
from weasyprint import HTML, CSS
import base64
import json
import requests
from datetime import date
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from mambudata.models import User_Credential
from mambu.models import Credential, Environment
from mambu.utils import Mambu
from .models import Task_results
from rest_framework.authentication import BasicAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from datetime import datetime, timedelta
import inflect
from .tasks import updateClientReference,ClientCreation,clearClientReferenceCF


def get_client_email(clientID,urls,headers):
    payload ={"fullDetails":"True"}
    geturl = urls + "clients/{0}".format(clientID)
    get_client_details = requests.get(geturl, json=payload,headers=headers)
    return get_client_details.json()

def get_client_mobile(clientID,urls,headers):
    geturl = urls + "clients/{0}".format(clientID)
    get_client_details = requests.get(geturl,headers=headers)
    return get_client_details.json()

def get_custom_field(fieldID, entity):
    for custom_field in entity:
        if custom_field["customFieldID"] == fieldID:
            # print('PRINT',custom_field["value"])
            return custom_field["value"]
    return None


# FIRST DEMAND LETTER 8 DAYS IN ARREARS
# class firstDemandNoticeActivity(APIView):
#     authentication_classes = (BasicAuthentication,)
#     permission_classes = (IsAuthenticated,)
#
#     def post(self, request):
#         first_demand_letter(request.data)
#         return Response({"message": "request received"}, status=status.HTTP_200_OK)
@csrf_exempt
def first_demand_letter(request):
    data=json.loads(request.body)
    env = Environment.objects.get(url=data['environment'])
    urls = "https://premieruganda.sandbox.mambu.com/api/" if data['environment'].endswith(
        'sandbox') else 'https://premieruganda.mambu.com/api/'
    auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
    headers = {
        "ApiKey": "Dstrgrhnx4ue8CXdHxlDI3W6fbxsmjmm"
    }
    geturl = urls + "loans/{0}".format(data['accountId'])
    getdetails = requests.get(geturl, headers=headers)
    getloan_response = getdetails.json()

    # GET LOAN ASSIGNED BRANCH KEY
    assigned_branchKey = getloan_response.get('assignedBranchKey','')

    # Head office, Govt payroll, Callcenter and Trade Finance branches to be excluded
    exclude_branches = ["8a1a33434f94875d014fb1515eb13843","8a9387d0623cd66401623d171dfa0e0d",
                        "8a9386e7750d479a01751b9116631230","8a9387b565b48c4a0165c39074f1072f"]

    if assigned_branchKey not in exclude_branches:
        # GET CLIENT EMAIL ADDRESS
        mobile_details = get_client_mobile(getloan_response.get('accountHolderKey',''),urls,headers)
        client_details = get_client_email(getloan_response.get('accountHolderKey',''),urls,headers)

        address_street = ''
        city = ''
        country = ''

        try:
            for detail in client_details['addresses']:
                address_street = detail.get('line1','')
                city = detail.get('city','')
                country = detail.get('country','')
        except KeyError:
            pass

        # # GET CURRENT TIME AND DATE
        now = datetime.now()
        # date_time = now.strftime("%d %B %Y")
        date_time = dateformat.format(now, 'jS F Y')

        # GET LOAN ACTIVATION DATE
        activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'], "%Y-%m-%dT%H:%M:%S+%f")
        # activation_date = datetime.strftime(activation,'%d %B %Y')
        activation_date = dateformat.format(activation, 'jS F Y')

        # GET TOTAL BALANCE
        total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(getloan_response['feesDue']) + float(getloan_response['penaltyDue'])

        due_date = date.today() + timedelta(days=7)
        due_date_final = due_date.strftime('%d %B %Y')
        due_date_final = dateformat.format(due_date, 'jS F Y')

        context = {
            "Client_name": data["fullName"],
            "street_add_1": address_street,
            "city": city,
            "country": country,
            "Client_phone_number": mobile_details.get('mobilePhone1',''),
            "activation_date": activation_date,
            "total_due": '{:,}'.format(round(total_due)),
            "due_date_final": due_date_final,
            "date": date_time,
        }
        rendered_html = render_to_string('DEP_automation/demand_notice.html', {'context': context}).encode(
            encoding="UTF-8")
        pdf_file = HTML(string=rendered_html,).write_pdf(
            stylesheets=[CSS(settings.STATIC_ROOT + 'css/premierug_dep/demand_notice.css'),
                         CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                         CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                         ])
        response = HttpResponse(pdf_file, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="First Demand Notice_{}.pdf"'.format(data['accountId'])

        # attachDocument working
        encoded_string = base64.b64encode(response.content).decode('utf-8')
        attachdoc = {
            "document": {
                "documentHolderKey": getdetails.json()['encodedKey'],
                "documentHolderType": "LOAN_ACCOUNT",
                "name": "First Demand Notice_{}".format(data['accountId']),
                "type": "PDF"
            },
            "documentContent": encoded_string
        }
        doc_url = urls + "loans/{0}/documents/".format(data['accountId'])
        details = requests.post(doc_url, json=attachdoc, headers=headers)

        # CREATING TASKS TO  BRANCH MANAGERS AND SUPERVISORS
        users_payload = {
            "branchID": assigned_branchKey,
            "offset": "0",
            "limit": "1000",
            "fullDetails":"True"

        }
        geturl = urls + "users/"
        getdetails = requests.get(geturl, json=users_payload,headers=headers)
        for obj in getdetails.json():
            # branch manager and supervisor
            try:
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a350e9745de50db0145f94719e50e68" or obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a2a8ba24e6921ac014e787a4eba127a":
                    task_payload = {
                        "title": 'First Demand Notice',
                        "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                        "description": "Fisrt demand notice attached for {account_id}".format(account_id=data["accountId"]),
                        "status": "OPEN",
                        "clientID": getloan_response['accountHolderKey'],
                        "username": obj['username']
                    }

                    taskurl = urls + "tasks"
                    taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                    glload = taskdetails.json()
                    Task_results(username=obj['username'],notice_type="First Demand Notice", task_status=glload).save()
            except KeyError:
                pass
        return response
    else:
        pass


# SECOND DEMAND LETTER 16 DAYS IN ARREARS
# class secondDemandNoticeActivity(APIView):
#     authentication_classes = (BasicAuthentication,)
#     permission_classes = (IsAuthenticated,)
#
#     def post(self, request):
#         second_demand_letter(request.data)
#         return Response({"message": "request sent"}, status=status.HTTP_200_OK)

@csrf_exempt
def second_demand_letter(request):
    data=json.loads(request.body)
    env = Environment.objects.get(url=data['environment'])
    urls = "https://premieruganda.sandbox.mambu.com/api/" if data['environment'].endswith(
        'sandbox') else 'https://premieruganda.mambu.com/api/'
    auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
    headers = {
        "ApiKey": "Dstrgrhnx4ue8CXdHxlDI3W6fbxsmjmm"
    }
    geturl = urls + "loans/{0}".format(data['accountId'])
    getdetails = requests.get(geturl, headers=headers)
    getloan_response = getdetails.json()

    # GET LOAN ASSIGNED BRANCH KEY
    assigned_branchKey = getloan_response.get('assignedBranchKey','')

    # Head office, Govt payroll, Callcenter and Trade Finance branches to be excluded
    exclude_branches = ["8a1a33434f94875d014fb1515eb13843","8a9387d0623cd66401623d171dfa0e0d","8a9386e7750d479a01751b9116631230","8a9387b565b48c4a0165c39074f1072f"]

    if assigned_branchKey not in exclude_branches:
        # GET CLIENT EMAIL ADDRESS
        mobile_details = get_client_mobile(getloan_response.get('accountHolderKey',''),urls,headers)
        client_details = get_client_email(getloan_response.get('accountHolderKey',''),urls,headers)

        address_street = ''
        city = ''
        country = ''
        try:
            for detail in client_details['addresses']:
                address_street = detail.get('line1','')
                city = detail.get('city','')
                country = detail.get('country','')
        except KeyError:
            pass

        # # GET CURRENT TIME AND DATE
        now = datetime.now()
        # date_time = now.strftime("%d %B %Y")
        date_time = dateformat.format(now, 'jS F Y')


        # GET LOAN ACTIVATION DATE
        activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'], "%Y-%m-%dT%H:%M:%S+%f")
        activation_date = dateformat.format(activation, 'jS F Y')

        # activation_date = datetime.strftime(activation,'%d %B %Y')

        # GET TOTAL BALANCE
        total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(getloan_response['feesDue']) + float(getloan_response['penaltyDue'])

        due_date = date.today() + timedelta(days=7)
        # due_date_final = due_date.strftime('%d %B %Y')
        due_date_final = dateformat.format(due_date, 'jS F Y')


        context = {
            "Client_name": data["fullName"],
            "street_add_1": address_street,
            "city": city,
            "country": country,
            "Client_phone_number": mobile_details.get('mobilePhone1',''),
            "activation_date": activation_date,
            "total_due": '{:,}'.format(round(total_due)),
            "due_date_final": due_date_final,
            "date": date_time,
        }
        rendered_html = render_to_string('DEP_automation/second_demand_notice.html', {'context': context}).encode(
            encoding="UTF-8")
        pdf_file = HTML(string=rendered_html,).write_pdf(
            stylesheets=[CSS(settings.STATIC_ROOT + 'css/premierug_dep/demand_notice.css'),
                         CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                         CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                         ])
        response = HttpResponse(pdf_file, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="Second Demand Notice_{}.pdf"'.format(data['accountId'])

        # attachDocument working
        encoded_string = base64.b64encode(response.content).decode('utf-8')
        attachdoc = {
            "document": {
                "documentHolderKey": getdetails.json()['encodedKey'],
                "documentHolderType": "LOAN_ACCOUNT",
                "name": "Second Demand Notice_{}".format(data['accountId']),
                "type": "PDF"
            },
            "documentContent": encoded_string
        }
        doc_url = urls + "loans/{0}/documents/".format(data['accountId'])
        details = requests.post(doc_url, json=attachdoc, headers=headers)


        # CREATING TASKS TO BRANCH MANAGERS AND SUPERVISORS
        users_payload = {
            "branchID": assigned_branchKey,
            "offset": "0",
            "limit": "1000",
            "fullDetails": "True"

        }
        geturl = urls + "users/"
        getdetails = requests.get(geturl, json=users_payload,headers=headers)
        for obj in getdetails.json():
            try:
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a350e9745de50db0145f94719e50e68" or obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a2a8ba24e6921ac014e787a4eba127a":
                    # print('obj',obj['username'])
                    task_payload = {
                        "title": 'Second Demand Notice',
                        "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                        "description": "Second demand notice attached for {account_id}".format(account_id=data["accountId"]),
                        "status": "OPEN",
                        "clientID": getloan_response['accountHolderKey'],
                        "username": obj['username']
                    }

                    taskurl = urls + "tasks"
                    taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                    glload = taskdetails.json()
                    Task_results(username=obj['username'],notice_type="Second Demand Notice", task_status=glload).save()
            except KeyError:
                pass
        return response
    else:
        pass


# loan recall
# class loanRecallActivity(APIView):
#     authentication_classes = (BasicAuthentication,)
#     permission_classes = (IsAuthenticated,)
#     def post(self, request):
#         loan_recall_notice(request.data)
#         return Response({"message": "request received"}, status=status.HTTP_200_OK)
@csrf_exempt
def loan_recall_notice(request):
    data=json.loads(request.body)

    env = Environment.objects.get(url=data['environment'])
    urls = "https://premieruganda.sandbox.mambu.com/api/" if data['environment'].endswith(
        'sandbox') else 'https://premieruganda.mambu.com/api/'
    auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
    headers = {
        "ApiKey": "Dstrgrhnx4ue8CXdHxlDI3W6fbxsmjmm"
    }
    geturl = urls + "loans/{0}".format(data['accountId'])
    getdetails = requests.get(geturl, headers=headers)
    getloan_response = getdetails.json()

    # GET LOAN ASSIGNED BRANCH KEY
    assigned_branchKey = getloan_response.get('assignedBranchKey','')

    # Head office, Govt payroll, Callcenter and Trade Finance branches to be excluded
    exclude_branches = ["8a1a33434f94875d014fb1515eb13843","8a9387d0623cd66401623d171dfa0e0d","8a9386e7750d479a01751b9116631230","8a9387b565b48c4a0165c39074f1072f"]

    if assigned_branchKey not in exclude_branches:
        # GET CLIENT EMAIL ADDRESS
        mobile_details = get_client_mobile(getloan_response.get('accountHolderKey',''),urls,headers)
        client_details = get_client_email(getloan_response.get('accountHolderKey',''),urls,headers)

        address_street = ''
        city = ''
        country = ''
        try:
            for detail in client_details['addresses']:
                address_street = detail.get('line1','')
                city = detail.get('city','')
                country = detail.get('country','')
        except KeyError:
            pass

        # # GET CURRENT TIME AND DATE
        now = datetime.now()
        # date_time = now.strftime("%d %B %Y")
        date_time = dateformat.format(now, 'jS F Y')


        # GET LOAN ACTIVATION DATE
        activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'], "%Y-%m-%dT%H:%M:%S+%f")
        activation_date = dateformat.format(activation, 'jS F Y')

        # activation_date = datetime.strftime(activation,'%d %B %Y')

        # DAYS IN ARREARS
        days_in_arrears = (datetime.today() - datetime.strptime(getloan_response["lastSetToArrearsDate"], "%Y-%m-%dT%H:%M:%S+%f")).days

        # GET TOTAL BALANCE
        total_balance = float(getloan_response['principalBalance']) + float(getloan_response['interestBalance']) + float(getloan_response['feesBalance']) +float(getloan_response['accruedInterest'])
        due_date = date.today() + timedelta(days=7)
        due_date_final = due_date.strftime('%d %B %Y')

        context = {
            "Client_name": data["fullName"],
            "street_add_1": address_street,
            "city": city,
            "country": country,
            "days_in_arrears": days_in_arrears,
            "Client_phone_number": mobile_details.get('mobilePhone1',''),
            "activation_date": activation_date,
            "total_balance": '{:,}'.format(round(total_balance)),
            "date": date_time,
        }
        rendered_html = render_to_string('DEP_automation/loan_recall_notice.html', {'context': context}).encode(
            encoding="UTF-8")
        pdf_file = HTML(string=rendered_html,).write_pdf(
            stylesheets=[CSS(settings.STATIC_ROOT + 'css/premierug_dep/loan_recall_notice.css'),
                         CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                         CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                         ])
        response = HttpResponse(pdf_file, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="Loan Recall_{}.pdf"'.format(data['accountId'])

        # attachDocument working
        encoded_string = base64.b64encode(response.content).decode('utf-8')
        attachdoc = {
            "document": {
                "documentHolderKey": getdetails.json()['encodedKey'],
                "documentHolderType": "LOAN_ACCOUNT",
                "name": "Loan Recall_{}".format(data['accountId']),
                "type": "PDF"
            },
            "documentContent": encoded_string
        }
        doc_url = urls + "loans/{0}/documents/".format(data['accountId'])
        details = requests.post(doc_url, json=attachdoc, headers=headers)

        # CREATING TASKS TO BRANCH MANAGERS AND SUPERVISORS
        users_payload = {
            "branchID": assigned_branchKey,
            "offset": "0",
            "limit": "1000",
            "fullDetails": "True"

        }
        geturl = urls + "users/"
        getdetails = requests.get(geturl, json=users_payload,headers=headers)
        for obj in getdetails.json():
            try:
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a350e9745de50db0145f94719e50e68" or obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a2a8ba24e6921ac014e787a4eba127a":
                    # print('obj',obj['username'])
                    task_payload = {
                        "title": 'Loan Recall',
                        "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                        "description": "Loan recall attached for {account_id}".format(account_id=data["accountId"]),
                        "status": "OPEN",
                        "clientID": getloan_response['accountHolderKey'],
                        "username": obj['username']
                    }

                    taskurl = urls + "tasks"
                    taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                    glload = taskdetails.json()
                    Task_results(username=obj['username'],notice_type="Loan recall", task_status=glload).save()
            except KeyError:
                pass
        return response
    else:
        pass

# guarantor one notice
# class guarantorOneNoticeActivity(APIView):
#     authentication_classes = (BasicAuthentication,)
#     permission_classes = (IsAuthenticated,)
#     def post(self, request):
#         guarantor_one_notice(request.data)
#         return Response({"message": "request Sent"}, status=status.HTTP_200_OK)

@csrf_exempt
def guarantor_one_notice(request):
    data=json.loads(request.body)

    env = Environment.objects.get(url=data['environment'])
    urls = "https://premieruganda.sandbox.mambu.com/api/" if data['environment'].endswith(
        'sandbox') else 'https://premieruganda.mambu.com/api/'
    auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
    headers = {
        "ApiKey": "Dstrgrhnx4ue8CXdHxlDI3W6fbxsmjmm"
    }
    geturl = urls + "loans/{0}".format(data['accountId'])
    getdetails = requests.get(geturl, headers=headers)
    getloan_response = getdetails.json()

    # GET LOAN ASSIGNED BRANCH KEY
    assigned_branchKey = getloan_response.get('assignedBranchKey','')

    # Head office, Govt payroll, Callcenter and Trade Finance branches to be excluded
    exclude_branches = ["8a1a33434f94875d014fb1515eb13843","8a9387d0623cd66401623d171dfa0e0d",
                        "8a9386e7750d479a01751b9116631230","8a9387b565b48c4a0165c39074f1072f"]

    if assigned_branchKey not in exclude_branches:
        # GET CLIENT EMAIL ADDRESS
        mobile_details = get_client_mobile(getloan_response.get('accountHolderKey',''),urls,headers)
        client_details = get_client_email(getloan_response.get('accountHolderKey',''),urls,headers)

        address_street = ''
        city = ''
        country = ''
        try:
            for detail in client_details['addresses']:
                address_street = detail.get('line1','')
                city = detail.get('city','')
                country = detail.get('country','')
        except KeyError:
            pass

        # # GET CURRENT TIME AND DATE
        now = datetime.now()
        # date_time = now.strftime("%d %B %Y")
        date_time = dateformat.format(now, 'jS F Y')
        year = dateformat.format(now, 'Y')



        # GET LOAN ACTIVATION DATE
        activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'], "%Y-%m-%dT%H:%M:%S+%f")
        # activation_date = datetime.strftime(activation,'%d %B %Y')
        activation_date = dateformat.format(activation, 'jS F Y')


        # DAYS IN ARREARS
        days_in_arrears = (datetime.today() - datetime.strptime(getloan_response["lastSetToArrearsDate"], "%Y-%m-%dT%H:%M:%S+%f")).days

        # GET TOTAL BALANCE
        total_balance = float(getloan_response['principalBalance']) + float(getloan_response['interestBalance']) + float(getloan_response['feesBalance']) +float(getloan_response['accruedInterest'])


        # GET LOAN AMOUNT
        loan_amount = float(getloan_response['loanAmount'])

        # Guarantor name
        get_url = urls + "loans/{0}/custominformation/{1}".format(data["accountId"], 'GN01')
        get_guarantor_details = requests.get(get_url, headers=headers)
        guarantor_one_name = get_custom_field("GN01", get_guarantor_details.json())

        context = {
            "Client_name": data["fullName"],
            "street_add_1": address_street,
            "city": city,
            "country": country,
            "days_in_arrears": days_in_arrears,
            "Client_phone_number": mobile_details.get('mobilePhone1',''),
            "activation_date": activation_date,
            "total_balance": '{:,}'.format(round(total_balance)),
            "loan_amount": '{:,}'.format(round(loan_amount)),
            "date": date_time,
            "guarantor_one":guarantor_one_name,
            "year":year
        }
        rendered_html = render_to_string('DEP_automation/guarantor_one_notice.html', {'context': context}).encode(
            encoding="UTF-8")
        pdf_file = HTML(string=rendered_html, ).write_pdf(
            stylesheets=[CSS(settings.STATIC_ROOT + 'css/premierug_dep/guarantor_notice.css'),
                         CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                         CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                         ])
        response = HttpResponse(pdf_file, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="Guarantor One Notice_{}.pdf"'.format(data['accountId'])

        # attachDocument working
        encoded_string = base64.b64encode(response.content).decode('utf-8')
        attachdoc = {
            "document": {
                "documentHolderKey": getdetails.json()['encodedKey'],
                "documentHolderType": "LOAN_ACCOUNT",
                "name": "Guarantor One Notice_{}".format(data['accountId']),
                "type": "PDF"
            },
            "documentContent": encoded_string
        }
        doc_url = urls + "loans/{0}/documents/".format(data['accountId'])
        details = requests.post(doc_url, json=attachdoc, headers=headers)

        # CREATING TASKS TO BRANCH MANAGERS AND SUPERVISORS
        users_payload = {
            "branchID": assigned_branchKey,
            "offset": "0",
            "limit": "1000",
            "fullDetails": "True"

        }
        geturl = urls + "users/"
        getdetails = requests.get(geturl, json=users_payload,headers=headers)
        for obj in getdetails.json():
            try:
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a350e9745de50db0145f94719e50e68" or obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a2a8ba24e6921ac014e787a4eba127a":
                    # print('obj',obj['username'])
                    task_payload = {
                        "title": 'Guarantor one notice',
                        "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                        "description": "Guarantor one notice attached for {account_id}".format(account_id=data["accountId"]),
                        "status": "OPEN",
                        "clientID": getloan_response['accountHolderKey'],
                        "username": obj['username']
                    }
                    taskurl = urls + "tasks"
                    taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                    glload = taskdetails.json()
                    Task_results(username=obj['username'],notice_type="Guarantor one notice", task_status=glload).save()
            except KeyError:
                pass
        return response
    else:
        pass


# guarantor two notice
# class guarantorTwoNoticeActivity(APIView):
#     authentication_classes = (BasicAuthentication,)
#     permission_classes = (IsAuthenticated,)
#     def post(self, request):
#         guarantor_two_notice(request.data)
#         return Response({"message": "request sent"}, status=status.HTTP_200_OK)

@csrf_exempt
def guarantor_two_notice(request):
    data=json.loads(request.body)
    env = Environment.objects.get(url=data['environment'])
    urls = "https://premieruganda.sandbox.mambu.com/api/" if data['environment'].endswith(
        'sandbox') else 'https://premieruganda.mambu.com/api/'
    auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
    headers = {
        "ApiKey": "Dstrgrhnx4ue8CXdHxlDI3W6fbxsmjmm"
    }
    geturl = urls + "loans/{0}".format(data['accountId'])
    getdetails = requests.get(geturl, headers=headers)
    getloan_response = getdetails.json()

    # GET LOAN ASSIGNED BRANCH KEY
    assigned_branchKey = getloan_response.get('assignedBranchKey','')

    # Head office, Govt payroll, Callcenter and Trade Finance branches to be excluded
    exclude_branches = ["8a1a33434f94875d014fb1515eb13843","8a9387d0623cd66401623d171dfa0e0d",
                        "8a9386e7750d479a01751b9116631230","8a9387b565b48c4a0165c39074f1072f"]

    if assigned_branchKey not in exclude_branches:
        # GET CLIENT EMAIL ADDRESS
        mobile_details = get_client_mobile(getloan_response.get('accountHolderKey',''),urls,headers)
        client_details = get_client_email(getloan_response.get('accountHolderKey',''),urls,headers)

        address_street = ''
        city = ''
        country = ''
        try:
            for detail in client_details['addresses']:
                address_street = detail.get('line1','')
                city = detail.get('city','')
                country = detail.get('country','')
        except KeyError:
            pass
        # # GET CURRENT TIME AND DATE
        now = datetime.now()
        date_time = dateformat.format(now, 'jS F Y')
        year = dateformat.format(now, 'Y')


        # GET LOAN ACTIVATION DATE
        activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'], "%Y-%m-%dT%H:%M:%S+%f")
        # activation_date = datetime.strftime(activation,'%d %B %Y')
        activation_date = dateformat.format(activation, 'jS F Y')


        # DAYS IN ARREARS
        days_in_arrears = (datetime.today() - datetime.strptime(getloan_response["lastSetToArrearsDate"], "%Y-%m-%dT%H:%M:%S+%f")).days

        # GET TOTAL BALANCE
        total_balance = float(getloan_response['principalBalance']) + float(getloan_response['interestBalance']) + float(getloan_response['feesBalance']) +float(getloan_response['accruedInterest'])
        due_date = date.today() + timedelta(days=7)
        # due_date_final = due_date.strftime('%d %B %Y')

        # GET LOAN AMOUNT
        loan_amount = float(getloan_response['loanAmount'])

        # Guarantor name
        get_url = urls + "loans/{0}/custominformation/{1}".format(data["accountId"], 'GN02')
        get_guarantor_details = requests.get(get_url, headers=headers)
        guarantor_two_name = get_custom_field("GN02", get_guarantor_details.json())

        context = {
            "Client_name": data["fullName"],
            "street_add_1": address_street,
            "city": city,
            "country": country,
            "days_in_arrears": days_in_arrears,
            "Client_phone_number": mobile_details.get('mobilePhone1',''),
            "activation_date": activation_date,
            "total_balance": '{:,}'.format(round(total_balance)),
            "loan_amount": '{:,}'.format(round(loan_amount)),
            "date": date_time,
            "guarantor_two":guarantor_two_name,
            "year":year
        }
        rendered_html = render_to_string('DEP_automation/guarantor_two_notice.html', {'context': context}).encode(
            encoding="UTF-8")
        pdf_file = HTML(string=rendered_html, ).write_pdf(
            stylesheets=[CSS(settings.STATIC_ROOT + 'css/premierug_dep/guarantor_notice.css'),
                         CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                         CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                         ])
        response = HttpResponse(pdf_file, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="Guarantor Two Notice_{}.pdf"'.format(data['accountId'])

        # attachDocument working
        encoded_string = base64.b64encode(response.content).decode('utf-8')
        attachdoc = {
            "document": {
                "documentHolderKey": getdetails.json()['encodedKey'],
                "documentHolderType": "LOAN_ACCOUNT",
                "name": "Guarantor Two Notice_{}".format(data['accountId']),
                "type": "PDF"
            },
            "documentContent": encoded_string
        }
        doc_url = urls + "loans/{0}/documents/".format(data['accountId'])
        details = requests.post(doc_url, json=attachdoc, headers=headers)

        # CREATING TASKS TO BRANCH MANAGERS AND SUPERVISORS
        users_payload = {
            "branchID": assigned_branchKey,
            "offset": "0",
            "limit": "1000",
            "fullDetails": "True"

        }
        geturl = urls + "users/"
        getdetails = requests.get(geturl, json=users_payload,headers=headers)
        for obj in getdetails.json():
            try:
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a350e9745de50db0145f94719e50e68" or obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a2a8ba24e6921ac014e787a4eba127a":
                    # print('obj',obj['username'])
                    task_payload = {
                        "title": 'Guarantor two notice',
                        "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                        "description": "Guarantor two notice attached for {account_id}".format(account_id=data["accountId"]),
                        "status": "OPEN",
                        "clientID": getloan_response['accountHolderKey'],
                        "username": obj['username']
                    }

                    taskurl = urls + "tasks"
                    taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                    glload = taskdetails.json()
                    Task_results(username=obj['username'], notice_type="Guarantor two notice", task_status=glload).save()
            except KeyError:
                pass
        return response
    else:
        pass


# notice of default
# class noticeDefaultActivity(APIView):
#     authentication_classes = (BasicAuthentication,)
#     permission_classes = (IsAuthenticated,)
#     def post(self, request):
#         notice_of_default(request.data)
#         return Response({"message": "request received"}, status=status.HTTP_200_OK)

@csrf_exempt
def notice_of_default(request):
    data=json.loads(request.body)
    env = Environment.objects.get(url=data['environment'])
    urls = "https://premieruganda.sandbox.mambu.com/api/" if data['environment'].endswith(
        'sandbox') else 'https://premieruganda.mambu.com/api/'
    auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
    headers = {
        "ApiKey": "Dstrgrhnx4ue8CXdHxlDI3W6fbxsmjmm"
    }
    geturl = urls + "loans/{0}".format(data['accountId'])
    getdetails = requests.get(geturl, headers=headers)
    getloan_response = getdetails.json()

    # GET LOAN ASSIGNED BRANCH KEY
    assigned_branchKey = getloan_response.get('assignedBranchKey','')

    # Head office, Govt payroll, Callcenter and Trade Finance branches to be excluded
    exclude_branches = ["8a1a33434f94875d014fb1515eb13843","8a9387d0623cd66401623d171dfa0e0d","8a9386e7750d479a01751b9116631230","8a9387b565b48c4a0165c39074f1072f"]

    if assigned_branchKey not in exclude_branches:
        # GET TOTAL BALANCE
        total_balance = float(getloan_response['principalBalance']) + float(getloan_response['interestBalance']) + float(getloan_response['feesBalance']) +float(getloan_response['accruedInterest'])

        # total balance in words
        p = inflect.engine()
        total_balance_words = p.number_to_words(int(total_balance)).capitalize()

        # GET LOAN AMOUNT
        loan_amount = float(getloan_response['loanAmount'])
        now = datetime.now()

        year = dateformat.format(now, 'Y')


        context = {
            "Client_name": data["fullName"],
            "total_balance": '{:,}'.format(round(total_balance)),
            "loan_amount": '{:,}'.format(round(loan_amount)),
            "total_balance_words":total_balance_words,
            "year":year
        }

        rendered_html = render_to_string('DEP_automation/notice_of_default.html', {'context': context}).encode(
            encoding="UTF-8")
        pdf_file = HTML(string=rendered_html,).write_pdf(
            stylesheets=[CSS(settings.STATIC_ROOT + 'css/premierug_dep/notice_of_default.css'),
                         CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                         CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                         ])
        response = HttpResponse(pdf_file, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="Default Notice_{}.pdf"'.format(data['accountId'])

        # attachDocument working
        encoded_string = base64.b64encode(response.content).decode('utf-8')
        attachdoc = {
            "document": {
                "documentHolderKey": getdetails.json()['encodedKey'],
                "documentHolderType": "LOAN_ACCOUNT",
                "name": "Default Notice_{}".format(data['accountId']),
                "type": "PDF"
            },
            "documentContent": encoded_string
        }
        doc_url = urls + "loans/{0}/documents/".format(data['accountId'])
        details = requests.post(doc_url, json=attachdoc, headers=headers)

        # CREATING TASKS TO BRANCH MANAGERS AND SUPERVISORS
        users_payload = {
            "branchID": assigned_branchKey,
            "offset": "0",
            "limit": "1000",
            "fullDetails": "True"

        }
        geturl = urls + "users/"
        getdetails = requests.get(geturl, json=users_payload, headers=headers)
        for obj in getdetails.json():
            try:
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a350e9745de50db0145f94719e50e68" or obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a2a8ba24e6921ac014e787a4eba127a":
                    # print('obj',obj['username'])
                    task_payload = {
                        "title": 'Default notice',
                        "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                        "description": "Default notice attached for {account_id}".format(account_id=data["accountId"]),
                        "status": "OPEN",
                        "clientID": getloan_response['accountHolderKey'],
                        "username": obj['username']
                    }

                    taskurl = urls + "tasks"
                    taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                    glload = taskdetails.json()
                    Task_results(username=obj['username'], notice_type="Default notice", task_status=glload).save()
            except KeyError:
                pass
        return response



# clientReference update
@csrf_exempt
def clientCreation(request):
    payload=json.loads(request.body)
    clientId = payload['clientID']
    environment= payload['environment']
    ClientCreation.delay(clientId,environment)

    return HttpResponse('200:Request Sent')
@csrf_exempt
def clientReference(request):
    payload=json.loads(request.body)
    clientId = payload['clientID']
    accountID = payload['accountID']
    environment= payload['environment']
    clientReference = payload['clientReference']
    updateClientReference.delay(clientId, accountID,environment,clientReference)

    return HttpResponse('200:Request Sent')

@csrf_exempt
def clearClientReference(request):
    # {
    #     "clientID": "{RECIPIENT_ID}",
    #     "customerReference": "{CF:CLIENT:CUSTOMER REFERENCE}",
    #     "accountID": "{ACCOUNT_ID}",
    #     "environment": "premieruganda.sandbox"
    # }
    payload=json.loads(request.body)
    clientID = payload['clientID']
    customerReference = payload['customerReference']
    accountID = payload['accountID']


    environment= payload['environment']
    clearClientReferenceCF.delay(clientID,environment,customerReference,accountID)

    return HttpResponse('200:Request Sent')

@csrf_exempt
def noticeDefaultV2(request):
    data=json.loads(request.body)
    # accountID = "*********"
    # environment = "premieruganda.sandbox"
    # fullName = "Eva Florence Nambalirwa"

    accountID = data['accountID']
    environment = data['environment']
    fullName = data['fullName']
    urls = "https://premieruganda.sandbox.mambu.com/api/" if environment.endswith(
        'sandbox') else 'https://premieruganda.mambu.com/api/'
    headers = {
        "ApiKey": "Dstrgrhnx4ue8CXdHxlDI3W6fbxsmjmm"
    }

    # headers = {
    #     "ApiKey": "3eKjDAxl8cFZhCZAsBZxO4furnfQmZ9s"
    # }
    geturl = urls + "loans/{0}".format(accountID)
    # print("geturl",geturl)

    getdetails = requests.get(geturl, headers=headers)
    getloan_response = getdetails.json()

    # print("getloan_response",getloan_response)

    # get client Details
    mobile_details = get_client_mobile(getloan_response.get('accountHolderKey', ''), urls, headers)
    # GET LOAN ASSIGNED BRANCH KEY
    assigned_branchKey = getloan_response.get('assignedBranchKey','')

    # Head office, Govt payroll, Callcenter and Trade Finance branches to be excluded
    exclude_branches = ["8a1a33434f94875d014fb1515eb13843","8a9387d0623cd66401623d171dfa0e0d","8a9386e7750d479a01751b9116631230","8a9387b565b48c4a0165c39074f1072f"]

    if assigned_branchKey not in exclude_branches:
        # GET TOTAL BALANCE
        total_balance = float(getloan_response['principalBalance']) + float(getloan_response['interestBalance']) + float(getloan_response['feesBalance']) +float(getloan_response['accruedInterest'])

        total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(getloan_response['feesDue']) + float(getloan_response['penaltyDue'])

        # total balance in words
        p = inflect.engine()
        total_balance_words = p.number_to_words(int(total_balance)).capitalize()

        # GET LOAN AMOUNT
        loan_amount = float(getloan_response['loanAmount'])
        now = datetime.now()

        year = dateformat.format(now, 'Y')

        # GET LOAN ACTIVATION DATE
        activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'], "%Y-%m-%dT%H:%M:%S+%f")
        # activation_date = datetime.strftime(activation,'%d %B %Y')
        activation_date = dateformat.format(activation, 'jS F Y')
        date_time = dateformat.format(now, 'jS F Y')



        context = {
            "Client_name": fullName,
            "total_due": '{:,}'.format(round(total_due)),
            "loan_amount": '{:,}'.format(round(loan_amount)),
            "total_balance_words":total_balance_words,
            "year":year,
            "activation_date":activation_date,
            "todayDate": date_time,
            "Client_phone_number": mobile_details.get('mobilePhone1', ''),

        }

        rendered_html = render_to_string('DEP_automation/notice_of_defaultv2.html', {'context': context}).encode(
            encoding="UTF-8")
        pdf_file = HTML(string=rendered_html,).write_pdf(
            stylesheets=[CSS(settings.STATIC_ROOT + 'css/premierug_dep/notice_of_defaultV2.css'),
                         CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                         CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                         ])
        response = HttpResponse(pdf_file, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="Default Notice_{}.pdf"'.format(accountID)

        # attachDocument working
        encoded_string = base64.b64encode(response.content).decode('utf-8')
        attachdoc = {
            "document": {
                "documentHolderKey": getdetails.json()['encodedKey'],
                "documentHolderType": "LOAN_ACCOUNT",
                "name": "Notification of Default_{}".format(accountID),
                "type": "PDF"
            },
            "documentContent": encoded_string
        }
        doc_url = urls + "loans/{0}/documents/".format(accountID)
        details = requests.post(doc_url, json=attachdoc, headers=headers)

        # CREATING TASKS TO BRANCH MANAGERS AND SUPERVISORS
        users_payload = {
            "branchID": assigned_branchKey,
            "offset": "0",
            "limit": "1000",
            "fullDetails": "True"

        }
        geturl = urls + "users/"
        getdetails = requests.get(geturl, json=users_payload, headers=headers)
        for obj in getdetails.json():
            try:
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a350e9745de50db0145f94719e50e68" or obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a2a8ba24e6921ac014e787a4eba127a":
                    # print('obj',obj['username'])
                    task_payload = {
                        "title": 'Default notice',
                        "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                        "description": "Notification of Default attached for {account_id}".format(account_id=accountID),
                        "status": "OPEN",
                        "clientID": getloan_response['accountHolderKey'],
                        "username": obj['username']
                    }

                    taskurl = urls + "tasks"
                    taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                    glload = taskdetails.json()
                    Task_results(username=obj['username'], notice_type="Default notice", task_status=glload).save()
            except KeyError:
                pass
        return response
