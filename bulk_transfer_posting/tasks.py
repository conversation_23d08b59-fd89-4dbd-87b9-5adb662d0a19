from __future__ import absolute_import, unicode_literals

import configparser

from newugandalms.celery import app
from mambu.utils import <PERSON><PERSON><PERSON>, <PERSON><PERSON>uError, MambuAPIError
from mambu.models import Credential, Environment
import pickle
import xlsxwriter
from django.core.mail import send_mail, BadHeaderError,EmailMessage
from io import StringIO, BytesIO
import math
import redis
import itertools
from datetime import datetime
import tablib
import xlrd
import pandas as pd
import json
import os
from django.conf import settings
import requests
from requests.exceptions import RequestException

premkeurl = "https://premierkenya.mambu.com/api/"


premugurl = "https://premieruganda.mambu.com/api/"
premug_sandbox_url = "https://premieruganda.sandbox.mambu.com/api/"



@app.task
def post_transactions(s_file, mambu_user_email, transaction_type, environment_name, file_name):
    env = Environment.objects.get(url=environment_name)
    auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
    headers = {
        auth_user.apikey: auth_user.generatedkey,
        "Accept": "application/vnd.mambu.v2+json"
    }

    start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    file = pickle.loads(s_file)
    emailaddress = mambu_user_email


    workbook = xlrd.open_workbook(file_contents=file)
    sheet = workbook.sheet_by_index(0)

    receipt_store = redis.StrictRedis(host='localhost', port=6379, db=1)
    receipt_store.config_get('databases')
    receipt_store.flushall()

    file_object = BytesIO()  # create a file-like object
    report = xlsxwriter.Workbook(file_object)
    results_to_post = report.add_worksheet('Post Results')
    results_to_post_count = 0
    results_to_post.write_row(0, 0, ('LGF ID', 'STATUS CODE', 'RETURN CODE', 'RETURN STATUS', 'ERROR SOURCE'))
    receipts_count = 0
    return_code = ''
    return_status = ''
    error_source = ''

    if transaction_type == 'TRANSFER':
        for row in range(1, sheet.nrows):
            try:
                toloanaccount = int(sheet.cell(row, 0).value)
            except ValueError:
                toloanaccount = sheet.cell(row, 0).value

            # print("toloanaccount",toloanaccount)

            try:
                lgf_id = sheet.cell(row, 1).value
            except ValueError:
                lgf_id = sheet.cell(row, 1).value

            try:
                transfer_amount = sheet.cell(row, 2).value
            except ValueError:
                transfer_amount = sheet.cell(row, 2).value
            try:
                notes = sheet.cell(row, 3).value
            except ValueError:
                notes = sheet.cell(row, 3).value


            amount ='{:.2f}'.format(transfer_amount)

            toloanaccount = [toloanaccount]
            TaskId = [lgf_id]
            amount = [amount]
            notes = [notes]


            # LGF TO LOANACCOUNT TRANSFER PAYLOAD
            payloadV2 = {
                "amount": amount[0],
                "notes": notes[0],
                "transferDetails": {
                    "linkedAccountId": toloanaccount[0],
                    "linkedAccountType": "LOAN"
                }
            }

            # payload = {
            #     'type': transaction_type,
            #     'toLoanAccount': toloanaccount[0],
            #     'amount': amount[0],
            #     'notes': notes[0],
            # }
            # print("payload",payload)
            # LGF TO LOANACCOUNT TRANSFER
            redis_dict = {}
            if transaction_type is not None:
                url = premkeurl + "deposits/{0}/transfer-transactions".format(int(TaskId[0]))
                # print("TASKSURL: ", url)
                try:
                    res = requests.post(url, json=payloadV2, headers=headers)
                    resp_json = res.json()
                except RequestException as err:
                    raise err
                else:
                    return_code = resp_json.get('returnCode', '')
                    return_status = resp_json.get('returnStatus', '')
                    error_source = resp_json.get('errorSource', '')

                    # Handle error response
                    if 'errors' in resp_json:
                        error = resp_json['errors'][0]
                        error_code = error.get('errorCode', '')
                        error_reason = error.get('errorReason', '')
                        # print(f"Error code: {error_code}")
                        # print(f"Error reason: {error_reason}")

                    status_code = res.status_code
                    receipts_count += 1
                    receipt_store.lpush(TaskId[0], '{status_code,return_code,return_status,error_source}')

            # print("REDIS STORE: ", receipt_store.scan_iter())
            count = 0
            for key in receipt_store.scan_iter(TaskId[0]):
                # print("KEY: ", key)
                count = count + 1
                results_to_post_count = results_to_post_count + 1

                dat = receipt_store.lrange(key, 0, -1)
                # print("REDIS DATA: ",dat)
                for row in dat:
                    row_values = eval(row)
                    # print("row_values",row_values)
                    if status_code in row_values:
                        redis_status_code = status_code
                    if return_code in row_values:
                        redis_code = return_code
                    if return_status in row_values:
                        redis_status = return_status
                    if error_source in row_values:
                        redis_error_source = error_source

                data = (TaskId[0], redis_status_code, redis_code, redis_status, redis_error_source)
                # print("DATA FROM REDIS: ", data)
                results_to_post.write_row(results_to_post_count, 0, data)
                # print("counted: {} keys".format(count))
        report.close()
        # ##send email
        try:
            emailaddresses = ['<EMAIL>']
            cc = ['<EMAIL>','<EMAIL>']

            emailaddresses.append(emailaddress)

            email = EmailMessage('Lgf transfers Report',
                                 'Please find attached report ' +  '\n Start time :' + start_time + '\n Finished: ' + datetime.now().strftime(
                                     '%Y-%m-%d %H:%M:%S'), '<EMAIL>', emailaddresses+cc)
            email.content_subtype = "html"
            email.attach('Results for ' + file_name, file_object.getvalue(), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            emailsent = email.send()
        except Exception as e:
            print("ERROREMAILMESSAGE: ", e)
    elif transaction_type =='WITHDRAWAL':
        results_to_post.write_row(0, 0, ('LGF ID', 'STATUS CODE','RETURN CODE', 'RETURN STATUS', 'ERROR SOURCE'))

        receipts_count = 0
        return_code = ''
        return_status = ''
        error_source = ''

        for row in range(1, sheet.nrows):
            try:
                lgf_id = sheet.cell(row, 0).value
            except ValueError:
                lgf_id = sheet.cell(row, 0).value

            try:
                transfer_amount = sheet.cell(row, 1).value
            except ValueError:
                transfer_amount = sheet.cell(row, 1).value
            try:
                notes = sheet.cell(row, 2).value
            except ValueError:
                notes = sheet.cell(row, 2).value


            amount ='{:.2f}'.format(transfer_amount)

            payload = {
                'type': transaction_type,
                'amount': amount,
                'notes': notes,
            }

            # Post a withdrawal
            redis_dict = {}
            if transaction_type is not None:
                url = premkeurl + "savings/{0}/transactions".format(int(lgf_id))
                print("TASKSURL: ", url)
                try:
                    res = requests.post(url, json=payload, headers=headers)
                except RequestException as err:
                    raise err
                else:
                    # print('res',res.json())
                    try:
                        return_code = res.json()['returnCode']
                    except KeyError as e:
                        return_code = ''

                    try:
                        return_status = res.json()['returnStatus']
                    except KeyError as e:
                        return_status = ''
                    try:
                        error_source = res.json()['errorSource']
                    except KeyError as e:
                        error_source = ''
                    status_code = res.status_code
                    receipts_count += 1
                    receipt_store.lpush(lgf_id, '{status_code,return_code,return_status,error_source}')

            # print("REDIS STORE: ", receipt_store.scan_iter())
            count = 0
            for key in receipt_store.scan_iter():
                # print("KEY: ", key)
                count = count + 1
                results_to_post_count = results_to_post_count + 1

                dat = receipt_store.lrange(key, 0, -1)
                # print("REDIS DATA: ",dat)
                for row in dat:
                    row_values = eval(row)
                    # print("row_values",row_values)
                    if status_code in row_values:
                        redis_status_code = status_code
                    if return_code in row_values:
                        redis_code = return_code
                    if return_status in row_values:
                        redis_status = return_status
                    if error_source in row_values:
                        redis_error_source = error_source

                data = (lgf_id, redis_status_code, redis_code, redis_status, redis_error_source)
                # print("DATA FROM REDIS: ", data)
                results_to_post.write_row(results_to_post_count, 0, data)
                # print("counted: {} keys".format(count))
        report.close()
        # print('VALUE',file_object.getvalue())
        # ##send email
        try:
            emailaddresses = []
            emailaddresses.append(emailaddress)
            email = EmailMessage('Transfer report for ' + transaction_type,
                                 'Please find attached report for ' + transaction_type + '\n Start time :' + start_time + '\n Finished: ' + datetime.now().strftime(
                                     '%Y-%m-%d %H:%M:%S'), '<EMAIL>', emailaddresses)

            email.content_subtype = "html"
            email.attach('Results for ' + file_name, file_object.getvalue(),'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

            emailsent = email.send()
        except Exception as e:
            print(e)


# BULK LGF TRANSFER AND WITHDRAWALS PREMIERUG
@app.task
def post_transactions_premug(s_file, mambu_user_email, transaction_type, environment_name, file_name):
    # env = Environment.objects.get(url=environment_name)
    # auth_user = Credential.objects.get(apikey="ApiKey", environment=env)


    start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    file = pickle.loads(s_file)
    emailaddress = mambu_user_email


    workbook = xlrd.open_workbook(file_contents=file)
    sheet = workbook.sheet_by_index(0)

    receipt_store = redis.StrictRedis(host='localhost', port=6379, db=1)
    receipt_store.config_get('databases')
    receipt_store.flushall()

    file_object = BytesIO()  # create a file-like object
    report = xlsxwriter.Workbook(file_object)
    results_to_post = report.add_worksheet('Post Results')
    results_to_post_count = 0
    results_to_post.write_row(0, 0, ('LGF ID', 'STATUS CODE', 'RETURN CODE', 'RETURN STATUS', 'ERROR SOURCE'))
    receipts_count = 0
    return_code = ''
    return_status = ''
    error_source = ''

    if transaction_type == 'TRANSFER':
        config = configparser.ConfigParser()
        config.read('secrets.ini')
        BULKTransfer = config.get('Credentials', 'PREMIERUGANDA_PROD_APIKEY_BULKTRANSFER')
        PREMIERUGANDA_SANDBOX_URL = config.get('Credentials', 'PREMIERUGANDA_SANDBOX_URL')
        PREMIERUGANDA_LIVE_URL =  config.get('Credentials', 'PREMIERUGANDA_LIVE_URL')

        headers = {
            "apiKey": "{}".format(BULKTransfer),
            "Accept": "application/vnd.mambu.v2+json"
        }

        # transfer consumerKey
        for row in range(1, sheet.nrows):
            try:
                toloanaccount = int(sheet.cell(row, 0).value)
            except ValueError:
                toloanaccount = sheet.cell(row, 0).value

            # print("toloanaccount",toloanaccount)

            try:
                lgf_id = sheet.cell(row, 1).value
            except ValueError:
                lgf_id = sheet.cell(row, 1).value

            try:
                transfer_amount = sheet.cell(row, 2).value
            except ValueError:
                transfer_amount = sheet.cell(row, 2).value
            try:
                notes = sheet.cell(row, 3).value
            except ValueError:
                notes = sheet.cell(row, 3).value


            amount ='{:.2f}'.format(transfer_amount)

            toloanaccount = [toloanaccount]
            TaskId = [lgf_id]
            amount = [amount]
            notes = [notes]


            # LGF TO LOANACCOUNT TRANSFER PAYLOAD
            payloadV2 = {
                "amount": amount[0],
                "notes": notes[0],
                "transferDetails": {
                    "linkedAccountId": toloanaccount[0],
                    "linkedAccountType": "LOAN"
                }
            }

            # LGF TO LOANACCOUNT TRANSFER
            if transaction_type is not None:
                root_url = PREMIERUGANDA_SANDBOX_URL if environment_name.endswith('sandbox') else PREMIERUGANDA_LIVE_URL
                url = root_url + "deposits/{0}/transfer-transactions".format(TaskId[0])
                try:
                    res = requests.post(url, json=payloadV2, headers=headers)
                    resp_json = res.json()

                except RequestException as err:
                    raise err
                else:
                    if res.status_code in [201, 200]:
                        return_code = "201"
                        return_status = "SUCCESS"
                        error_source = res.json().get('errorSource', '')
                        status_code = res.status_code
                        receipts_count += 1
                        receipt_store.lpush(TaskId[0], '{status_code,return_code,return_status,error_source}')
                    else:
                        for error in res.json().get('errors', []):
                            return_code = error.get('errorCode', '')
                            return_status = error.get('errorReason', '')
                            error_source = error.get('errorSource', '')
                            status_code = res.status_code

                            receipts_count += 1
                            receipt_store.lpush(TaskId[0], '{status_code,return_code,return_status,error_source}')

            # print("REDIS STORE: ", receipt_store.scan_iter())
            count = 0
            for key in receipt_store.scan_iter(TaskId[0]):
                # print("KEY: ", key)
                count = count + 1
                results_to_post_count = results_to_post_count + 1

                dat = receipt_store.lrange(key, 0, -1)
                # print("REDIS DATA: ",dat)
                for row in dat:
                    row_values = eval(row)
                    # print("row_values",row_values)
                    if status_code in row_values:
                        redis_status_code = status_code
                    if return_code in row_values:
                        redis_code = return_code
                    if return_status in row_values:
                        redis_status = return_status
                    if error_source in row_values:
                        redis_error_source = error_source

                data = (TaskId[0], redis_status_code, redis_code, redis_status, redis_error_source)
                # print("DATA FROM REDIS: ", data)
                results_to_post.write_row(results_to_post_count, 0, data)
                # print("counted: {} keys".format(count))
        report.close()
        # ##send email
        try:
            emailaddresses = ['<EMAIL>']

            cc =["<EMAIL>"]

            emailaddresses.append(emailaddress)

            email = EmailMessage('Lgf transfers Report',
                                 'Please find attached report ' +  '\n Start time :' + start_time + '\n Finished: ' + datetime.now().strftime(
                                     '%Y-%m-%d %H:%M:%S'), '<EMAIL>', emailaddresses +cc)
            email.content_subtype = "html"
            email.attach('Results for ' + file_name, file_object.getvalue(), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            emailsent = email.send()
        except Exception as e:
            print("ERROREMAILMESSAGE: ", e)

    elif transaction_type =='WITHDRAWAL':
        config = configparser.ConfigParser()
        config.read('secrets.ini')
        BULKWithDrawal = config.get('Credentials', 'PREMIERUGANDA_PROD_APIKEY_BULKWITHDRAWAL')
        PREMIERUGANDA_SANDBOX_URL = config.get('Credentials', 'PREMIERUGANDA_SANDBOX_URL')
        PREMIERUGANDA_LIVE_URL =  config.get('Credentials', 'PREMIERUGANDA_LIVE_URL')
        headersV2 = {
            "apiKey": "{}".format(BULKWithDrawal),
            'Accept': 'application/vnd.mambu.v2+json',

        }

        results_to_post.write_row(0, 0, ('LGF ID', 'STATUS CODE','RETURN CODE', 'RETURN STATUS', 'ERROR SOURCE'))

        receipts_count = 0
        return_code = ''
        return_status = ''
        error_source = ''

        for row in range(1, sheet.nrows):
            try:
                lgf_id = sheet.cell(row, 0).value
            except ValueError:
                lgf_id = sheet.cell(row, 0).value

            try:
                withdrawal_amount = sheet.cell(row, 1).value
            except ValueError:
                withdrawal_amount = sheet.cell(row, 1).value

            try:
                xl_channel = sheet.cell(row, 2).value
            except ValueError:
                xl_channel = sheet.cell(row, 2).value

            try:
                notes = sheet.cell(row, 3).value
            except ValueError:
                notes = sheet.cell(row, 3).value

            # split channel
            split_channel = xl_channel.split('-')
            channel_code = split_channel[0]
            # print('CHANNEL',channel_code)

            sanitized_lgf_id = int(lgf_id) if type(lgf_id) is float or type(lgf_id) is int else lgf_id

            TaskId = [sanitized_lgf_id]
            withdrawal_amount = [withdrawal_amount]
            channel_id = [channel_code]
            notes = [notes]

            amount ='{:.2f}'.format(withdrawal_amount[0])

            # LGF TO LOANACCOUNT TRANSFER PAYLOAD
            # payload = {
            #     "type": transaction_type,
            #     "amount": amount,
            #     "method": channel_id[0],
            #     "notes": notes[0]
            # }
            payloadV2 = {
                # "_Transaction_Details_Transaction": {
                #     "CHECK_NUMBER_TRANSACTION_CHANNEL": check_no[0],
                #     "ACCOUNT_NUMBER_TRANSACTION_CHANN": account_no[0]
                # },
                "transactionDetails": {
                    "transactionChannelKey": channel_id[0]
                },
                "amount": amount,
                "notes": notes[0]
            }

            # print('PAYLOAD',payload)

            # Post a withdrawal
            redis_dict = {}
            if transaction_type is not None:
                root_url = PREMIERUGANDA_SANDBOX_URL if environment_name.endswith('sandbox') else PREMIERUGANDA_LIVE_URL
                url = root_url + "deposits/{0}/withdrawal-transactions".format(TaskId[0])

                try:
                    res = requests.post(url, json=payloadV2, headers=headersV2)
                except RequestException as err:
                    raise err
                else:
                    if res.status_code in [201, 200]:
                        return_code = "201"
                        return_status = "SUCCESS"
                        error_source = res.json().get('errorSource', '')
                        status_code = res.status_code
                        receipts_count += 1
                        receipt_store.lpush(TaskId[0], '{status_code,return_code,return_status,error_source}')
                    else:
                        for error in res.json().get('errors', []):
                            return_code = error.get('errorCode', '')
                            return_status = error.get('errorReason', '')
                            error_source = error.get('errorSource', '')
                            status_code = res.status_code

                            receipts_count += 1
                            receipt_store.lpush(TaskId[0], '{status_code,return_code,return_status,error_source}')

                    # print("REDIS STORE: ", receipt_store.scan_iter())
                    count = 0
                    for key in receipt_store.scan_iter(TaskId[0]):
                        # print("KEY: ", key)
                        count = count + 1
                        results_to_post_count = results_to_post_count + 1

                        dat = receipt_store.lrange(key, 0, -1)
                        # print("REDIS DATA: ",dat)
                        for row in dat:
                            row_values = eval(row)
                            # print("row_values",row_values)
                            if status_code in row_values:
                                redis_status_code = status_code
                            if return_code in row_values:
                                redis_code = return_code
                            if return_status in row_values:
                                redis_status = return_status
                            if error_source in row_values:
                                redis_error_source = error_source

                        data = (TaskId[0], redis_status_code, redis_code, redis_status, redis_error_source)
                        # print("DATA FROM REDIS: ", data)
                        results_to_post.write_row(results_to_post_count, 0, data)
                        # print("counted: {} keys".format(count))
        report.close()

        # ##send email
        try:
            emailaddresses = ['<EMAIL>']

            cc = ["<EMAIL>"]

            emailaddresses.append(emailaddress)

            email = EmailMessage('Withdrawal Report ',
                                 'Please find attached report for ' + transaction_type + '\n Start time :' + start_time + '\n Finished: ' + datetime.now().strftime(
                                     '%Y-%m-%d %H:%M:%S'), '<EMAIL>', emailaddresses+cc)
            email.content_subtype = "html"
            email.attach('Results for ' + file_name, file_object.getvalue(), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            emailsent = email.send()
        except Exception as e:
            print("ERROREMAILMESSAGE: ", e)
