import configparser
from django.shortcuts import render, redirect
from .forms import transfersForm
from .tasks import post_transactions,post_transactions_premug
import xlrd
import xlsxwriter
from django.http import HttpResponseRedirect
from mambu.utils import Mambu
from mambu.models import Credential, Environment
import base64
import json
from django.views.decorators.csrf import csrf_exempt
import pickle

# premierke bulk transfer and withdrawals
@csrf_exempt
def post_transactions_index(request):
    mambu_user = {}
    user_role = ""
    mambu_user_email =""
    environment_name =""
    signed_request = request.POST.get('signed_request')
    bulk_posting_form = transfersForm(request.POST, request.FILES)

    if signed_request is not None:
        encoded_string = signed_request.split('.')[1]
        encoded_string += "=" * ((4 - len(encoded_string) % 4) % 4)
        decoded_string = base64.b64decode(encoded_string).decode("utf-8")
        obj = json.loads(decoded_string)
        mambu_user_key = obj['USER_KEY']
        environment_name = obj["DOMAIN"].split('.mambu.com', 1)[0]
        # print("Environment Name: ",environment_name)
        env = Environment.objects.get(url=environment_name)
        auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
        mambu = Mambu(auth_user.apikey, auth_user.generatedkey, auth_user.environment.url)
        mambu_user = mambu.get_users(username=mambu_user_key, fullDetails=True)
        mambu_user_email = mambu_user["email"]
        request.session['mambu_user'] = mambu_user


        user_role = mambu_user["role"]["encodedKey"] if "role" in mambu_user else ""

    c = {'form': bulk_posting_form, 'signed_request': signed_request}

    allowed_userroles = ["8a725e1346caaee20146cc5f29240c52", "8a858f7e5c213ef4015c2f2a75bc6d3e","8a9387de867f37dc01867fbe2c970d16","8a9386c9661b24cd016638364cab23f1"]

    if request.session.get('mambu_user'):

        if user_role in allowed_userroles:
            if request.method == "POST":
                if bulk_posting_form.is_valid():
                    transaction_type = request.POST.get('trans_type')
                    file_name = request.FILES['file'].name
                    try:
                        post_transactions.delay(pickle.dumps(request.FILES['file'].read()), mambu_user_email, transaction_type,
                                           environment_name, file_name)
                    except post_transactions.OperationalError as exc:
                        logger.exception('Sending task raised: %r', exc)
                    return HttpResponseRedirect('/bulk_transfer_posting/progress?email=' + mambu_user_email)
            else:
                bulk_posting_form = transfersForm()
            return render(request, 'premierke_bulk_transfers/bulk_transfer_post.html', c)
        else:
            return redirect('bulk_transfer_posting:rolenotallowedpremierke')
    else:
       return render(request,'premierke_bulk_transfers/404.html')

@csrf_exempt
def main_dashboard(request):
    status = ""
    # mambu_user_email="<EMAIL>"
    # environment_name = "premierkenya.sandbox"
    mambu_user_email = request.session.get('mambu_user')['email']
    environment_name = request.session["environment_name"]

    errors = []
    if (request.POST):
        bulk_posting_form = transfersForm(request.POST, request.FILES)

        if bulk_posting_form.is_valid():
            transaction_type = request.POST.get('trans_type')
            file_name = request.FILES['file'].name

            if transaction_type == 'NO_SELECTION':
                errors.append('You have not selected any transaction type!!!')
            if not errors:
                try:
                    post_transactions.delay(pickle.dumps(request.FILES['file'].read()), mambu_user_email, transaction_type,
                                       environment_name, file_name)
                except post_transactions.OperationalError as exc:
                    logger.exception('Sending task raised: %r', exc)

                return HttpResponseRedirect('/bulk_transfer_posting/progress?email=' + mambu_user_email)
    else:
        bulk_posting_form = transfersForm()
    return render(request, 'premierke_bulk_transfers/bulk_transfer_post.html',
                  {"form": bulk_posting_form, 'errors': errors})





def role_not_allowed(request):
    return render(request,'premierke_bulk_transfers/access_denied.html')


def progress(request):
    if request.method == 'GET':
        status = "processing ...."
        return render(request, 'premierke_bulk_transfers/progress.html', {'data': status, "emailadd":request.GET.get('email')})


# premierug bulk transfer and withdrawals
@csrf_exempt
def post_transactionsug_index(request):
    mambu_user = {}
    bulk_posting_form_premug = transfersForm(request.POST, request.FILES)
    user_role = ""
    mambu_user_email = ""
    environment_name = ""
    signed_request = request.POST.get('signed_request')
    # signed_request="148187140a638325063f9a2a1ba0a28a46360eb6e5ef548c14d665320f8508dc.eyJET01BSU4iOiJwcmVtaWVydWdhbmRhLm1hbWJ1LmNvbSIsIkFMR09SSVRITSI6ImhtYWNTSEEyNTYiLCJURU5BTlRfSUQiOiJwcmVtaWVydWdhbmRhIiwiVVNFUl9LRVkiOiI4YTkzODc4NjZhZTk2NTAzMDE2YWVhMjAyMDk4MWIxMSJ9"
    if signed_request is not None:
        encoded_string = signed_request.split('.')[1]
        encoded_string += "=" * ((4 - len(encoded_string) % 4) % 4)
        decoded_string = base64.b64decode(encoded_string).decode("utf-8")
        obj = json.loads(decoded_string)
        mambu_user_key = obj['USER_KEY']
        environment_name = obj["DOMAIN"].split('.mambu.com', 1)[0]
        config = configparser.ConfigParser()
        config.read('secrets.ini')
        withDrawalTransfer = config.get('Credentials', 'PREMIERUGANDA_PROD_APIKEY_BULKTRANSFER')
        mambu = Mambu("ApiKey", withDrawalTransfer, environment_name)
        mambu_user = mambu.get_users(username=mambu_user_key, fullDetails=True)

        mambu_user_email = mambu_user["email"]
        user_role = mambu_user["role"]["encodedKey"] if "role" in mambu_user else ""
        request.session['mambu_user'] = mambu_user


        ## System Admin Keys
    c = {'form': bulk_posting_form_premug, 'signed_request': signed_request}
    errors = []
    allowed_userroles = ["8abc1aea45eb1c2a0145ef7b21155074","8a9387bb60028c90016006bc4f9666d1",
                         "8a93879f6dd3dd3e016dd4243e720e31","8abc1aea45eb1c2a0145ef76d23a4e0a"]


    if request.session.get('mambu_user'):

        if user_role in allowed_userroles:
            if request.method == "POST":
                if bulk_posting_form_premug.is_valid():
                    transaction_type = request.POST.get('trans_type')
                    file_name = request.FILES['file'].name
                    if transaction_type == 'NO_SELECTION':
                        errors.append('You have not selected any transaction type!!!')

                    if not errors:
                        try:
                            post_transactions_premug.delay(pickle.dumps(request.FILES['file'].read()), mambu_user_email,
                                                           transaction_type,environment_name, file_name)
                        except post_transactions_premug.OperationalError as exc:
                            logger.exception('Sending task raised: %r', exc)

                        return HttpResponseRedirect('/bulk_transfer_posting/response?email=' + mambu_user_email)
            else:
                bulk_posting_form_premug = transfersForm()
            return render(request, 'premierug_bulk_transfers/bulk_transfer_post.html', c)
        else:
            return redirect('bulk_transfer_posting:rolenotallowedpremierug')
    else:
       return render(request,'premierug_bulk_transfers/404.html')



def role_not_allowed_premug(request):
    return render(request,'premierug_bulk_transfers/access_denied.html')


@csrf_exempt
def main_dashboard_premierug(request):
    status = ""
    # mambu_user_email="<EMAIL>"
    # environment_name = "premieruganda.sandbox"

    mambu_user_email = request.session.get('mambu_user')['email']
    environment_name = request.session["environment_name"]

    errors = []
    if (request.POST):
        bulk_posting_form_premug = transfersForm(request.POST, request.FILES)
        if bulk_posting_form_premug.is_valid():
            transaction_type = request.POST.get('trans_type')
            file_name = request.FILES['file'].name
            # print("transaction_type",transaction_type)

            if transaction_type=='NO_SELECTION':
                errors.append('You have not selected any transaction type!!!')

            if not errors:
                try:
                    post_transactions_premug.delay(pickle.dumps(request.FILES['file'].read()), mambu_user_email, transaction_type,
                                       environment_name, file_name)
                except post_transactions_premug.OperationalError as exc:
                    logger.exception('Sending task raised: %r', exc)

                return HttpResponseRedirect('/bulk_transfer_posting/response?email=' + mambu_user_email)
    else:
        bulk_posting_form_premug = transfersForm()
    return render(request, 'premierug_bulk_transfers/bulk_transfer_post.html',
                  {"form": bulk_posting_form_premug, 'errors': errors})

def progress_premug(request):
    if request.method == 'GET':
        status = "processing ...."
        return render(request, 'premierug_bulk_transfers/response.html', {'data': status, "emailadd":request.GET.get('email')})
