from django.urls import path
from . import views

app_name = 'bulk_transfer_posting'
urlpatterns = [
    # premierke urls
    path('premirke_bulk_transfers_index/$', views.post_transactions_index, name="premierke_transactions_index"),
    path('role_not_allowed/', views.role_not_allowed, name='rolenotallowedpremierke'),
    path('select_transaction_type_premierke', views.main_dashboard, name='landing_page'),
    path('progress/', views.progress, name="progress"),

    # premierug urls
    path('premierug_bulk_transfers_index/$', views.post_transactionsug_index, name="premierug_transactions_index"),
    path('role_not_allowed/', views.role_not_allowed_premug, name='rolenotallowedpremierug'),
    path('response/', views.progress_premug, name="progress_ug"),
    path('select_transaction_type_premierug', views.main_dashboard_premierug, name='landing_page_premug'),

    # path('premierug_test/', views.main_dashboard_premierug),

]