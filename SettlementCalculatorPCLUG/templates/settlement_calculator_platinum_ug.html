{% extends 'parent.html' %}
{% load crispy_forms_tags %}
{% block content %}

<form method="post" enctype="multipart/form-data">
    {% csrf_token %}

    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">{{ title }}</h3>
        </div>

        <!-- ✅ Django messages framework block -->
        {% if messages %}
        <div class="panel-body">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- ✅ Legacy manual error_message fallback -->
        {% if error_message %}
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ error_message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% else %}

        <div class="panel-body">

            <div class="panel panel-default">
                <div class="panel-heading">Details</div>
                    <div class="panel-body">

                        <!-- ✅ Optional success messages -->
                        {% if yes %}
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ yes }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endif %}

                        {% if message %}
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endif %}

                        <!-- ✅ Optional list of error rows -->
                        {% if errors %}
                        <div class="panel panel-danger">
                            <div class="panel-heading">The file import has generated the following errors:</div>
                            <ul class="list-group">
                                {% for row in errors %}
                                <li class="list-group-item text-danger">{{ row }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}

                        <!-- ✅ Main form block -->
                        <div class="col-md-6">
                            <input type="hidden" name="signed_request" value="{{ signed_request }}">
                            {{ form|crispy }}
                        </div>

                    </div>
                 </div>
            </div>
        </div>

        {% endif %}
    </div>
</form>

{% endblock %}