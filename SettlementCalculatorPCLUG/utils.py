import configparser
import logging
import json
import requests
from datetime import date, timedelta

logger = logging.getLogger(__name__)

def get_api_credentials():
    try:
        config = configparser.ConfigParser()
        with open('secrets.ini') as config_file:
            config.read_file(config_file)
        url = config.get('Credentials', 'PLATINUMUGANDA_PROD_URL')
        api_key = config.get('Credentials', 'SETTLEMENT_CALCULATOR_API_KEY_PROD')
        return url, api_key
    except (configparser.Error, FileNotFoundError) as e:
        logger.error(f"Configuration error: {str(e)}")
        raise ValueError("API configuration error.")

def handle_api_response(response):
    if response.status_code != 200:
        error_msg = response.json().get('error', 'Unknown error')
        logger.error(f"API Error: {error_msg}")
        raise Exception(f"API error occurred: {error_msg}")
    return response.json()

def getSalaryAdvanceLoans(account_holder_key, url, api_key):
    headers = {'Content-Type': 'application/json', 'apiKey': api_key, 'Accept': 'application/vnd.mambu.v2+json'}
    payload = json.dumps({
        "filterCriteria": [
            {"field": "accountHolderKey", "operator": "EQUALS", "value": account_holder_key},
            {"field": "accountState", "operator": "IN", "values": ["ACTIVE", "ACTIVE_IN_ARREARS"]}
        ]
    })
    response = requests.post(f"{url}loans:search?detailsLevel=FULL", headers=headers, data=payload)
    response.raise_for_status()
    total = 0
    for loan in response.json():
        if loan.get('loanName', '').strip() == 'Salary Advance USSD':
            balances = loan.get('balances', {})
            total += sum(float(balances.get(k, 0)) for k in ['principalBalance', 'feesBalance', 'interestBalance', 'penaltyBalance'])
    return total

def getLgfBalance(account_holder_key, url, api_key):
    headers = {'Content-Type': 'application/json', 'apiKey': api_key, 'Accept': 'application/vnd.mambu.v2+json'}
    payload = json.dumps({
        "filterCriteria": [
            {"field": "accountHolderKey", "operator": "EQUALS", "value": account_holder_key},
            {"field": "accountState", "operator": "IN", "values": ["ACTIVE"]}
        ]
    })
    response = requests.post(f"{url}deposits:search?detailsLevel=FULL", headers=headers, data=payload)
    response.raise_for_status()
    for account in response.json():
        if account.get('name') == 'LGF' and account.get('accountState') == 'ACTIVE':
            return float(account.get('balances', {}).get('totalBalance', 0))
    return 0

def calculate_interest(loan_data, diff,fee_expected,loanTerm,schedule_data):
    """
    Calculate interest based on loan data and time difference.
    
    Args:
        loan_data: Dictionary containing loan information
        diff: Time difference in months
        
    Returns:
        float: Calculated interest amount
    """
    installments = schedule_data["installments"]

    try:
        totalPrincipal = float(loan_data['balances']['principalBalance']) + float(loan_data['balances']['principalPaid'])


        if fee_expected==6000:
            # old terms cs
            discount_rates = [
                    ((0, 2), 20.00),
                    ((3, 4), 15.00),
                    ((5, 5), 12.00),
                    ((6, 6), 10.00),
                    ((7, 8), 9.50),
                    ((9, 12), 7.00),
                    ((13, 15), 6.00),
                    ((16, 24), 5.50),
                    ((25, 41), 5.00),
                    ((42, 47), 4.50),
                    ((48, 59), 3.97),
                    ((60, float('inf')), 3.70)]
            
            settlementAccrueadFee = 6000
            adminFeeBalance = float(loan_data["balances"]["feesBalance"]) + float(settlementAccrueadFee)
        
            # Calculate interest rate based on time difference
            for (start, end), rate in discount_rates:
                if start <= diff <= end:
                    interest_rate = rate
                    break

        
            interestExpected = (totalPrincipal *diff *interest_rate/100)

    

            # print("adminFee",adminFeeBalance)
            applicationFee = 0

            # calculate settlement interest
            if 1 <= loanTerm <= 84 and diff >= 25:

                # Find the installment with number "26"
                target = next((inst for inst in installments if inst["number"] == str(diff)), None)

                if target:
                    settlementAccruedInterest = target['interest']['amount']['expected']
                else:
                    print("Installment with number '26' not found.")

            else:

                settlementAccruedInterest = float(interestExpected) - float(loan_data['balances']['interestPaid']) - float(loan_data['balances']['interestBalance'])
            settlementAccruedInterest = round(settlementAccruedInterest, 2)
            # print("settlementAccruedInterest",settlementAccruedInterest)

            

            return interestExpected,adminFeeBalance,settlementAccruedInterest
        else:
            loan_fee_structure = [
            {"range": (1, 12), "interest_rate": 2.80, "application_fee": 200000, "appraisal_fee_percent": 12.50, "admin_fee": 250000},
            {"range": (13, 18), "interest_rate": 2.80, "application_fee": 200000, "appraisal_fee_percent": 12.50, "admin_fee": 150000},
            {"range": (19, 24), "interest_rate": 2.80, "application_fee": 200000, "appraisal_fee_percent": 12.50, "admin_fee": 150000},
            {"range": (25, 36), "interest_rate": 2.80, "application_fee": 200000, "appraisal_fee_percent": 12.50, "admin_fee": 130000},
            {"range": (37, 48), "interest_rate": 2.80, "application_fee": 200000, "appraisal_fee_percent": 12.50, "admin_fee": 24000},
            {"range": (49, 84), "interest_rate": 2.80, "application_fee": 200000, "appraisal_fee_percent": 12.50, "admin_fee": 15000},
            {"range": (85, 96), "interest_rate": 2.45, "application_fee": 50000,  "appraisal_fee_percent": 12.50, "admin_fee": 5000}
            ]

            # adminFee  is dependent on diiff
            adminFee = loan_fee_structure[0]['admin_fee'] if diff <= 12 else loan_fee_structure[1]['admin_fee'] if diff <= 18 else loan_fee_structure[2]['admin_fee'] if diff <= 24 else loan_fee_structure[3]['admin_fee'] if diff <= 36 else loan_fee_structure[4]['admin_fee'] if diff <= 48 else loan_fee_structure[5]['admin_fee'] if diff <= 84 else loan_fee_structure[6]['admin_fee']

            # Calculate interest rate based on time difference
            for structure in loan_fee_structure:
                start, end = structure["range"]
                if start <= diff <= end:
                    interest_rate = structure["interest_rate"]
                    applicationFee = structure["application_fee"]
                    break
            else:
                interest_rate = 0
                applicationFee = 0
            # Calculate interest
            interestExpected = 0
            installments = schedule_data["installments"]

            # Find the installment with number "26"
            target = next((inst for inst in installments if inst["number"] == str(diff)), None)

            if target:
                settlementAccruedInterest = target['interest']['amount']['expected']
            else:
                print("Installment with number '26' not found.")
            adminFeeBalance = float(loan_data["balances"]["feesBalance"])  
            # Sum all paid fees
            total_fee_paid = sum(inst["fee"]["amount"]["paid"] for inst in installments)

            settlementAccrueadFee = (adminFee * 3) - float(adminFeeBalance) - float(total_fee_paid)
            adminFeeBalance = float(loan_data["balances"]["feesBalance"]) 
            adminFeeBalTotal = float(adminFeeBalance) + float(settlementAccrueadFee) 


            return interestExpected,adminFeeBalTotal,settlementAccruedInterest

    except (ValueError, TypeError) as e:
        logger.error(f"Error calculating interest: {str(e)}")
        raise ValueError('calculation_error').format(str(e))

def getAgentFee(loan_data,fee_expected):
    settlementAccrueadFee=0
    reissuedInterestBal = 0
    reissuedFeeBalance =0
    principalBalance = loan_data['balances']['principalBalance']
    interestBalance = loan_data['balances']['interestBalance']
    adminFeeBalance = loan_data['balances']['feesBalance']

    # print("principalBalance",principalBalance)
    # print("interestBalance",interestBalance)

    # print("adminFeeBalance",adminFeeBalance)

    if fee_expected==6000:
        settlementAccrueadFee = 6000

    agencyFee = float(principalBalance) +float(interestBalance) + float(adminFeeBalance) + float(settlementAccrueadFee) +float(reissuedFeeBalance) +float(reissuedInterestBal)

    # print("dd",float(agencyFee) *0.15 *1.18)
    return float(agencyFee) *0.15 *1.18








