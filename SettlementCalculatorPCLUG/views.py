import base64
import json
import logging
from datetime import datetime, timedelta, date

import requests
from django.contrib import messages
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.clickjacking import xframe_options_exempt

from .forms import PayoffForm
from .utils import (
    get_api_credentials,
    handle_api_response,
    getSalaryAdvanceLoans,
    getLgfBalance,
    calculate_interest,
    getAgentFee
)

logger = logging.getLogger(__name__)

@xframe_options_exempt
@csrf_exempt
def settlement_applicaton_platinumUg(request):
    """
    Handles loan settlement calculation for Platinum Uganda branch.
    Processes signed requests, fetches loan info, calculates settlement amount,
    updates loan custom fields, and displays results or errors to the user.
    """
    try:
        # Get signed request from POST
        # signed_request="10c364c5c219d1b9f893ba431c9972e5d75314c8f953915fd0854ea5eb4f6c5b.eyJET01BSU4iOiJwbGF0aW51bXVnYW5kYS5tYW1idS5jb20iLCJPQkpFQ1RfSUQiOiIxOTAwMTUwNjgiLCJBTEdPUklUSE0iOiJobWFjU0hBMjU2IiwiVEVOQU5UX0lEIjoicGxhdGludW11Z2FuZGEiLCJVU0VSX0tFWSI6IjhhOTM4N2ZlNjg1ZjM1NjIwMTY4NWZhNjQ0NGIwN2NlIn0"
        signed_request = request.POST.get('signed_request', '')
        if not signed_request:
            messages.error(request, "Missing signed request.")
            return render(request, 'settlement_calculator_platinum_ug.html', {'form': PayoffForm()})

        settle_form = PayoffForm(request.POST or None)
        url, api_key = get_api_credentials()

        # Prepare headers for API calls
        headers = {
            'Content-Type': 'application/json',
            'apiKey': api_key,
            'Accept': 'application/vnd.mambu.v2+json'
        }
        headersv1 = headers.copy()
        headersv1['Accept'] = 'application/vnd.mambu.v1+json'

        # Decode signed request and extract user and loan info
        try:
            parts = signed_request.split('.')
            if len(parts) < 2:
                raise ValueError("Invalid signed request format.")

            payload = base64.b64decode(parts[1] + "==")
            user_json = json.loads(payload)
            userid = user_json.get("USER_KEY")
            account_id = user_json.get("OBJECT_ID")

            if not userid or not account_id:
                raise ValueError("Missing USER_KEY or OBJECT_ID.")

        except Exception as e:
            logger.error(f"Signed request decode error: {str(e)}")
            messages.error(request, "Invalid or incomplete signed request.")
            return render(request, 'settlement_calculator_platinum_ug.html', {'form': settle_form})

        # Fetch main loan data
        try:
            loan_data = handle_api_response(
                requests.get(f"{url}loans/{account_id}?detailsLevel=FULL", headers=headers)
            )
        except Exception as e:
            logger.error(f"Loan fetch error: {str(e)}")
            messages.error(request, "Could not retrieve loan details.")
            return render(request, 'settlement_calculator_platinum_ug.html', {'form': settle_form})

        try:
            # Parse disbursement and calculate term
            from datetime import datetime
            import math

            disbursement_date = datetime.strptime(
                loan_data['disbursementDetails']['disbursementDate'], '%Y-%m-%dT%H:%M:%S%z'
            )

            # Get the current date with the same timezone as disbursement_date
            current_date = datetime.now(disbursement_date.tzinfo)

            # Calculate the difference between the current date and disbursement date
            diff = current_date - disbursement_date
            # print("actual",diff)

            # Convert to days, then round up to approximate full months
            diff_in_months = round(diff.days / 30)

            loanTerm = loan_data['scheduleSettings']['repaymentInstallments']
            # print("loan term",loanTerm)
            # print("LOAN DATA",loan_data)

            try:

                computeAgencyFee = loan_data['rei']['external_collector']
                print("computeAgencyFee",computeAgencyFee)
            except Exception as e:
                computeAgencyFee = ''     


            # Get loan schedule and fee expectations
            schedule_data = handle_api_response(
                requests.get(f"{url}loans/{account_id}/schedule", headers=headers)
            )

            # print("schedule_data",schedule_data)
            fee_expected = int(schedule_data['installments'][0]['fee']['amount']['expected'])
            AgencyFee = 0
            if computeAgencyFee:
                AgencyFee = getAgentFee(loan_data,fee_expected)
            else:
                AgecyFee = 0  

            # print("AgencyFee",AgecyFee)  
            try:
                # calculate Reissued interestBal and feeBalance
                refinance_data = loan_data.get('_Refinance_Amount_Loan_Accounts', {})
                reissuedFees = refinance_data.get('reissued_fees_due', 0)
                reissuedInterestBal = refinance_data.get('reissued_interest_balance', 0)

                try:
                    reissuedInterestBalLessIntPaid = max(0, float(reissuedInterestBal) - float(loan_data['balances'].get('interestPaid', 0)))
                except (ValueError, TypeError):
                    reissuedInterestBalLessIntPaid = 0

                try:
                    reissuedFeesLessFeePaid = max(0, float(reissuedFees) - float(loan_data['balances'].get('feesPaid', 0)))
                except (ValueError, TypeError):
                    reissuedFeesLessFeePaid = 0

                # print("reissuedFeesLessFeePaid", reissuedFeesLessFeePaid)
                # print("reissuedInterestBalLessIntPaid", reissuedInterestBalLessIntPaid)

                reissuedFigure = float(reissuedFeesLessFeePaid) + float(reissuedInterestBalLessIntPaid)
            except Exception as e:
                logger.warning(f"Error calculating reissued balances: {e}")
                reissuedFeesLessFeePaid = 0
                reissuedInterestBalLessIntPaid = 0
                reissuedFigure = 0
            # Perform settlement calculations
            interestExpected,adminFeeBalance,settlementAccruedInterest = calculate_interest(loan_data, diff_in_months, fee_expected,loanTerm,schedule_data)
            # print("interestExpected",interestExpected)
            # print("adminFeeBalance",adminFeeBalance)
            # print("settlementAccruedInterest",settlementAccruedInterest)



            lgf_balance = getLgfBalance(loan_data['accountHolderKey'],url,api_key) or 0
            salary_advance = getSalaryAdvanceLoans(loan_data['accountHolderKey'],url,api_key) or 0

            principal_balance = float(loan_data['balances']['principalBalance']) -float(lgf_balance)
            interest_balance = float(loan_data['balances']['interestBalance']) + float(settlementAccruedInterest)
            pay_off_amount = float(principal_balance) + float(interest_balance)+ float(adminFeeBalance) + float(AgencyFee) +float(salary_advance) + float(reissuedFigure)
            settlement_balance = principal_balance + salary_advance 
            # print("pay_off_amount",pay_off_amount)

            # Prepare payload to update loan custom fields
            payload = {
                "customInformation": [
                    {"customFieldID": "SPB01", "value": f"{settlement_balance:,.2f}"},
                    {"customFieldID": "IN001", "value": f"{interest_balance:,.2f}"},
                    {"customFieldID": "AF01", "value": f"{AgencyFee:,.2f}"},
                    {"customFieldID": "crrnt_bal", "value": f"{pay_off_amount:,.2f}"},
                    {"customFieldID": "SGB01", "value": userid},
                    {"customFieldID": "TAF_01", "value": f"{adminFeeBalance:,.2f}"},
                    {"customFieldID": "D_01", "value": diff_in_months},
                    {"customFieldID": "VD01", "value": (date.today() + timedelta(days=14)).strftime('%d-%m-%Y')},
                    {"customFieldID": "G_date", "value": date.today().isoformat()},
                    {"customFieldID": "LGFB01", "value": f"{lgf_balance:,.2f}"},
                    {"customFieldID": "term_01", "value": diff_in_months},
                    {"customFieldID": "settle_approved_by", "value": "Joshua Kasalirwe"},
                    {"customFieldID": "settlement_label", "value": "Approved By:"}
                ]
            }

            # Submit the custom field updates to Mambu
            patch_url = f"{url}loans/{account_id}/custominformation"

            # print("PAYLOAD",payload)
            patch_response = requests.patch(patch_url, json=payload, headers=headersv1)
            # print("patch_response",patch_response.json())

            messages.success(request, f"Loan {account_id} settlement calculated and submitted successfully.")

            return render(request, 'settlement_calculator_platinum_ug.html', {
                'form': settle_form,
                'signed_request': signed_request,
                'userid': userid,
                'account_id': account_id,
                'loan_amount': loan_data['loanAmount'],
                # 'interest': interest,
                'payOffAmouunt': pay_off_amount,
                'disbursement_date': disbursement_date.strftime('%Y-%m-%d'),
                'current_date': current_date.strftime('%Y-%m-%d'),
            })

        except Exception as e:
            logger.error(f"Settlement calculation error: {str(e)}")
            messages.error(request, f"Settlement calculation failed: {str(e)}")

            return render(request, 'settlement_calculator_platinum_ug.html', {'form': settle_form})

    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        messages.error(request, "Unexpected system error. Please try again later.")
        return render(request, 'settlement_calculator_platinum_ug.html', {'form': PayoffForm()})
