from newugandalms.celery import app
from mambu.models import Credential, Environment
from django.core.mail import send_mail, BadHeaderError, EmailMessage

import math
import requests
import json
import xlrd, xlsxwriter
from datetime import datetime
import pickle
import redis
from io import StringIO, BytesIO

def get_client_id(headers, urls, account_id):
    # Use V2 API headers for loan operations with full details
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey']
    }
    # V2 API endpoint 
    geturl = urls + "loans/{0}?detailsLevel=FULL".format(account_id)
    get_clients_details = requests.get(geturl, headers=v2_headers)
    return get_clients_details.json().get('accountHolderKey', '')


def get_lgf_balance(urls, headers, client_id):
    # Use V2 API headers and search endpoint for savings operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
        'Content-Type': 'application/json'
    }
    # V2 API uses search endpoint with filter criteria
    geturl = urls + "deposits:search?detailsLevel=FULL"
    search_payload = {
        "filterCriteria": [
            {
                "field": "accountHolderKey",
                "operator": "EQUALS",
                "value": client_id
            }
        ]
    }
    get_lgf_details = requests.post(geturl, json=search_payload, headers=v2_headers)

    # Process search results
    if get_lgf_details.status_code == 200:
        search_results = get_lgf_details.json()
        for obj in search_results:
            # V2 API: Access balance from nested balances object
            return obj.get('balances', {}).get('totalBalance', 0)
    return ''


def get_lgf_id(urls, headers, client_id, account_id):
    # Use V2 API headers and search endpoint for savings operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
        'Content-Type': 'application/json'
    }
    # V2 API uses search endpoint with filter criteria
    geturl = urls + "deposits:search?detailsLevel=FULL"
    search_payload = {
        "filterCriteria": [
            {
                "field": "accountHolderKey",
                "operator": "EQUALS",
                "value": client_id
            }
        ]
    }
    get_lgf_details = requests.post(geturl, json=search_payload, headers=v2_headers)

    # Process search results
    if get_lgf_details.status_code == 200:
        search_results = get_lgf_details.json()
        for obj in search_results:
            if obj['accountState'] == "ACTIVE":
                # V2 API: Access balance from nested balances object
                current_balance = obj.get('balances', {}).get('totalBalance', 0)
                if int(current_balance) > 0:
                    # TRANSFER PROCESS START
                    tranfer_to_loanaccount = transfer_lgf_amount(account_id, current_balance, obj['id'], urls, headers)
                    close_lgf_account = close_lgf_after_transfer(urls, headers, obj['id'])
                else:
                    close_lgf_account = close_lgf_after_transfer(urls, headers, obj['id'])
            elif obj['accountState'] == "APPROVED":
                # V2 headers for POST operations
                v2_post_headers = {
                    'Accept': 'application/vnd.mambu.v2+json',
                    'ApiKey': headers['ApiKey'],
                    'Content-Type': 'application/json'
                }
                payload = {
                    'type': "withdraw",
                    'notes': "account withdrawn",
                }
                geturl = urls + "deposits/{0}/transactions".format(obj['id'])
                lgf_close_details = requests.post(geturl, json=payload, headers=v2_post_headers)
            else:
                close_lgf_account = close_lgf_after_transfer(urls, headers, obj['id'])


def transfer_lgf_amount(account_id, lgf_balance, lgf_id, urls, headers):
    # Use V2 API headers for loan and savings operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
        'Content-Type': 'application/json'
    }
    
    # check account substatus
    transfer_results = ""
    url = urls + "loans/{0}?detailsLevel=FULL".format(account_id)
    get_account_substatus = requests.get(url, headers=v2_headers)

    if "accountSubState" in get_account_substatus.json():
        if get_account_substatus.json()['accountState'] == 'ACTIVE_IN_ARREARS' or get_account_substatus.json()[
            'accountState'] == 'ACTIVE' and get_account_substatus.json()['accountSubState'] == 'LOCKED':
            # V2 unlock endpoint
            geturl = urls + "loans/{0}/unlock-transactions".format(account_id)
            post_transaction = requests.post(geturl, json={}, headers=v2_headers)
            
            # V2 transfer payload structure
            payload = {
                'amount': lgf_balance,
                'notes': "deposit balance transferred to loan account before write off",
                'transferDetails': {
                    'linkedAccountId': account_id,
                    'linkedAccountType': 'LOAN'
                }
            }

            geturl = urls + "deposits/{0}/transfer-transactions".format(lgf_id)
            post_lgf_balance = requests.post(geturl, json=payload, headers=v2_headers)
            transfer_results = post_lgf_balance.json()
    else:
        # V2 transfer payload structure
        payload = {
            'amount': lgf_balance,
            'notes': "deposit balance transferred to loan account before write off",
            'transferDetails': {
                'linkedAccountId': account_id,
                'linkedAccountType': 'LOAN'
            }
        }

        geturl = urls + "deposits/{0}/transfer-transactions".format(lgf_id)
        post_lgf_balance = requests.post(geturl, json=payload, headers=v2_headers)
        transfer_results = post_lgf_balance.json()

    return transfer_results


def close_lgf_after_transfer(urls, headers, lgf_id):
    # Use V2 API headers for savings operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
        'Content-Type': 'application/json'
    }
    # V2 API uses changeState endpoint
    payload = {
        'action': 'CLOSE',
        'notes': 'account closed',
    }
    geturl = urls + "deposits/{0}:changeState".format(lgf_id)
    lgf_close_details = requests.post(geturl, json=payload, headers=v2_headers)
    return lgf_close_details.json()


def get_write_off(urls, headers, account_id, reason_writeoff):
    # Use V2 API headers for loan write-off operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
        'Content-Type': 'application/json'
    }
    # V2 API uses writeOff endpoint
    payload = {
        "notes": reason_writeoff
    }
    geturl = urls + "loans/{0}:writeOff".format(account_id)
    writeoff_details = requests.post(geturl, json=payload, headers=v2_headers)
    return writeoff_details.json()


def close_written_off_account(urls, headers, account_id):
    # Use V2 API headers for loan operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
        'Content-Type': 'application/json'
    }
    # V2 API uses changeState endpoint
    payload = {
        "action": "CLOSE",
        "notes": "Account closed after writeoff"
    }
    geturl = urls + "loans/{0}:changeState".format(account_id)
    close_account_details = requests.post(geturl, json=payload, headers=v2_headers)
    return close_account_details.json()


def get_transactions(deposit_id, urls, headers):
    # Use V2 API headers for deposit transactions
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey']
    }
    geturl = urls + "deposits/{0}/transactions".format(deposit_id)
    payload = {'offset': 0, 'limit': 1000}

    deposit_details = requests.get(geturl, params=payload, headers=v2_headers)
    return deposit_details.json()


def get_loan_name(urls, headers, account_id):
    # Use V2 API headers for loan operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey']
    }
    geturl = urls + "loans/{0}?detailsLevel=FULL".format(account_id)
    get_loan_details = requests.get(geturl, headers=v2_headers)
    return get_loan_details.json().get('loanName', '')


def get_balance_to_maturity(urls, headers, account_id):
    # Use V2 API headers for loan repayment schedule
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey']
    }
    payload = {'offset': 0, 'limit': 1000}
    geturl = urls + "loans/{0}/schedule".format(account_id)
    get_repayment_schedule = requests.get(geturl, params=payload, headers=v2_headers)
    
    # V2 API response structure: schedule has installments array
    schedule_response = get_repayment_schedule.json()
    total_due = 0
    total_paid = 0
    
    if 'installments' in schedule_response:
        for installment in schedule_response['installments']:
            # V2 structure: nested amount objects
            total_due += float(installment.get('principal', {}).get('amount', {}).get('due', 0))
            total_due += float(installment.get('interest', {}).get('amount', {}).get('due', 0))
            total_due += float(installment.get('fee', {}).get('amount', {}).get('due', 0))
            total_due += float(installment.get('penalty', {}).get('amount', {}).get('due', 0))
            
            total_paid += float(installment.get('principal', {}).get('amount', {}).get('paid', 0))
            total_paid += float(installment.get('interest', {}).get('amount', {}).get('paid', 0))
            total_paid += float(installment.get('fee', {}).get('amount', {}).get('paid', 0))
            total_paid += float(installment.get('penalty', {}).get('amount', {}).get('paid', 0))
    else:
        # Fallback: if response is direct array
        for obj in schedule_response:
            total_due += float(obj.get("principalDue", 0)) + float(obj.get("interestDue", 0)) + float(obj.get("feesDue", 0)) + float(obj.get("penaltyDue", 0))
            total_paid += float(obj.get("principalPaid", 0)) + float(obj.get("interestPaid", 0)) + float(obj.get("feesPaid", 0)) + float(obj.get("penaltyPaid", 0))
    
    return total_due - total_paid


def create_bad_debt_account(urls, headers, client_id, account_id, loan_balance, balance_to_maturity, loan_name):
    # Use V2 API headers for deposit account creation
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
        'Content-Type': 'application/json'
    }

    productTypeKey = "8a181ead51240a790151382031196ea9"
    leo = datetime.today().strftime('%Y-%m-%d')[:10]

    # V2 API payload structure
    bad_debt_payload = {
        "accountHolderKey": client_id,
        "accountHolderType": "CLIENT",
        "productTypeKey": productTypeKey,
        "accountType": "REGULAR_SAVINGS",
        "accountState": "APPROVED",
        "name": "Bad Debt Recovered A/C",
        # V2 custom fields
        "_customFieldValues": {
            "WOD001": str(leo),  # writeoff date
            "WOAID001": account_id,  # written off acc ID
            "WOB001": loan_balance,  # written off balance
            "BTM01": balance_to_maturity,  # balance to maturity
            "WOLN01": loan_name,  # written off loan name
            "BTR01": loan_balance  # balance to recover
        }
    }

    # V2 API endpoint for creating deposits
    geturl = urls + "deposits"
    create_bad_debt_details = requests.post(geturl, json=bad_debt_payload, headers=v2_headers)
    return create_bad_debt_details.json()


def blacklist_client(urls, headers_details, client_id):
    headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers_details['ApiKey']
    }
    v2_payload_blacklist = [{
        "op": "REPLACE",
        "path": "state",
        "value": "BLACKLISTED"}]

    geturl = urls + "clients/{0}".format(client_id)
    blacklist_details = requests.patch(geturl, json=v2_payload_blacklist, headers=headers)
    return blacklist_details.status_code


def check_client_state(client_id, urls, headers):
    # V2 headers for client operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey']
    }
    client_url = urls + "clients/{}?detailsLevel=FULL".format(client_id)
    client_details = requests.get(client_url, headers=v2_headers)

    if client_details.status_code == 200:
        if client_details.json()['state'] == "BLACKLISTED":
            # V2 PATCH payload for state change
            undo_blacklist = [{
                "op": "REPLACE",
                "path": "state",
                "value": "ACTIVE"
            }]
            patch_client_details = requests.patch(client_url, json=undo_blacklist, headers=v2_headers)


def get_custom_field_value(fieldID, entity):
    for custom_field in entity:
        if custom_field["customFieldID"] == fieldID:
            return custom_field["value"]
    return None


from datetime import date

# PREMIERKENYA WRITEOFF VERSION 2 - V2 API
@app.task
def post_writeoff(s_file, mambu_user_email, environment_name, file_name):
    leo = date.today().strftime('%Y-%m-%d')[:10]
    env = Environment.objects.get(url=environment_name)
    urls = "https://premierkenya.sandbox.mambu.com/api/" if environment_name.endswith(
        'sandbox') else 'https://premierkenya.mambu.com/api/'
    auth_user = Credential.objects.get(apikey="ApiKey", environment=env)

    # V2 API headers
    headers = {
        "Accept": "application/vnd.mambu.v2+json",
        auth_user.apikey: auth_user.generatedkey
    }

    start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    file = pickle.loads(s_file)
    emailaddress = mambu_user_email

    receipt_store = redis.StrictRedis(host='localhost', port=6379, db=1)
    receipt_store.config_get('databases')
    receipt_store.flushall()

    file_object = BytesIO()  # create a file-like object
    report = xlsxwriter.Workbook(file_object)
    results_to_post = report.add_worksheet('Post Results')
    results_to_post_count = 0
    results_to_post.write_row(0, 0, ('ACCOUNT ID', 'WRITEOFF RESPONSE', 'BLACKLISTING RESPONSE'))

    receipts_count = 0
    workbook = xlrd.open_workbook(file_contents=file)
    sheet = workbook.sheet_by_index(0)
    for row in range(1, sheet.nrows):
        try:
            account_id = int(sheet.cell(row, 0).value)
        except ValueError:
            account_id = sheet.cell(row, 0).value

        try:
            reason_writeoff = sheet.cell(row, 1).value
        except ValueError:
            reason_writeoff = sheet.cell(row, 1).value
        TaskId = [account_id]

        # GET LOAN NAME BEFORE WRITEOFF
        loan_name = get_loan_name(urls, headers, int(TaskId[0]))

        # GET ACCOUNTHOLDER ENCODEDEDKEY/CLIENT ID
        client_id = get_client_id(headers, urls, int(TaskId[0]))

        # check client state
        client_state = check_client_state(client_id, urls, headers)

        # GET LGF DETAILS using V2 search endpoint
        v2_headers_with_content = {
            "Accept": "application/vnd.mambu.v2+json",
            "ApiKey": auth_user.generatedkey,
            "Content-Type": "application/json"
        }

        # V2 search for deposits by client
        search_payload = {
            "filterCriteria": [
                {
                    "field": "accountHolderKey",
                    "operator": "EQUALS",
                    "value": client_id
                }
            ]
        }
        geturl = urls + "deposits:search?detailsLevel=FULL"
        get_lgf_details = requests.post(geturl, json=search_payload, headers=v2_headers_with_content)

        close_lgf_account = ''
        tranfer_to_loanaccount = ''

        # Process V2 search results
        if get_lgf_details.status_code == 200:
            search_results = get_lgf_details.json()
            for obj in search_results:
                if obj['name'] == "LGF":
                    if obj['accountState'] == "ACTIVE":
                        # V2 API: Access balance from nested balances object
                        current_balance = obj.get('balances', {}).get('totalBalance', 0)
                        if int(float(current_balance)) > 0:
                            lgf_balance = current_balance
                            # TRANSFER PROCESS START
                            tranfer_to_loanaccount = transfer_lgf_amount(int(TaskId[0]), lgf_balance, obj['id'], urls, headers)

                            # CLOSE LGF ACCOUNT AFTER TRANSFER IS DONE
                            close_lgf_account = close_lgf_after_transfer(urls, headers, obj['id'])
                        else:
                            # CLOSE LGF ACCOUNT WHOSE STATE IS ACTIVE WITH ZERO BALANCE
                            close_lgf_account = close_lgf_after_transfer(urls, headers, obj['id'])
                    # WITHDRAW LGF ACCOUNTS WHOSE STATE IS APPROVED
                    elif obj['accountState'] == "APPROVED":
                        lgf_balance = 0
                        payload = {
                            'type': "withdraw",
                            'notes': "account withdrawn",
                        }
                        geturl = urls + "deposits/{0}/transactions".format(obj['id'])
                        lgf_close_details = requests.post(geturl, json=payload, headers=v2_headers_with_content)
                    else:
                        lgf_balance = 0
                        # CLOSE LGF ACCOUNTS WHOSE STATE IS NEITHER ACTIVE NOR APPROVED
                        close_lgf_account = close_lgf_after_transfer(urls, headers, obj['id'])

        # WRITING OFF LOAN ACCOUNT START
        write_off_loan = get_write_off(urls, headers, int(TaskId[0]), reason_writeoff)

        loan_balance = abs(float(write_off_loan["amount"]))

        # V2 API PATCH headers for custom field updates
        v2_patch_headers = {
            "Accept": "application/vnd.mambu.v2+json",
            "ApiKey": auth_user.generatedkey,
            "Content-Type": "application/json"
        }

        # Update Reasons for blacklisting the client and writeoff amounts (V2 API)
        update_blacklist_reasons = [
            {
                "op": "REPLACE",
                "path": "/_customFieldValues/blacklistingReasons",
                "value": reason_writeoff
            },
            {
                "op": "REPLACE",
                "path": "/_customFieldValues/writeOffBal",
                "value": loan_balance
            },
            {
                "op": "REPLACE",
                "path": "/_customFieldValues/loanProduct",
                "value": loan_name
            },
            {
                "op": "REPLACE",
                "path": "/_customFieldValues/dateBlacklisted",
                "value": str(leo)
            },
            {
                "op": "REPLACE",
                "path": "/_customFieldValues/blacklistingOrigin",
                "value": "Premier Credit"
            }
        ]

        geturl = urls + "clients/{0}".format(client_id)
        update_blacklist_details = requests.patch(geturl, json=update_blacklist_reasons, headers=v2_patch_headers)

        # GET BALANCE TO MATURITY
        balance_to_maturity = get_balance_to_maturity(urls, headers, int(TaskId[0]))

        # CREATE A BAD DEBT RECOVERED ACCOUNT AND PATCH RESPECTIVE CUSTOMFIELDS
        bad_debt_details = create_bad_debt_account(urls, headers, client_id, int(TaskId[0]), loan_balance,
                                                   balance_to_maturity, loan_name)

        # BLACKLIST CLIENT AFTER CREATION OF BAD DEBT ACCOUNT
        blacklist_details = blacklist_client(urls, headers, client_id)

        try:
            writeoff_status = write_off_loan['returnStatus']
        except KeyError as e:
            writeoff_status = ''

        try:
            blacklist_status = blacklist_details
        except KeyError as e:
            blacklist_status = ''

        receipts_count += 1
        receipt_store.lpush(TaskId[0], '{writeoff_status,blacklist_status}')

        count = 0
        for key in receipt_store.scan_iter(TaskId[0]):
            count = count + 1
            results_to_post_count = results_to_post_count + 1

            dat = receipt_store.lrange(key, 0, -1)
            for row in dat:
                row_values = eval(row)
                if writeoff_status in row_values:
                    redis_writeoff_status = writeoff_status
                if blacklist_status in row_values:
                    redis_blacklist_status = blacklist_status

            data = (TaskId[0], redis_writeoff_status, redis_blacklist_status)
            results_to_post.write_row(results_to_post_count, 0, data)

    report.close()
    # send email
    try:
        emailaddresses = ['<EMAIL>']
        cc = ['<EMAIL>']

        emailaddresses.append(emailaddress)

        email = EmailMessage('Writeoff Report',
                             'Please find attached report ' + '\n Start time :' + start_time + '\n Finished: ' + datetime.now().strftime(
                                 '%Y-%m-%d %H:%M:%S'), '<EMAIL>', emailaddresses + cc)
        email.content_subtype = "html"
        email.attach('Results for ' + file_name, file_object.getvalue(),
                     'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        emailsent = email.send()
    except Exception as e:
        print("ERROREMAILMESSAGE: ", e)


@app.task
def deposit_activities(deposit_id, environment):
    env = Environment.objects.get(url=environment)
    urls = "https://premierkenya.sandbox.mambu.com/api/" if environment.endswith(
        'sandbox') else 'https://premierkenya.mambu.com/api/'
    auth_user = Credential.objects.get(apikey="ApiKey", environment=env)

    # V2 API headers
    headers = {
        "Accept": "application/vnd.mambu.v2+json",
        auth_user.apikey: auth_user.generatedkey
    }

    # GET TOTAL DEPOSITS AND DEPOSIT ADJUSTMENTS
    total_depos = get_transactions(deposit_id, urls, headers)
    total_deposits = 0
    total_adjustment = 0
    total_withdrawals = 0
    total_withdrawaw_adjustment = 0
    for obj in total_depos:
        if obj['type'] == "DEPOSIT":
            total_deposits += float(obj['amount'])
        elif obj['type'] == "ADJUSTMENT":
            total_adjustment += float(obj['amount'])
        elif obj['type'] == "WITHDRAWAL":
            total_withdrawals += float(obj['amount'])
        elif obj['type'] == "WITHDRAWAL_ADJUSTMENT":
            total_withdrawaw_adjustment += float(obj['amount'])
        else:
            pass
    actual_depo_diff = abs(total_deposits) - abs(total_adjustment) - abs(total_withdrawals) + abs(
        total_withdrawaw_adjustment)

    actual_depo_amount = abs(actual_depo_diff)

    # V2 API headers for PATCH operations
    v2_patch_headers = {
        "Accept": "application/vnd.mambu.v2+json",
        "ApiKey": auth_user.generatedkey,
        "Content-Type": "application/json"
    }

    # GET TOTAL AMOUNT RECOVERED AND UPDATE WITH DEPOSIT AMOUNT (V2 API)
    # First get the deposit account to access custom fields
    deposit_url = urls + "deposits/{0}?detailsLevel=FULL".format(deposit_id)
    deposit_details = requests.get(deposit_url, headers=headers)

    # Extract current amount recovered value
    amt_recovered = 0
    writeoff_bal = 0
    if deposit_details.status_code == 200:
        deposit_data = deposit_details.json()
        if '_customFieldValues' in deposit_data:
            amt_recovered = deposit_data['_customFieldValues'].get('AR01', 0) or 0
            writeoff_bal = deposit_data['_customFieldValues'].get('WOB001', 0) or 0

    # V2 API PATCH payload for updating custom fields
    amount_recoverd_payload = [
        {
            "op": "REPLACE",
            "path": "/_customFieldValues/AR01",
            "value": actual_depo_amount
        }
    ]

    geturl = urls + "deposits/{0}".format(deposit_id)
    patch_amount_recovered = requests.patch(geturl, json=amount_recoverd_payload, headers=v2_patch_headers)

    # V2 API PATCH payload for balance to recover
    bal_recover_payload = [
        {
            "op": "REPLACE",
            "path": "/_customFieldValues/BTR01",
            "value": float(writeoff_bal) - float(actual_depo_amount)
        }
    ]

    geturl = urls + "deposits/{0}".format(deposit_id)
    bal_recover_details = requests.patch(geturl, json=bal_recover_payload, headers=v2_patch_headers)

    # Get new balance to recover
    new_bal_recover = float(writeoff_bal) - float(actual_depo_amount)

    # V2 API recovery status updates
    if float(new_bal_recover) > 0.0:
        partially_recovered_payload = [
            {
                "op": "REPLACE",
                "path": "/_customFieldValues/RS01",
                "value": "Partially Recovered"
            }
        ]
        geturl = urls + "deposits/{0}".format(deposit_id)
        full_recovery_details = requests.patch(geturl, json=partially_recovered_payload, headers=v2_patch_headers)
    elif float(new_bal_recover) <= 0.0:
        fully_recovered_payload = [
            {
                "op": "REPLACE",
                "path": "/_customFieldValues/RS01",
                "value": "Fully Recovered"
            }
        ]
        geturl = urls + "deposits/{0}".format(deposit_id)
        partial_recovery_details = requests.patch(geturl, json=fully_recovered_payload, headers=v2_patch_headers)

    if actual_depo_amount == 0.0:
        # V2 API: Remove custom field by setting to null
        delete_field_payload = [
            {
                "op": "REMOVE",
                "path": "/_customFieldValues/RS01"
            }
        ]
        geturl = urls + "deposits/{0}".format(deposit_id)
        field_delete = requests.patch(geturl, json=delete_field_payload, headers=v2_patch_headers)
        data = field_delete.json()
        print('DATA', data)
