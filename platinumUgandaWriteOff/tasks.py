import configparser

from newugandalms.celery import app
from mambu.models import Credential, Environment
from datetime import datetime, date, timedelta
import time
from .models import UploadedilesWriteOffPlatug,WriteOffDetailsPlatUg
# from django.db import transaction
import requests
import json
import datetime

def get_client_id(headers,urls,account_id):
    # Use V2 API headers for loan operations with full details
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey']
    }
    # V2 API endpoint 
    geturl = urls + "loans/{0}?detailsLevel=FULL".format(account_id)
    # print('client url',geturl)
    get_clients_details = requests.get(geturl, headers=v2_headers)

    # print('details',get_clients_details.json())
    return get_clients_details.json().get('accountHolderKey','')

def get_lgf_balance(urls,headers,client_id):
    # Use V2 API headers and search endpoint for savings operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
    }
    # V2 API uses search endpoint with filter criteria
    geturl = urls + "deposits:search?detailsLevel=FULL"
    search_payload = {
        "filterCriteria": [
            {
                "field": "accountHolderKey",
                "operator": "EQUALS",
                "value": client_id
            }
        ]
    }
    get_lgf_details = requests.post(geturl, json=search_payload, headers=v2_headers)

    # Process search results
    if get_lgf_details.status_code == 200:
        search_results = get_lgf_details.json()
        for obj in search_results:
            # V2 API: Access balance from nested balances object
            return obj.get('balances', {}).get('totalBalance', 0)
    return 0


def get_lgf_id(urls,headers,client_id,account_id):
    # Use V2 API headers and search endpoint for savings operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
    }
    # V2 API uses search endpoint with filter criteria
    geturl = urls + "deposits:search?detailsLevel=FULL"
    search_payload = {
        "filterCriteria": [
            {
                "field": "accountHolderKey",
                "operator": "EQUALS",
                "value": client_id
            }
        ]
    }
    get_lgf_details = requests.post(geturl, json=search_payload, headers=v2_headers)

    # Process search results
    if get_lgf_details.status_code == 200:
        search_results = get_lgf_details.json()
        for obj in search_results:
            if obj['accountState'] =="ACTIVE":
                # V2 API: Access balance from nested balances object
                current_balance = obj.get('balances', {}).get('totalBalance', 0)
                if int(current_balance) > 0:
                    # TRANSFER PROCESS START
                    tranfer_to_loanaccount=transfer_lgf_amount(account_id,current_balance,obj['id'],urls,headers)
                    close_lgf_account=close_lgf_after_transfer(urls,headers,obj['id'])
                else:
                    close_lgf_account=close_lgf_after_transfer(urls,headers,obj['id'])
            elif obj['accountState'] == "APPROVED":
                payload = {
                    'type': "withdraw",
                    'notes': "account withdrawn",
                }
                geturl = urls + "deposits/{0}/transactions".format(obj['id'])
                lgf_close_details = requests.post(geturl, json=payload, headers=headers)
                # return lgf_close_details.json()
            else:
                close_lgf_account = close_lgf_after_transfer(urls, headers, obj['id'])

def transfer_lgf_amount(account_id,lgf_balance,lgf_id,urls,headers):
    # Use V2 API headers for loan and savings operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
    }
    # check account substatus using V2 endpoint
    transfer_results = ""
    url = urls + "loans/{0}?detailsLevel=FULL".format(account_id)
    get_account_substatus = requests.get(url, headers=v2_headers)

    if "accountSubState" in get_account_substatus.json():
        if get_account_substatus.json()['accountState'] =='ACTIVE_IN_ARREARS'  or get_account_substatus.json()['accountState'] =='ACTIVE' and get_account_substatus.json()['accountSubState'] =='LOCKED':
            # V2 unlock endpoint
            unlock_payload = {}
            geturl = urls + "loans/{0}/unlock-transactions".format(account_id)
            post_transaction = requests.post(geturl, json=unlock_payload, headers=v2_headers)
            print("post_transaction",post_transaction.json())

            # V2 transfer payload structure
            payload = {
                'amount': lgf_balance,
                'notes': "deposit balance transferred to loan account before write off",
                'transferDetails': {
                    'linkedAccountId': account_id,
                    'linkedAccountType': 'LOAN'
                }
            }
             

            print("Paylod",payload)
            # V2 transfer endpoint
            geturl = urls + "deposits/{0}/transfer-transactions".format(lgf_id)
            print("url",geturl)

            post_lgf_balance = requests.post(geturl, json=payload, headers=v2_headers)
            transfer_results = post_lgf_balance.json()
    else:
        # V2 transfer payload structure
        payload = {
            'amount': lgf_balance,
            'notes': "deposit balance transferred to loan account before write off",
            'transferDetails': {
                'linkedAccountId': account_id,
                'linkedAccountType': 'LOAN'
            }
        }
        print("Paylod1", payload)

        # V2 transfer endpoint
        geturl = urls + "deposits/{0}/transfer-transactions".format(lgf_id)
        print("url", geturl)

        post_lgf_balance = requests.post(geturl, json=payload, headers=v2_headers)
        transfer_results = post_lgf_balance.json()

    return transfer_results

def close_lgf_after_transfer(urls,headers,lgf_id):
    # Use V2 API headers for savings operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
    }
    # V2 changeState payload structure
    payload = {
        'action': "CLOSE",
        'notes': "Account closed after balance transfer",
    }
    # V2 changeState endpoint
    geturl = urls + "deposits/{0}:changeState".format(lgf_id)
    lgf_close_details = requests.post(geturl, json=payload, headers=v2_headers)
    return lgf_close_details.json()

def get_write_off(urls,headers,account_id,reason_writeoff):
    # Use V2 API headers for loan operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
    }

    # First, check the current loan state
    loan_url = urls + "loans/{0}?detailsLevel=FULL".format(account_id)
    loan_details = requests.get(loan_url, headers=v2_headers)

    if loan_details.status_code == 200:
        loan_data = loan_details.json()
        current_state = loan_data.get('accountState', '')
        print(f"Current loan state: {current_state}")

        # Check if loan is already written off or closed
        if current_state in ['CLOSED_WRITTEN_OFF', 'CLOSED']:
            print(f"Loan is already in {current_state} state - cannot write off")
            return {
                "error": "INVALID_STATE_TRANSITION",
                "message": f"Loan is already {current_state}",
                "current_state": current_state,
                "returnStatus": "LOAN_ALREADY_PROCESSED"
            }

        # Check if loan is in a valid state for write-off
        valid_states = ['ACTIVE', 'ACTIVE_IN_ARREARS', 'PARTIAL_APPLICATION']
        if current_state not in valid_states:
            print(f"Loan state {current_state} is not valid for write-off. Valid states: {valid_states}")
            return {
                "error": "INVALID_STATE_TRANSITION",
                "message": f"Loan state {current_state} cannot be written off",
                "current_state": current_state,
                "valid_states": valid_states,
                "returnStatus": "INVALID_LOAN_STATE"
            }

    # V2 writeOff payload structure
    payload = {
        "notes": reason_writeoff
    }

    # V2 writeOff endpoint
    geturl = urls + "loans/{0}:writeOff".format(account_id)
    print(f"Write-off URL: {geturl}")
    print(f"Write-off Payload: {payload}")

    writeoff_details = requests.post(geturl, json=payload, headers=v2_headers)

    print(f"Write-off Status Code: {writeoff_details.status_code}")
    print(f"Write-off Raw Response: {writeoff_details.text}")

    # Check if the request was successful (204 is success for write-off operations)
    if writeoff_details.status_code in [200, 201, 202, 204]:
        try:
            if writeoff_details.text:  # Check if there's response content
                response_data = writeoff_details.json()
                response_data['returnStatus'] = 'SUCCESS'
                return response_data
            else:
                # 204 No Content - successful write-off
                return {"message": "Write-off successful", "returnStatus": "SUCCESS", "status_code": 204}
        except ValueError as e:
            print(f"JSON parsing error: {e}")
            # For 204 responses, this is normal - no JSON content
            if writeoff_details.status_code == 204:
                return {"message": "Write-off successful", "returnStatus": "SUCCESS", "status_code": 204}
            return {"error": "Invalid JSON response", "raw_response": writeoff_details.text, "returnStatus": "JSON_ERROR"}
    else:
        print(f"API Error - Status: {writeoff_details.status_code}")
        try:
            error_response = writeoff_details.json()
            # Handle specific error codes
            if 'errors' in error_response:
                for error in error_response['errors']:
                    if error.get('errorCode') == 116 and error.get('errorReason') == 'INVALID_STATE_TRANSITION':
                        return {
                            "error": "INVALID_STATE_TRANSITION",
                            "message": "Loan cannot be written off in current state",
                            "status_code": writeoff_details.status_code,
                            "details": error_response,
                            "returnStatus": "INVALID_STATE_TRANSITION"
                        }

            return {
                "error": "API request failed",
                "status_code": writeoff_details.status_code,
                "details": error_response,
                "returnStatus": "API_ERROR"
            }
        except ValueError:
            return {
                "error": "API request failed",
                "status_code": writeoff_details.status_code,
                "raw_response": writeoff_details.text,
                "returnStatus": "API_ERROR"
            }

def close_written_off_account(urls,headers,account_id):
    # Use V2 API headers for loan operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
    }
    # V2 changeState payload structure
    payload={
        "action": "CLOSE",
        "notes": "Account closed after write-off"
    }
    # V2 changeState endpoint
    geturl = urls + "loans/{0}:changeState".format(account_id)
    close_account_details = requests.post(geturl, json=payload, headers=v2_headers)
    return close_account_details.json()

def get_loan_balance(urls,headers,account_id):
    # Use V2 API headers for loan operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey']
    }
    geturl = urls + "loans/{0}?detailsLevel=FULL".format(account_id)
    get_loan_details = requests.get(geturl, headers=v2_headers)
    # V2 API: Access balance fields from nested balances object
    loan_data = get_loan_details.json()
    balances = loan_data.get('balances', {})

    principal_balance = float(balances.get('principalBalance', 0))
    interest_balance = float(balances.get('interestBalance', 0))
    fees_balance = float(balances.get('feesBalance', 0))
    penalty_balance = float(balances.get('penaltyBalance', 0))
    accrued_interest = float(loan_data.get('accruedInterest', 0))

    total_bal = principal_balance + interest_balance + fees_balance + penalty_balance + accrued_interest
    return total_bal


def get_transactions(deposit_id,urls,headers):
    # Use V2 API headers for deposit transactions
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey']
    }
    geturl = urls + "deposits/{0}/transactions".format(deposit_id)
    # print('get url',geturl)
    payload = {'offset': 0, 'limit': 1000}

    # payload = {'offset': "0", 'limit': "1000"}

    deposit_details = requests.get(geturl,params=payload, headers=v2_headers)
    # print('json',deposit_details.json())
    return deposit_details.json()

def get_loan_name(urls,headers,account_id):
    # Use V2 API headers for loan operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey']
    }
    geturl = urls + "loans/{0}?detailsLevel=FULL".format(account_id)
    # print("Urls",geturl)
    get_loan_details = requests.get(geturl, headers=v2_headers)
    # print("loanCheck",get_loan_details.json())
    return get_loan_details.json()

def get_balance_to_maturity(urls,headers,account_id):
    # Use V2 API headers for loan repayment schedule
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey']
    }
    payload = {'offset': 0, 'limit': 1000}
    geturl = urls + "loans/{0}/schedule".format(account_id)
    get_repayment_schedule = requests.get(geturl,params=payload, headers=v2_headers)

    # V2 API response structure: schedule has installments array
    schedule_response = get_repayment_schedule.json()
    total_due = 0
    total_paid = 0

    if 'installments' in schedule_response:
        for installment in schedule_response['installments']:
            # V2 structure: nested amount objects
            total_due += float(installment.get('principal', {}).get('amount', {}).get('due', 0))
            total_due += float(installment.get('interest', {}).get('amount', {}).get('due', 0))
            total_due += float(installment.get('fee', {}).get('amount', {}).get('due', 0))
            total_due += float(installment.get('penalty', {}).get('amount', {}).get('due', 0))

            total_paid += float(installment.get('principal', {}).get('amount', {}).get('paid', 0))
            total_paid += float(installment.get('interest', {}).get('amount', {}).get('paid', 0))
            total_paid += float(installment.get('fee', {}).get('amount', {}).get('paid', 0))
            total_paid += float(installment.get('penalty', {}).get('amount', {}).get('paid', 0))
    else:
        # Fallback: if response is direct array
        for obj in schedule_response:
            total_due += float(obj.get("principalDue", 0)) + float(obj.get("interestDue", 0)) + float(obj.get("feesDue", 0)) + float(obj.get("penaltyDue", 0))
            total_paid += float(obj.get("principalPaid", 0)) + float(obj.get("interestPaid", 0)) + float(obj.get("feesPaid", 0)) + float(obj.get("penaltyPaid", 0))

    return total_due - total_paid


def create_bad_debt_account(urls ,headers,client_id,account_id, new_loan_balance, balance_to_maturity,loan_name,lgf_balance):
    # Use V2 API headers for deposit account creation
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
    }

    productTypeKey = "8a9387516531a6ac0165328b1c2b1c5e"
    leo = datetime.datetime.today().strftime('%Y-%m-%d')[:10]

    # V2 API payload structure
    bad_debt_payload = {
        "accountHolderKey": client_id,
        "accountHolderType": "CLIENT",
        "productTypeKey": productTypeKey,
        "accountType": "REGULAR_SAVINGS",
        "accountState": "APPROVED",
        "name": "Bad Debt Recovered A/C",
        "_WOD01": {
            "WOD_01": str(leo),  # Written-off date
            "WLO_01": str(account_id),  # Written off loan ID
            "WOB_01": float(new_loan_balance),  # Written-off balance
            "BM_01": float(balance_to_maturity),  # Balance maturity
            "written_off_loan_name": str(loan_name),  # Loan name
            # "balance_to_recover": float(balance_to_maturity),  # Balance to recover
            "_AM_RC_01": 0  # Amount recovered
        }
    }

    # V2 API endpoint for creating deposits
    geturl = urls + "deposits"

    print(f"Bad Debt Creation URL: {geturl}")
    print(f"Bad Debt Headers: {v2_headers}")
    print(f"Bad Debt Payload: {bad_debt_payload}")

    create_bad_debt_details = requests.post(geturl, json=bad_debt_payload, headers=v2_headers)

    print(f"Bad Debt Status Code: {create_bad_debt_details.status_code}")
    print(f"Bad Debt Response Headers: {create_bad_debt_details.headers}")
    print(f"Bad Debt Raw Response: {create_bad_debt_details.text}")

    # Check if the request was successful
    if create_bad_debt_details.status_code in [200, 201, 202]:
        try:
            response_data = create_bad_debt_details.json()
            print(f"Bad Debt Account Created Successfully: {response_data.get('id', 'No ID returned')}")
            return response_data
        except ValueError as e:
            print(f"Bad Debt JSON parsing error: {e}")
            return {"error": "Invalid JSON response", "raw_response": create_bad_debt_details.text}
    else:
        print(f"Bad Debt API Error - Status: {create_bad_debt_details.status_code}")
        try:
            error_response = create_bad_debt_details.json()
            print(f"Bad Debt Error Details: {error_response}")
            return {"error": "Bad debt creation failed", "status_code": create_bad_debt_details.status_code, "details": error_response}
        except ValueError:
            return {"error": "Bad debt creation failed", "status_code": create_bad_debt_details.status_code, "raw_response": create_bad_debt_details.text}

def blacklist_client(urls,headers_details,client_id,reasons_blacklisting):
    headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey':headers_details['ApiKey']
    }
    geturl = urls + "clients/{0}".format(client_id)

    v2_payload_updateField=[
      {
        "op": "ADD",
        "path": "/_BC01/BR001",
        "value": str(reasons_blacklisting)
      }
    ]

    updateResults = requests.patch(geturl,json=v2_payload_updateField, headers=headers)

    # print("update Blacklist Reasons",updateResults.status_code)

    v2_payload_blacklist=[{
          "op": "REPLACE",
          "path": "state",
          "value": "BLACKLISTED"}]
    # client_blacklist_payload = {
    #     "client":
    #         {
    #             "state": "BLACKLISTED"
    #         }
    # }

    # print('url',geturl)
    blacklist_details = requests.patch(geturl,json=v2_payload_blacklist, headers=headers)

    if blacklist_details.status_code in ["400","401"]:
        return blacklist_details.json()
    else:
        return blacklist_details.status_code

# check client state
def check_client_state(client_id,urls,headers):
    client_url = urls + "clients/{}?detailsLevel=FULL".format(client_id)
    client_details = requests.get(client_url, headers=headers)

    try:
        if client_details.json()['client']['state'] == "BLACKLISTED":
            undo_blacklist={
                "client":
                    {
                        "state": "ACTIVE"
                    }
            }

            patch_client_details = requests.patch(client_url, json=undo_blacklist, headers=headers)
        else:
            return client_details.json()['client']['state']
            # print("clientPatch",patch_client_details.json())
    except Exception:
        pass

def get_custom_field_value(fieldID, entity):
    for custom_field in entity:
        if custom_field["customFieldID"] == fieldID:
            return custom_field["value"]
    return None

@app.task
def approve_writeoff(account_id,environment, reason_writeoff, writeoff_id, req, user,reasons_blacklisting):
    urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment.endswith(
        'sandbox') else 'https://platinumuganda.mambu.com/api/'
    print("environment",environment)
    print("urls",urls)
    config = configparser.ConfigParser()
    config.read('secrets.ini')
    # bulkWriteOff = config.get('Credentials', 'PLATINUMUGANDA_PROD_APIKEY_BULKWRITEOFF')
    bulkWriteOff = config.get('Credentials', 'PLATINUMUGANDA_SANDBOX_APIKEY_CUSTOMFIELDPATCH')

    # V2 API headers
    headers = {
        "Accept": "application/vnd.mambu.v2+json",
        "ApiKey": "{}".format(bulkWriteOff)
    }
    # GET LOAN NAME BEFORE WRITEOFF
    # print("environment",environment)
    loanResponse = get_loan_name(urls, headers, account_id)

    print("Res",loanResponse)
    loan_name= loanResponse.get('loanName','')
    client_id = loanResponse.get('accountHolderKey','')


    # check client state
    client_state = check_client_state(client_id, urls, headers)
    print("clientState",client_state)

    # GET LGF ID's using V2 search endpoint
    v2_headers_with_content = {
        "Accept": "application/vnd.mambu.v2+json",
        "ApiKey": "{}".format(bulkWriteOff),
    }

    # V2 search for deposits by client
    search_payload = {
        "filterCriteria": [
            {
                "field": "accountHolderKey",
                "operator": "EQUALS",
                "value": client_id
            }
        ]
    }
    geturl = urls + "deposits:search?detailsLevel=FULL"
    get_lgf_details = requests.post(geturl, json=search_payload, headers=v2_headers_with_content)
    print("get_lgf_details",get_lgf_details.json())
    lgf_balance = ''

    # Process V2 search results
    print(f"LGF Search Status Code: {get_lgf_details.status_code}")
    print(f"LGF Search Response: {get_lgf_details.text}")

    if get_lgf_details.status_code == 200:
        search_results = get_lgf_details.json()
        print(f"LGF Search Results Count: {len(search_results)}")
    elif get_lgf_details.status_code == 204:
        print("No LGF accounts found for this client (204 No Content)")
        search_results = []
    else:
        print(f"LGF Search failed with status: {get_lgf_details.status_code}")
        search_results = []

    # Process search results if any exist
    for obj in search_results:
            if obj['accountState'] =="ACTIVE" and obj['name'] != "Bad Debt Recovered A/C":
                # V2 API: Access balance from nested balances object
                current_balance = obj.get('balances', {}).get('totalBalance', 0)
                if int(float(current_balance)) > 0:
                    lgf_balance = current_balance
                    # TRANSFER PROCESS START
                    tranfer_to_loanaccount=transfer_lgf_amount(account_id,lgf_balance,obj['id'],urls,headers)

                    print("TRANSFER RES",tranfer_to_loanaccount)

                    # CLOSE LGF ACCOUNT AFTER TRANSFER IS DONE
                    close_lgf_account=close_lgf_after_transfer(urls,headers,obj['id'])
                elif int(float(current_balance)) == 0:
                    # CLOSE LGF ACCOUNT WHOSE STATE IS ACTIVE WITH ZERO BALANCE
                    close_lgf_account=close_lgf_after_transfer(urls,headers,obj['id'])
            # WITHDRAW LGF ACCOUNTS WHOSE STATE IS APPROVED
            elif obj['accountState'] == "APPROVED" and obj['name'] != "Bad Debt Recovered A/C":
                lgf_balance=0
                payload = {
                    'type': "withdraw",
                    'notes': "account withdrawn",
                }
                # V2 endpoint for deposit transactions
                geturl = urls + "deposits/{0}/transactions".format(obj['id'])
                lgf_close_details = requests.post(geturl, json=payload, headers=v2_headers_with_content)
            else:
                lgf_balance=0
                #CLOSE LGF ACCOUNTS WHOSE STATE IS NEITHER ACTIVE NOR APPROVED
                close_lgf_account = close_lgf_after_transfer(urls, headers, obj['id'])

    # GET LOAN BALANCE BEFORE WRITEOFF
    loan_balance = get_loan_balance(urls,headers,account_id)
    # new_loan_balance = int(round(float(loan_balance,2)))
    new_loan_balance = round(float(loan_balance), 2)
    # print("new_loan_balance",loan_balance)
    # WRITING OFF LOAN ACCOUNT START
    write_off_loan=get_write_off(urls,headers,account_id,reason_writeoff)

    print("write_off_loan",write_off_loan)

    # GET BALANCE TO MATUrity
    balance_to_maturity1 = get_balance_to_maturity(urls, headers, account_id)

    balance_to_maturity = str(round(balance_to_maturity1, 2))

    # CREATE A BAD DEBT RECOVERED ACCOUNT AND PATCH RESPECTIVE CUSTOMFIELDS(Write of Date,Written Off Loan,Write off balance,Balance to Maturity)
    bad_debt_details = create_bad_debt_account(urls,headers,client_id,account_id,new_loan_balance,balance_to_maturity,loan_name,lgf_balance)
    # print("bad_debt_details",bad_debt_details)

    # BLACKLIST CLIENT AFTER CREATION OF BAD DEBT ACCOUNT
    blacklist_details = blacklist_client(urls,headers,client_id,reasons_blacklisting)

    # # UPDATE STATUS AFTER WRITEOFF PROCESS
    writeoff_status = WriteOffDetailsPlatUg.objects.get(id=writeoff_id)
    writeoff_status.status = 'Approved'
    writeoff_status.write_off_loan_response = write_off_loan
    writeoff_status.badDebt_details_response = bad_debt_details
    writeoff_status.blacklistDetails_response = blacklist_details
    # Handle user data safely
    if user and isinstance(user, dict) and "firstName" in user and "lastName" in user:
        writeoff_status.approved_rejected_by = user["firstName"] + " " + user["lastName"]
    else:
        writeoff_status.approved_rejected_by = str(user) if user else "System User"
    writeoff_status.save()

    #UPDATING THE UPLOADED FILES STATUS
    objs=UploadedilesWriteOffPlatug.objects.get(id=req)
    objs.status="Approved"
    objs.save()

@app.task
def reject_writeoff(environment, writeoff_id, req, user):
    urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment.endswith(
        'sandbox') else 'https://platinumuganda.mambu.com/api/'

    # config auth method
    config = configparser.ConfigParser()
    config.read('secrets.ini')
    # bulkWriteOff = config.get('Credentials', 'PLATINUMUGANDA_PROD_APIKEY_BULKWRITEOFF')
    bulkWriteOff = config.get('Credentials', 'PLATINUMUGANDA_SANDBOX_APIKEY_CUSTOMFIELDPATCH')
    headers = {
        "Accept": "application/vnd.mambu.v2+json",
        "apiKey": "{}".format(bulkWriteOff)
    }

    # UPDATE STATUS AFTER WRITEOFF PROCESS
    writeoff_status = WriteOffDetailsPlatUg.objects.get(id=writeoff_id)
    writeoff_status.status = 'Rejected'
    writeoff_status.approved_rejected_by = user["firstName"] + " " + user["lastName"]
    writeoff_status.save()

    #UPDATING THE UPLOADED FILES STATUS
    objs=UploadedilesWriteOffPlatug.objects.get(id=req)
    objs.status="Rejected"
    objs.save()


@app.task
def deposit_activities(deposit_id, environment):
    env = Environment.objects.get(url=environment)
    urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment.endswith(
        'sandbox') else 'https://platinumuganda.mambu.com/api/'
    # config auth method
    config = configparser.ConfigParser()
    config.read('secrets.ini')
    # bulkWriteOff = config.get('Credentials', 'PLATINUMUGANDA_PROD_APIKEY_BULKWRITEOFF')
    bulkWriteOff = config.get('Credentials', 'PLATINUMUGANDA_SANDBOX_APIKEY_CUSTOMFIELDPATCH')
    # V2 API headers
    headers = {
        "Accept": "application/vnd.mambu.v2+json",
        "ApiKey": "{}".format(bulkWriteOff)
    }
    # GET TOTAL DEPOSITS AND DEPOSIT ADJUSTMENTS
    total_depos = get_transactions(deposit_id,urls,headers)
    # print("DEPOSIT ACCOUNT",deposit_id)
    # print("TRANSACTIONS WRITEOFF",total_depos)


    total_deposits = 0
    total_adjustment = 0
    total_withdrawals = 0
    total_withdrawaw_adjustment = 0
    for obj in total_depos:
        if obj['type'] == "DEPOSIT":
            total_deposits += float(obj['amount'])
        elif obj['type'] == "ADJUSTMENT":
            total_adjustment += float(obj['amount'])
        elif obj['type'] == "WITHDRAWAL":
            total_withdrawals += float(obj['amount'])
        elif obj['type'] == "WITHDRAWAL_ADJUSTMENT":
            total_withdrawaw_adjustment += float(obj['amount'])
        else:
            pass
    actual_depo_diff = abs(total_deposits)-abs(total_adjustment) - abs(total_withdrawals) + abs(total_withdrawaw_adjustment)
    # print("TOTAL DEPOSIT",total_deposits)
    # print("TOTAL ADJUSTMENT",total_adjustment)
    # print("TOTAL WITHDRAWAL",total_withdrawals)
    # print("TOTAL WITHDRAWAL_ADJUSTMENT",total_withdrawaw_adjustment)


    # print("ACTUAL DEPO",actual_depo_diff)
    # print("total_deposits",total_deposits)
    # print("total_adjustment",total_adjustment)
    # print("total_withdrawals",total_withdrawals)

    actual_depo_amount = abs(actual_depo_diff)
    print('actual_depo_diff',actual_depo_diff)


    # V2 API headers for PATCH operations
    v2_patch_headers = {
        "Accept": "application/vnd.mambu.v2+json",
        "ApiKey": "{}".format(bulkWriteOff),
  
    }

    # GET TOTAL AMOUNT RECOVERED AND UPDATE WITH DEPOSIT AMOUNT (V2 API)
    # First get the deposit account to access custom fields
    deposit_url = urls + "deposits/{0}?detailsLevel=FULL".format(deposit_id)
    deposit_details = requests.get(deposit_url, headers=headers)

    # Extract current amount recovered value
    amt_recovered = 0
    if deposit_details.status_code == 200:
        deposit_data = deposit_details.json()
        if '_customFieldValues' in deposit_data and '_AM_RC_01' in deposit_data['_customFieldValues']:
            amt_recovered = deposit_data['_customFieldValues']['_AM_RC_01'] or 0

    # V2 API PATCH payload for updating custom fields
    amount_recoverd_payload = [
        {
            "op": "REPLACE",
            "path": "/_customFieldValues/_AM_RC_01",
            "value": int(actual_depo_amount)
        }
    ]

    geturl = urls + "deposits/{0}".format(deposit_id)
    patch_amount_recovered = requests.patch(geturl, json=amount_recoverd_payload, headers=v2_patch_headers)

    print("AMOUNT RECOVERD",patch_amount_recovered.json())

    # GET WRITEOFF BALANCE AND UPDATE WITH DEPOSIT AMOUNT (V2 API)
    # Extract writeoff balance from the deposit data we already fetched
    writeoff_bal = 0
    if deposit_details.status_code == 200:
        deposit_data = deposit_details.json()
        if '_customFieldValues' in deposit_data and 'BM_01' in deposit_data['_customFieldValues']:
            writeoff_bal = deposit_data['_customFieldValues']['BM_01'] or 0

    # V2 API PATCH payload for balance to recover
    bal_recover_payload = [
        {
            "op": "REPLACE",
            "path": "/_customFieldValues/balance_to_recover",
            "value": "{:,.2f}".format(round(float(writeoff_bal) - float(actual_depo_amount)))
        }
    ]

    geturl = urls + "deposits/{0}".format(deposit_id)
    bal_recover_details = requests.patch(geturl, json=bal_recover_payload, headers=v2_patch_headers)
    #
    # Get new balance to recover
    new_bal_recover = float(writeoff_bal) - float(actual_depo_amount)

    # V2 API recovery status updates
    if float(new_bal_recover) > 0.0:
        partially_recovered_payload = [
            {
                "op": "REPLACE",
                "path": "/_customFieldValues/REC_STAT",
                "value": "Partially Recovered"
            }
        ]
        geturl = urls + "deposits/{0}".format(deposit_id)
        full_recovery_details = requests.patch(geturl, json=partially_recovered_payload, headers=v2_patch_headers)
        # print('PARTIAL RECOVERED STATUS', full_recovery_details.json())
    elif float(new_bal_recover) <= 0.0:
        fully_recovered_payload = [
            {
                "op": "REPLACE",
                "path": "/_customFieldValues/REC_STAT",
                "value": "Fully Recovered"
            }
        ]
        geturl = urls + "deposits/{0}".format(deposit_id)
        partial_recovery_details = requests.patch(geturl, json=fully_recovered_payload, headers=v2_patch_headers)

    if actual_depo_amount == 0.0:
        # V2 API: Remove custom field by setting to null
        delete_field_payload = [
            {
                "op": "REMOVE",
                "path": "/_customFieldValues/REC_STAT"
            }
        ]
        geturl = urls + "deposits/{0}".format(deposit_id)
        field_delete = requests.patch(geturl, json=delete_field_payload, headers=v2_patch_headers)
        data = field_delete.json()
        print('DATA',data)













