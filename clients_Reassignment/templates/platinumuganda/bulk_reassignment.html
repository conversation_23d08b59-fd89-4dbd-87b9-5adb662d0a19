{% extends "platinumuganda/main_dashboard.html" %}
{% load bootstrap4 %}
{% load url_replace %}
{% load humanize %}
{% block content %}
{% load static %}
{% block style %}


<link rel="stylesheet" href="{% static 'css/journals.css' %}"> {% endblock style %}
{% for message in messages %}
<div class="message">
    {{ message }}
    <a href="#" class="del-msg">&times;</a>
</div>
{% endfor %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card" style="height: 100%;
                                    margin: 2%;
                                    border: 1px solid #5ABF5A;
                                    padding: 49px;
                                    overflow: auto;">
                        <h2 style="font-size:20px;font-family: sans-serif;
                        font-size: 20px;text-align:center;">Bulk clients reassignment</h2>
                <div class="content">
                    <form method="POST" enctype="multipart/form-data">
                        {% csrf_token %}
                        <div class="panel-body">
                            {% if errors %}
                            <div class="panel panel-danger">
                                <div class="panel-heading">The file upload has generated the following errors:</div>
                                <ul class="list-group">
                                    {% for row in errors %}
                                    <li class="list-group-item">{{ row }}</li>
                                    {% endfor %}
                                </ul>
                            </div>

                            {% endif %}

                            {{ form.as_p }}

                            <div class="text-left">
                                <input type="hidden" name="signed_request" value="{{signed_request}}">
                                <a class="btn btn-success btn-wd" style="font-size: 15px;
                                font-stretch: extra-expanded;
                                font-family: sans-serif;
                                width: 160px;
                                background-color: #5ABF5A;
                                height: 30px;
                                border-radius: 10px;" href="/static/platinumug Clients_Reassignment.xlsx" role="button">Download Template</a>
                            </div>

                            <div class="text-left">
                                <button type="submit" style="font-size: 15px;
                                font-stretch: extra-expanded;
                                font-family: sans-serif;
                                background-color: #28a745;
                                width: 150px;
                                margin-top: 24px;
                                height: 30px;
                                border-radius: 10px;" name="add" class="btn btn-success btn-fill btn-wd">Upload File</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>

    <link rel="stylesheet" href="https://ajax.aspnetcdn.com/ajax/jquery.ui/1.10.4/themes/smoothness/jquery-ui.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<script src="{% static 'js/messages.js' %}"></script>

{% endblock %}