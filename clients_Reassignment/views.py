from django.shortcuts import render
import pickle
from django.shortcuts import render, redirect,HttpResponse
from django.views.decorators.csrf import csrf_exempt
from .forms import patchclientForm
from .tasks import clientReassignment<PERSON>latKe,post_client_reassignment_premierug,post_client_reassignment_platinumug
from django.http import HttpResponse, HttpResponseRedirect
import base64,json
from django.contrib import messages
from mambu.utils import <PERSON>mb<PERSON>, MambuError, MambuAPIError
from mambu.models import Credential, Environment

# PLATINUM KENYA
@csrf_exempt
def index_platinumke(request):
    user_role =""
    mambu_user_email =""
    environment_name =""
    bulk_posting_form = patchclientForm(request.POST, request.FILES)
    signed_request = request.POST.get('signed_request')
    if signed_request is not None:
        encoded_string = signed_request.split('.')[1]
        encoded_string += "=" * ((4 - len(encoded_string) % 4) % 4)
        decoded_string = base64.b64decode(encoded_string).decode("utf-8")
        obj = json.loads(decoded_string)
        mambu_user_key = obj['USER_KEY']
        environment_name = obj["DOMAIN"].split('.mambu.com', 1)[0]
        # print("Environment Name: ",environment_name)
        env = Environment.objects.get(url=environment_name)
        auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
        mambu = Mambu(auth_user.apikey, auth_user.generatedkey, auth_user.environment.url)
        mambu_user = mambu.get_users(username=mambu_user_key, fullDetails=True)
        user_role = mambu_user["role"]["encodedKey"]

        mambu_user_email = mambu_user["email"]

    c = {'form': bulk_posting_form, 'signed_request': signed_request}
    allowed_userroles = ["8a858e5257d85fa40157f5827a2416e0", "8a9386ab6bd02d4b016bd10f978c18d6",
                         "8a1a184a46dc1d950146f2090f155e8d","8a858f5659cb44e30159cbe844281635"]
    if user_role in allowed_userroles:
        if request.method == "POST":
            if bulk_posting_form.is_valid():
                entity = request.POST.get('entity')
                file_name = request.FILES['file'].name
                try:
                    clientReassignmentPlatKe.delay(pickle.dumps(request.FILES['file'].read()),
                                                             mambu_user_email, entity, environment_name, file_name)
                except clientReassignmentPlatKe.OperationalError as exc:
                    logger.exception('Sending task raised: %r', exc)
                messages.add_message(request, messages.SUCCESS, 'File successfully uploaded')
        else:
            bulk_posting_form = patchclientForm()
        return render(request, 'platinumke/bulk_reassignment.html', c)
    else:
        return redirect('clients_Reassignment:rolenotallowedplatinumke')


def role_not_allowed(request):
    return render(request, 'platinumke/access_denied.html')


def progress(request):
    if request.method == 'GET':
        status = "processing ...."
        return render(request, 'platinumke/progress.html', {'data': status, "emailadd":request.GET.get('email')})


# @csrf_exempt
# def update_client_platinumke(request):
#     status = ""
#     mambu_user_email = request.session.get('mambu_user')['email']
#     # print('EMAIL ADDRESS',mambu_user_email)
#     environment_name = request.session["environment_name"]
#     # mambu_user_email = "<EMAIL>"
#
#     errors = []
#     if (request.POST):
#         bulk_posting_form = patchclientForm(request.POST, request.FILES)
#
#         if bulk_posting_form.is_valid():
#             entity = request.POST.get('entity')
#             file_name = request.FILES['file'].name
#             try:
#                 patch_fields_platinumke.delay(pickle.dumps(request.FILES['file'].read()), mambu_user_email, entity,environment_name, file_name)
#             except patch_fields_platinumke.OperationalError as exc:
#                 logger.exception('Sending task raised: %r', exc)
#
#             return HttpResponseRedirect('/client_reassignment/progress?email=' + mambu_user_email)
#     else:
#         bulk_posting_form = patchclientForm()
#     return render(request, 'platinumke/bulk_reassignment.html',
#                   {"form": bulk_posting_form, 'errors': errors})



# prmieruganda client Reassignment
@csrf_exempt
def premierug_client_reassignment_index(request):
    user_role =""
    mambu_user_email =""
    environment_name =""
    client_reassignment_form = patchclientForm(request.POST, request.FILES)

    signed_request = request.POST.get('signed_request')
    if signed_request is not None:
        encoded_string = signed_request.split('.')[1]
        encoded_string += "=" * ((4 - len(encoded_string) % 4) % 4)
        decoded_string = base64.b64decode(encoded_string).decode("utf-8")
        obj = json.loads(decoded_string)
        mambu_user_key = obj['USER_KEY']
        decoded_string = base64.b64decode(encoded_string)


        # get environment name
        obj = json.loads(decoded_string)

        # get logged in user key
        environment_name = obj["DOMAIN"].split('.mambu.com', 1)[0]
        # print("Environment Name: ",environment_name)
        env = Environment.objects.get(url=environment_name)
        auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
        mambu = Mambu(auth_user.apikey, auth_user.generatedkey, auth_user.environment.url)
        mambu_user = mambu.get_users(username=mambu_user_key, fullDetails=True)
        user_role = mambu_user["role"]["encodedKey"]
        request.session['mambu_user'] = mambu_user



        mambu_user_email = mambu_user["email"]
        # print("MAMBU USER",mambu_user)

    c = {'form': client_reassignment_form, 'signed_request': signed_request}
    # environment_name = "premieruganda.sandbox"
    # system admin,mis assistant
    allowed_userroles = ["8abc1aea45eb1c2a0145ef7b21155074","8a9386ab6bd02d4b016bd10f978c18d6","8a1a184a46dc1d950146f2090f155e8d"]

    if request.session.get('mambu_user'):
        if user_role in allowed_userroles:
            if request.method == "POST":
                if client_reassignment_form.is_valid():
                    entity = request.POST.get('entity')
                    file_name = request.FILES['file'].name
                    try:
                        post_client_reassignment_premierug.delay(pickle.dumps(request.FILES['file'].read()), mambu_user_email, entity, environment_name,file_name)
                    except post_client_reassignment_premierug.OperationalError as exc:
                        logger.exception('Sending task raised: %r', exc)
                    messages.add_message(request, messages.SUCCESS, 'File successfully uploaded')
            else:
                client_reassignment_form = patchclientForm()
            return render(request, 'premieruganda/bulk_reassignment.html', c)
        else:
            return redirect('clients_Reassignment:rolenotallowedplatinumke')
    else:
        return render(request, 'premieruganda/404.html', c)


# platinumuganda client Reassignment
@csrf_exempt
def platinumug_client_reassignment_index(request):
    user_role =""
    mambu_user_email =""
    environment_name = ""
    client_reassignment_form = patchclientForm(request.POST, request.FILES)

    signed_request = request.POST.get('signed_request')
    if signed_request is not None:
        encoded_string = signed_request.split('.')[1]
        encoded_string += "=" * ((4 - len(encoded_string) % 4) % 4)
        decoded_string = base64.b64decode(encoded_string).decode("utf-8")
        obj = json.loads(decoded_string)
        mambu_user_key = obj['USER_KEY']
        decoded_string = base64.b64decode(encoded_string)


        # get environment name
        obj = json.loads(decoded_string)

        # get logged in user key
        environment_name = obj["DOMAIN"].split('.mambu.com', 1)[0]
        # print("Environment Name: ",environment_name)
        env = Environment.objects.get(url=environment_name)
        auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
        mambu = Mambu(auth_user.apikey, auth_user.generatedkey, auth_user.environment.url)
        mambu_user = mambu.get_users(username=mambu_user_key, fullDetails=True)
        user_role = mambu_user["role"]["encodedKey"]


        mambu_user_email = mambu_user["email"]
        # print("MAMBU USER",mambu_user)
    else:
        return render(request, 'platinumuganda/404.html')


    c = {'form': client_reassignment_form, 'signed_request': signed_request}
    allowed_userroles = ["8a9387627018d51501701a19f093238d","8a858ee159de9b420159debe33340dda",
                         "8a858f955a61ad4e015a6458238a3eaf",""]
    if user_role in allowed_userroles:
        if request.method == "POST":
            if client_reassignment_form.is_valid():
                entity = request.POST.get('entity')

                file_name = request.FILES['file'].name
                try:
                    post_client_reassignment_platinumug.delay(pickle.dumps(request.FILES['file'].read()), mambu_user_email,entity, environment_name,file_name)
                except post_client_reassignment_platinumug.OperationalError as exc:
                    logger.exception('Sending task raised: %r', exc)
                messages.add_message(request, messages.SUCCESS, 'File successfully uploaded')
        else:
            client_reassignment_form = patchclientForm()
        return render(request, 'platinumuganda/bulk_reassignment.html', c)
    else:
        return redirect('clients_Reassignment:rolenotallowedplatinumke')

