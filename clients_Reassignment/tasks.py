from __future__ import absolute_import, unicode_literals
from newugandalms.celery import app
from mambu.utils import <PERSON><PERSON><PERSON>, MambuError, MambuAPIError
from mambu.models import Credential, Environment
import pickle
import xlsxwriter
from django.core.mail import send_mail, BadHeaderError, EmailMessage
from io import StringIO, BytesIO
import math
import redis
import itertools
from datetime import datetime
import xlrd
import requests
from requests.exceptions import RequestException


xlrd.xlsx.ensure_elementtree_imported(False, None)
xlrd.xlsx.Element_has_iter = True


##Pcl Kenya credentials
platinumke_sandbox_url = "https://platinumkenya.sandbox.mambu.com/api/"
platinumke_live_url = "https://platinumkenya.mambu.com/api/"

premieruganda_sandbox_url = "https://premieruganda.sandbox.mambu.com/api/"
premieruganda_live_url = "https://premieruganda.mambu.com/api/"

platinumuganda_sandbox_url = "https://platinumuganda.sandbox.mambu.com/api/"
platinumuganda_live_url = "https://platinumuganda.mambu.com/api/"



def get_client_state(root_url,clientid,headers):
    branchkenya = root_url + "clients/{0}".format(clientid)

    # print("PREMIERUUG URL",branchkenya)
    # sandbrprem = requests.get(branchkenya, auth=passwordprem)
    sandbrprem = requests.get(branchkenya, headers=headers)
    # print("JSON RES",sandbrprem.json())
    getbrprem = sandbrprem.json()

    return getbrprem.get("state","")

###Final customfield patch
@app.task
def clientReassignmentPlatKe(s_file, mambu_user_email, entity, environment_name, file_name):
    env = Environment.objects.get(url=environment_name)
    auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
    headers = {
        auth_user.apikey: auth_user.generatedkey
    }

    start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    file = pickle.loads(s_file)
    # print('FILE',file)
    emailaddress = mambu_user_email

    # print("emailaddress",emailaddress)

    workbook = xlrd.open_workbook(file_contents=file)
    sheet = workbook.sheet_by_index(0)

    receipt_store = redis.StrictRedis(host='localhost', port=6379, db=1, decode_responses=True)
    receipt_store.config_get('databases')
    receipt_store.flushall()

    file_object = BytesIO()  # create a file-like object
    report = xlsxwriter.Workbook(file_object)
    results_to_post = report.add_worksheet('Patch Results')
    results_to_post_count = 0
    results_to_post.write_row(0,0,('ID', 'VALUE', 'RETURN CODE', 'RETURN STATUS', 'ERROR SOURCE'))

    receipts_count = 0
    return_code = ''
    return_status = ''
    error_source = ''
    TaskId = []
    params = []

    for row in range(1, sheet.nrows):
        try:
            client_id = int(sheet.cell(row, 0).value)
        except ValueError:
            client_id = sheet.cell(row, 0).value

        try:
            assigneduserkey = sheet.cell(row, 1).value
        except ValueError:
            assigneduserkey = sheet.cell(row, 1).value


        try:
            try:
                assignedbranch = sheet.cell(row, 2).value
            except ValueError:
                assignedbranch = sheet.cell(row, 2).value
        except Exception as err:
            assignedbranch = ""

        TaskId = [client_id]
        params = [assigneduserkey]
        branchKey = [assignedbranch]

        if assignedbranch is None or assignedbranch == "":
            payload = {
                "client":
                    {
                        "assignedUserKey": params[0]
                    }
            }
        else:
            payload = {
                "client":
                {
                    "assignedBranchKey": branchKey[0],
                    "assignedUserKey":params[0]
                }
            }
        # print("payload",payload)
        root_url = platinumke_sandbox_url if environment_name.endswith('sandbox') else platinumke_live_url
        # print('Root url',root_url)

        clientState = get_client_state(root_url,TaskId[0],headers)

        if clientState:
            if clientState == "BLACKLISTED":
                undo_blacklist = {
                    "client":
                        {"state": "ACTIVE"}
                }
                geturl = platinumke_live_url + "clients/{0}".format(TaskId[0])
                patch_client_details = requests.patch(geturl, json=undo_blacklist, headers=headers)

                if patch_client_details.status_code in [400,401]:
                    undo_blacklist = {
                        "client":
                            {"state": "INACTIVE"}
                    }
                    geturl = platinumke_live_url + "clients/{0}".format(TaskId[0])
                    patch_client_details = requests.patch(geturl, json=undo_blacklist, headers=headers)

                if entity is not None:
                    url = root_url + entity + "/{0}/".format(TaskId[0])
                    # print('URL',url)
                    try:
                        res = requests.patch(url, json=payload, headers=headers)
                        # print('response',res.json())
                    except RequestException as err:
                        raise err
                    else:
                        return_code = res.json()['returnCode']
                        return_status = res.json()['returnStatus']
                        try:
                            error_source = res.json()['errorSource']
                        except KeyError as e:
                            error_source = ''

                        receipts_count += 1
                        receipt_store.lpush(TaskId[0], f'{params[0],return_code,return_status,error_source}')

                        client_blacklist_payload = {
                            "client":
                                {
                                    "state": "BLACKLISTED"
                                }
                        }
                        geturl = url + "clients/{0}".format(TaskId[0])
                        blacklist_details = requests.patch(geturl, json=client_blacklist_payload, headers=headers)

            elif clientState =="EXITED":
                undo_blacklist = {
                    "client":
                        {"state": "ACTIVE"}
                }
                geturl = root_url + "clients/{0}".format(TaskId[0])
                patch_client_details = requests.patch(geturl, json=undo_blacklist, headers=headers)

                if patch_client_details.status_code in [400, 401]:
                    undo_blacklist = {
                        "client":
                            {"state": "INACTIVE"}
                    }
                    geturl = root_url + "clients/{0}".format(TaskId[0])
                    patch_client_details = requests.patch(geturl, json=undo_blacklist, headers=headers)

                if entity is not None:
                    url = root_url + entity + "/{0}/".format(TaskId[0])
                    # print('URL',url)
                    try:
                        res = requests.patch(url, json=payload, headers=headers)
                        print('PREMIERUG REASSIGNMENT:', res.json())
                    except RequestException as err:
                        raise err
                    else:
                        return_code = res.json()['returnCode']
                        return_status = res.json()['returnStatus']
                        try:
                            error_source = res.json()['errorSource']
                        except KeyError as e:
                            error_source = ''

                        receipts_count += 1
                        receipt_store.lpush(TaskId[0], f'{params[0], return_code, return_status, error_source}')

                        client_blacklist_payload = {
                            "client":
                                {
                                    "state": "EXITED"
                                }
                        }
                        geturl = root_url + "clients/{0}".format(TaskId[0])
                        blacklist_details = requests.patch(geturl, json=client_blacklist_payload,
                                                           headers=headers)
                else:
                    return_code = "400"
                    return_status = "Deactivated user"
                    error_source = ""

                    receipts_count += 1
                    receipt_store.lpush(TaskId[0], f'{params[0], return_code, return_status, error_source}')

            else:
                if entity is not None:
                    url = root_url + entity + "/{0}/".format(TaskId[0])
                    # print('URL',url)
                    try:
                        res = requests.patch(url, json=payload, headers=headers)
                        # print('response',res.json())
                    except RequestException as err:
                        raise err
                    else:
                        return_code = res.json()['returnCode']
                        return_status = res.json()['returnStatus']
                        try:
                            error_source = res.json()['errorSource']
                        except KeyError as e:
                            error_source = ''

                        receipts_count += 1
                        receipt_store.lpush(TaskId[0], f'{params[0],return_code,return_status,error_source}')

            count = 0
            for key in receipt_store.scan_iter(TaskId[0]):
                # print("KEY: ", key)
                count = count + 1
                results_to_post_count = results_to_post_count + 1

                dat = receipt_store.lrange(key, 0, -1)
                # print("REDIS DATA: ",dat)
                for row in dat:
                    row_values = eval(row)
                    # print("row_values",row_values)
                    if params[0] in row_values:
                        redis_params = params[0]

                    if return_code in row_values:
                        redis_code = return_code
                    if return_status in row_values:
                        redis_status = return_status
                    if error_source in row_values:
                        redis_error_source = error_source

                data=(TaskId[0],redis_params, redis_code, redis_status, redis_error_source)
                # print("DATA FROM REDIS: ", data)
                results_to_post.write_row(results_to_post_count, 0, data)
                # print("counted: {} keys".format(count))
        else:
            return_code = "404"
            return_status = "INVALID_CLIENT_ID"
            error_source = ""

            receipts_count += 1
            receipt_store.lpush(TaskId[0], f'{params[0], return_code, return_status, error_source}')
    report.close()
    ##send email
    try:
        emailaddresses = ['<EMAIL>']

        emailaddresses.append(emailaddress)

        email = EmailMessage('Reassignment report for ' + entity,
                             'Please find attached report for ' + entity + '\n Start time :' + start_time + '\n Finished: ' + datetime.now().strftime(
                                 '%Y-%m-%d %H:%M:%S'), '<EMAIL>', emailaddresses)
        email.content_subtype = "html"
        email.attach('Results for ' + file_name, file_object.getvalue(), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        emailsent = email.send()
    except Exception as e:
        print("ERROREMAILMESSAGE: ", e)

# client reassignment premierug
@app.task
def post_client_reassignment_premierug(s_file, mambu_user_email, entity, environment_name, file_name):
    env = Environment.objects.get(url=environment_name)
    auth_user = Credential.objects.get(apikey="ApiKey", environment=env)

    mambu = Mambu(auth_user.apikey, auth_user.generatedkey, auth_user.environment.url)

    start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    file = pickle.loads(s_file)
    # print('FILE',file)
    emailaddress = mambu_user_email

    # print("emailaddress",emailaddress)

    workbook = xlrd.open_workbook(file_contents=file)
    sheet = workbook.sheet_by_index(0)

    receipt_store = redis.StrictRedis(host='localhost', port=6379, db=1, decode_responses=True)
    receipt_store.config_get('databases')
    receipt_store.flushall()

    file_object = BytesIO()  # create a file-like object
    report = xlsxwriter.Workbook(file_object)
    results_to_post = report.add_worksheet('Patch Results')
    results_to_post_count = 0
    results_to_post.write_row(0,0,('ID', 'VALUE', 'RETURN CODE', 'RETURN STATUS', 'ERROR SOURCE'))

    receipts_count = 0
    return_code = ''
    return_status = ''
    error_source = ''
    TaskId = []
    params = []

    for row in range(1, sheet.nrows):
        try:
            client_id = sheet.cell(row, 0).value
        except ValueError:
            client_id = sheet.cell(row, 0).value

        try:
            assigneduserkey = sheet.cell(row, 1).value
        except ValueError:
            assigneduserkey = sheet.cell(row, 1).value
        try:
            assignedbranch = sheet.cell(row, 2).value
        except ValueError:
            assignedbranch = sheet.cell(row, 2).value

        split_branch = assignedbranch.split('-')
        assignedbranchkey = split_branch[0]


        try:
            try:
                assignedcenterkey = sheet.cell(row, 3).value
            except ValueError:
                assignedcenterkey = sheet.cell(row, 3).value
        except Exception as err:
            assignedcenterkey = ""

        TaskId = [client_id]
        params = [assigneduserkey]
        branchKey = [assignedbranchkey]
        payload=''

        if assignedcenterkey is not None or assignedcenterkey != "":
            split_center = assignedcenterkey.split('-')
            assignedcenterkey = split_center[0]
            centerKey = [assignedcenterkey]

            payload = {
                "client":
                {
                    "assignedCentreKey":centerKey[0],
                    "assignedBranchKey":branchKey[0],
                    "assignedUserKey":params[0]
                }
            }
        if assignedcenterkey is None or assignedcenterkey == "":
            payload = {
                "client":
                    {
                        "assignedBranchKey": branchKey[0],
                        "assignedUserKey": params[0]
                    }
            }

        # print("assigneduserkey",assigneduserkey)
        headers = {
            "ApiKey": "R4wuVQVe9dv8PshKR4mYagTdgWe6Vv0O"
        }

        sandbox_headers = {
            "ApiKey": "********************************"

        }

        # get users
        # user_json = mambu.get_users(assigneduserkey)

        # print("URL USERS",self.environment_url)
        if assigneduserkey is not None:
            url = premieruganda_live_url+ "users/{0}".format(params[0])
            # print("usersJson",url)
            userRes = requests.get(url,headers=headers)
            user_json = userRes.json()
            print("userJsonResponse",user_json)

        root_url = premieruganda_sandbox_url if environment_name.endswith('sandbox') else premieruganda_live_url
        clientState = get_client_state(root_url,TaskId[0],headers)

        if clientState:

            if clientState == "BLACKLISTED":
                undo_blacklist = {
                    "client":
                        {"state": "ACTIVE"}
                }
                geturl = root_url + "clients/{0}".format(TaskId[0])
                patch_client_details = requests.patch(geturl, json=undo_blacklist, headers=headers)

                if patch_client_details.status_code in [400,401]:
                    undo_blacklist = {
                        "client":
                            {"state": "INACTIVE"}
                    }
                    geturl = root_url + "clients/{0}".format(TaskId[0])
                    patch_client_details = requests.patch(geturl, json=undo_blacklist, headers=headers)

                if entity is not None and user_json["userState"] == "ACTIVE":
                    url = root_url + entity + "/{0}/".format(TaskId[0])
                    headers = sandbox_headers if environment_name.endswith("sandbox") else headers
                    # print('URL',url)
                    try:
                        res = requests.patch(url, json=payload, headers=headers)
                        print('PREMIERUG REASSIGNMENT:', res.json())
                    except RequestException as err:
                        raise err
                    else:
                        return_code = res.json()['returnCode']
                        return_status = res.json()['returnStatus']
                        try:
                            error_source = res.json()['errorSource']
                        except KeyError as e:
                            error_source = ''

                        receipts_count += 1
                        receipt_store.lpush(TaskId[0], f'{params[0], return_code, return_status, error_source}')

                        client_blacklist_payload = {
                            "client":
                                {
                                    "state": "BLACKLISTED"
                                }
                        }
                        geturl = url + "clients/{0}".format(TaskId[0])
                        blacklist_details = requests.patch(geturl, json=client_blacklist_payload, headers=headers)
                else:
                    return_code = "400"
                    return_status = "Deactivated user"
                    error_source = ""

                    receipts_count += 1
                    receipt_store.lpush(TaskId[0], f'{params[0], return_code, return_status, error_source}')
            elif clientState =="EXITED":
                undo_blacklist = {
                    "client":
                        {"state": "ACTIVE"}
                }
                geturl = root_url + "clients/{0}".format(TaskId[0])
                patch_client_details = requests.patch(geturl, json=undo_blacklist, headers=headers)

                if patch_client_details.status_code in [400, 401]:
                    undo_blacklist = {
                        "client":
                            {"state": "INACTIVE"}
                    }
                    geturl = root_url + "clients/{0}".format(TaskId[0])
                    patch_client_details = requests.patch(geturl, json=undo_blacklist, headers=headers)

                if entity is not None and user_json["userState"] == "ACTIVE":
                    url = root_url + entity + "/{0}/".format(TaskId[0])
                    # print('URL',url)
                    try:
                        res = requests.patch(url, json=payload, headers=headers)
                        print('PREMIERUG REASSIGNMENT:', res.json())
                    except RequestException as err:
                        raise err
                    else:
                        return_code = res.json()['returnCode']
                        return_status = res.json()['returnStatus']
                        try:
                            error_source = res.json()['errorSource']
                        except KeyError as e:
                            error_source = ''

                        receipts_count += 1
                        receipt_store.lpush(TaskId[0], f'{params[0], return_code, return_status, error_source}')

                        client_blacklist_payload = {
                            "client":
                                {
                                    "state": "EXITED"
                                }
                        }
                        geturl = root_url + "clients/{0}".format(TaskId[0])
                        blacklist_details = requests.patch(geturl, json=client_blacklist_payload,
                                                           headers=headers)
                else:
                    return_code = "400"
                    return_status = "Deactivated user"
                    error_source = ""

                    receipts_count += 1
                    receipt_store.lpush(TaskId[0], f'{params[0], return_code, return_status, error_source}')

            else:
                if entity is not None and user_json["userState"] == "ACTIVE":
                    url = root_url + entity + "/{0}/".format(TaskId[0])
                    headers = sandbox_headers if environment_name.endswith("sandbox") else headers
                    print('UgandaResponse',url)
                    try:
                        res = requests.patch(url, json=payload, headers=headers)
                        print('PREMIERUG REASSIGNMENT:', res.json())
                    except (RequestException,KeyError) as err:
                        # raise err
                        pass
                    else:
                        return_code = res.json()['returnCode']
                        return_status = res.json()['returnStatus']
                        try:
                            error_source = res.json()['errorSource']
                        except KeyError as e:
                            error_source = ''

                        receipts_count += 1
                        receipt_store.lpush(TaskId[0], f'{params[0], return_code, return_status, error_source}')
                else:
                    return_code = "400"
                    return_status = "Deactivated user"
                    error_source = ""

                    receipts_count += 1
                    receipt_store.lpush(TaskId[0], f'{params[0], return_code, return_status, error_source}')

            count = 0
            for key in receipt_store.scan_iter(TaskId[0]):
                # print("KEY: ", key)
                count = count + 1
                results_to_post_count = results_to_post_count + 1

                dat = receipt_store.lrange(key, 0, -1)
                # print("REDIS DATA: ",dat)
                for row in dat:
                    row_values = eval(row)
                    # print("row_values",row_values)
                    if params[0] in row_values:
                        redis_params = params[0]

                    if return_code in row_values:
                        redis_code = return_code
                    if return_status in row_values:
                        redis_status = return_status
                    if error_source in row_values:
                        redis_error_source = error_source

                data=(TaskId[0],redis_params, redis_code, redis_status, redis_error_source)
                # print("DATA FROM REDIS: ", data)
                results_to_post.write_row(results_to_post_count, 0, data)
                # print("counted: {} keys".format(count))
        else:
            return_code = "404"
            return_status = "INVALID_CLIENT_ID"
            error_source = ""

            receipts_count += 1
            receipt_store.lpush(TaskId[0], f'{params[0], return_code, return_status, error_source}')

    report.close()
    ##send email
    try:
        emailaddresses = ['<EMAIL>']

        emailaddresses.append(emailaddress)

        email = EmailMessage('Reassignment report for ' + entity,
                             'Please find attached report for ' + entity + '\n Start time :' + start_time + '\n Finished: ' + datetime.now().strftime(
                                 '%Y-%m-%d %H:%M:%S'), '<EMAIL>', emailaddresses)
        email.content_subtype = "html"
        email.attach('Results for ' + file_name, file_object.getvalue(), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        emailsent = email.send()
    except Exception as e:
        print("ERROREMAILMESSAGE: ", e)

# client reassignment platinumuganda
@app.task
def post_client_reassignment_platinumug(s_file, mambu_user_email, entity, environment_name, file_name):
    env = Environment.objects.get(url=environment_name)
    auth_user = Credential.objects.get(apikey="ApiKey", environment=env)
    headers = {
        "ApiKey": "inL3qDhnz6bju8xYqxuGJT2PTXjltT2Y"
    }
    mambu = Mambu(auth_user.apikey, auth_user.generatedkey, auth_user.environment.url)
    start_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    file = pickle.loads(s_file)
    # print('FILE',file)
    emailaddress = mambu_user_email

    # print("emailaddress",emailaddress)

    workbook = xlrd.open_workbook(file_contents=file)
    sheet = workbook.sheet_by_index(0)

    receipt_store = redis.StrictRedis(host='localhost', port=6379, db=1, decode_responses=True)
    receipt_store.config_get('databases')
    receipt_store.flushall()

    file_object = BytesIO()  # create a file-like object
    report = xlsxwriter.Workbook(file_object)
    results_to_post = report.add_worksheet('Patch Results')
    results_to_post_count = 0
    results_to_post.write_row(0,0,('ID', 'VALUE', 'RETURN CODE', 'RETURN STATUS', 'ERROR SOURCE'))

    receipts_count = 0
    return_code = ''
    return_status = ''
    error_source = ''
    TaskId = []
    params = []

    for row in range(1, sheet.nrows):
        try:
            client_id = sheet.cell(row, 0).value
        except ValueError:
            client_id = sheet.cell(row, 0).value

        try:
            assigneduserkey = sheet.cell(row, 1).value
        except ValueError:
            assigneduserkey = sheet.cell(row, 1).value

        try:
            assignedbranch = sheet.cell(row, 2).value
        except ValueError:
            assignedbranch = sheet.cell(row, 2).value

        split_branch = assignedbranch.split('-')
        assignedbranchkey = split_branch[0]


        # get users
        try:
            user_json = mambu.get_users(assigneduserkey)
            # print("USERS DETAILS",user_json["userState"])
        except Exception as e:
            pass

        TaskId = [client_id]
        params = [assigneduserkey]
        branchKey = [assignedbranchkey]

        payload = {
            "client":
            {
                "assignedBranchKey":branchKey[0],
                "assignedUserKey":params[0]
            }
        }
        # print("payload",payload)
        root_url = platinumuganda_sandbox_url if environment_name.endswith('sandbox') else platinumuganda_live_url

        # print("URL USERS",self.environment_url)
        if assigneduserkey is not None:
            url = root_url+ "users/{0}".format(params[0])
            userRes = requests.get(url,headers=headers)
            user_json = userRes.json()
            # print("userJsonResponse",user_json)


        clientState = get_client_state(root_url,TaskId[0],headers)

        if clientState == "BLACKLISTED":
            undo_blacklist = {
                "client":
                    {"state": "ACTIVE"}
            }
            geturl = root_url + "clients/{0}".format(TaskId[0])
            patch_client_details = requests.patch(geturl, json=undo_blacklist, headers=headers)

            if patch_client_details.status_code in [400,401]:
                undo_blacklist = {
                    "client":
                        {"state": "INACTIVE"}
                }
                geturl = root_url + "clients/{0}".format(TaskId[0])
                patch_client_details = requests.patch(geturl, json=undo_blacklist, headers=headers)

            if entity is not None and user_json["userState"] == "ACTIVE":
                url = root_url + entity + "/{0}/".format(TaskId[0])
                try:
                    res = requests.patch(url, json=payload, headers=headers)
                except RequestException as err:
                    raise err
                else:
                    return_code = res.json()['returnCode']
                    return_status = res.json()['returnStatus']
                    try:
                        error_source = res.json()['errorSource']
                    except KeyError as e:
                        error_source = ''

                    receipts_count += 1
                    receipt_store.lpush(TaskId[0], f'{params[0], return_code, return_status, error_source}')

                    client_blacklist_payload = {
                        "client":
                            {
                                "state": "BLACKLISTED"
                            }
                    }
                    geturl = url + "clients/{0}".format(TaskId[0])
                    blacklist_details = requests.patch(geturl, json=client_blacklist_payload, headers=headers)
            else:
                return_code = "400"
                return_status = "Deactivated user"
                error_source = ""

                receipts_count += 1
                receipt_store.lpush(TaskId[0], f'{params[0], return_code, return_status, error_source}')
        elif clientState =="EXITED":
            undo_blacklist = {
                "client":
                    {"state": "ACTIVE"}
            }
            geturl = root_url + "clients/{0}".format(TaskId[0])
            patch_client_details = requests.patch(geturl, json=undo_blacklist, headers=headers)

            if patch_client_details.status_code in [400, 401]:
                undo_blacklist = {
                    "client":
                        {"state": "INACTIVE"}
                }
                geturl = root_url + "clients/{0}".format(TaskId[0])
                patch_client_details = requests.patch(geturl, json=undo_blacklist, headers=headers)

            if entity is not None and user_json["userState"] == "ACTIVE":
                url = root_url + entity + "/{0}/".format(TaskId[0])
                # print('URL',url)
                try:
                    res = requests.patch(url, json=payload, headers=headers)
                    print('PLATUG REASSIGNMENT:', res.json())
                except RequestException as err:
                    raise err
                else:
                    return_code = res.json()['returnCode']
                    return_status = res.json()['returnStatus']
                    try:
                        error_source = res.json()['errorSource']
                    except KeyError as e:
                        error_source = ''

                    receipts_count += 1
                    receipt_store.lpush(TaskId[0], f'{params[0], return_code, return_status, error_source}')

                    client_blacklist_payload = {
                        "client":
                            {
                                "state": "EXITED"
                            }
                    }
                    geturl = root_url + "clients/{0}".format(TaskId[0])
                    blacklist_details = requests.patch(geturl, json=client_blacklist_payload,
                                                       headers=headers)
            else:
                return_code = "400"
                return_status = "Deactivated user"
                error_source = ""

                receipts_count += 1
                receipt_store.lpush(TaskId[0], f'{params[0], return_code, return_status, error_source}')

        else:
            if entity is not None and user_json["userState"] == "ACTIVE":
                url = root_url + entity + "/{0}/".format(TaskId[0])
                try:
                    res = requests.patch(url, json=payload, headers=headers)
                    print('PREMIERKE REASSIGNMENT:', res.json())
                except RequestException as err:
                    raise err
                else:
                    return_code = res.json()['returnCode']
                    return_status = res.json()['returnStatus']
                    try:
                        error_source = res.json()['errorSource']
                    except KeyError as e:
                        error_source = ''

                    receipts_count += 1
                    receipt_store.lpush(TaskId[0], f'{params[0], return_code, return_status, error_source}')
            else:
                return_code = "400"
                return_status = "Deactivated user"
                error_source = ""

                receipts_count += 1
                receipt_store.lpush(TaskId[0], f'{params[0], return_code, return_status, error_source}')

        count = 0
        for key in receipt_store.scan_iter(TaskId[0]):
            # print("KEY: ", key)
            count = count + 1
            results_to_post_count = results_to_post_count + 1

            dat = receipt_store.lrange(key, 0, -1)
            # print("REDIS DATA: ",dat)
            for row in dat:
                row_values = eval(row)
                # print("row_values",row_values)
                if params[0] in row_values:
                    redis_params = params[0]

                if return_code in row_values:
                    redis_code = return_code
                if return_status in row_values:
                    redis_status = return_status
                if error_source in row_values:
                    redis_error_source = error_source

            data=(TaskId[0],redis_params, redis_code, redis_status, redis_error_source)
            # print("DATA FROM REDIS: ", data)
            results_to_post.write_row(results_to_post_count, 0, data)
            # print("counted: {} keys".format(count))
    report.close()
    ##send email
    try:
        emailaddresses = ['<EMAIL>']

        # cc=['<EMAIL> ']

        emailaddresses.append(emailaddress)

        email = EmailMessage('Reassignment report for ' + entity,
                             'Please find attached report for ' + entity + '\n Start time :' + start_time + '\n Finished: ' + datetime.now().strftime(
                                 '%Y-%m-%d %H:%M:%S'), '<EMAIL>', emailaddresses)
        email.content_subtype = "html"
        email.attach('Results for ' + file_name, file_object.getvalue(), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        emailsent = email.send()
    except Exception as e:
        print("ERROREMAILMESSAGE: ", e)










