# Generated by Django 3.0.6 on 2021-09-16 08:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Platinumuganda_DEP_Letters', '0012_auto_20210916_0924'),
    ]

    operations = [
        migrations.CreateModel(
            name='psl_final_demand_weekdays',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('accountId', models.CharField(blank=True, max_length=255, null=True)),
                ('environment', models.CharField(blank=True, max_length=255, null=True)),
                ('fullName', models.CharField(blank=True, max_length=255, null=True)),
                ('employer_Name', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'ordering': ('accountId',),
            },
        ),
        migrations.CreateModel(
            name='psl_final_demand_weekend',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('accountId', models.CharField(blank=True, max_length=255, null=True)),
                ('environment', models.CharField(blank=True, max_length=255, null=True)),
                ('fullName', models.CharField(blank=True, max_length=255, null=True)),
                ('employer_Name', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'ordering': ('accountId',),
            },
        ),
        migrations.CreateModel(
            name='psl_second_demand_weekdays',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('accountId', models.CharField(blank=True, max_length=255, null=True)),
                ('environment', models.CharField(blank=True, max_length=255, null=True)),
                ('fullName', models.CharField(blank=True, max_length=255, null=True)),
                ('employer_Name', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'ordering': ('accountId',),
            },
        ),
        migrations.CreateModel(
            name='psl_second_demand_weekend',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('accountId', models.CharField(blank=True, max_length=255, null=True)),
                ('environment', models.CharField(blank=True, max_length=255, null=True)),
                ('fullName', models.CharField(blank=True, max_length=255, null=True)),
                ('employer_Name', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'ordering': ('accountId',),
            },
        ),
    ]
