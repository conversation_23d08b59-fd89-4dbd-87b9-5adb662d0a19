# Generated by Django 3.0.6 on 2021-05-20 07:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Platinumuganda_DEP_Letters', '0004_delete_lbf_demand_letter'),
    ]

    operations = [
        migrations.CreateModel(
            name='lbf_demand_notice',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('accountId', models.CharField(blank=True, max_length=255, null=True)),
                ('attachment_response', models.TextField(null=True)),
                ('email_response', models.TextField(null=True)),
                ('product_name', models.CharField(max_length=255, null=True)),
            ],
            options={
                'ordering': ('accountId',),
            },
        ),
    ]
