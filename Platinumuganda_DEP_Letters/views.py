import configparser

from django.shortcuts import HttpResponse, render, redirect
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import dateformat
from rest_framework.decorators import api_view
from weasyprint import HTML, CSS
from googleapiclient.errors import HttpError
import requests
from datetime import date
from mambu.models import Credential, Environment
from rest_framework.authentication import BasicAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from datetime import datetime, timedelta
import inflect
from pathlib import Path
import json
from mambu.utils import Mambu
from django.views.decorators.csrf import csrf_exempt
from .tasks import save_psl_first_demand, save_psl_second_demand, save_psl_final_demand, save_lbf_demand_letter
import base64
# gmail api
import pickle
import os
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from google.auth.transport.requests import Request
from .models import psl_demand_letter_new, sme_demand_letter, lbf_demand_notice
import configparser


def get_custom_field(fieldID, entity):
    for custom_field in entity:
        if custom_field["customFieldID"] == fieldID:
            return custom_field["value"]
    return None


def Create_Service_platke(client_secret_file, api_name, api_version, *scopes):
    # print(client_secret_file, api_name, api_version, scopes, sep='-')
    CLIENT_SECRET_FILE = client_secret_file
    API_SERVICE_NAME = api_name
    API_VERSION = api_version
    SCOPES = [scope for scope in scopes[0]]
    # print(SCOPES)
    cred = None

    pickle_file = Path().absolute() / 'Platinumuganda_DEP_Letters/token_{0}_{1}.pickle'.format(API_SERVICE_NAME,
                                                                                               API_VERSION)
    # print(pickle_file)

    if os.path.exists(pickle_file):
        with open(pickle_file, 'rb') as token:
            cred = pickle.load(token)

    if not cred or not cred.valid:
        if cred and cred.expired and cred.refresh_token:
            cred.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(CLIENT_SECRET_FILE, SCOPES)
            cred = flow.run_local_server()

        with open(pickle_file, 'wb') as token:
            pickle.dump(cred, token)

    try:
        service = build(API_SERVICE_NAME, API_VERSION, credentials=cred)
        # print(API_SERVICE_NAME, 'service created successfully')
        return service
    except Exception as e:
        print('Unable to connect.')
        print(e)
        return None


# path to gmail_credentials.json
gmail_credentials_platug = Path().absolute() / 'Platinumuganda_DEP_Letters/gmail_credentials.json'


def get_client_details(clientID, urls, headers):
    geturl = urls + "clients/{0}".format(clientID)
    get_client_details = requests.get(geturl, headers=headers)
    return get_client_details.json()


def get_repayments(accountid, headers, url):
    loanfindurl = url + "loans/{0}/repayments".format(accountid)
    # print("loanfindurl", loanfindurl)
    getloan = requests.get(loanfindurl, headers=headers)

    findloan = getloan.json()
    installment = 0

    try:

        if len(findloan) <= 2:
            principalDue = findloan[0]['principalDue']
            interestDue = findloan[0]['interestDue']
            feesDue = findloan[0]['feesDue']
            installment = float(principalDue) + float(interestDue) + float(feesDue)
        else:
            principalDue = findloan[2]['principalDue']
            interestDue = findloan[2]['interestDue']
            feesDue = findloan[2]['feesDue']

            installment = float(principalDue) + float(interestDue) + float(feesDue)
    except KeyError:
        installment = 0

    return installment


def get_sales_rep_email(assigneduserKey, headers, urls):
    geturl = urls + "users/{0}".format(assigneduserKey)
    get_sales_rep_details = requests.get(geturl, headers=headers)
    return get_sales_rep_details.json().get("email", '')


# PSL FIRST DEMAND NOTICE
@csrf_exempt
@api_view(['POST'])
def PslfirstDemandNoticeActivity(request):
    data = json.loads(request.body)
    save_psl_first_demand.delay(data)
    return Response({"message": "request received"}, status=status.HTTP_200_OK)


# PSL SECOND DEMAND NOTICE
@csrf_exempt
@api_view(['POST'])
def PslsecondDemandNoticeActivity(request):
    data = json.loads(request.body)
    save_psl_second_demand.delay(data)
    return Response({"message": "request received"}, status=status.HTTP_200_OK)


# PSL FINAL DEMAND NOTICE
@csrf_exempt
@api_view(['POST'])
def PslfinalDemandNoticeActivity(request):
    data = json.loads(request.body)
    save_psl_final_demand.delay(data)
    return Response({"message": "request received"}, status=status.HTTP_200_OK)


# LBF DEMAND NOTICES
# class LBFDemandActivity(APIView):
#     authentication_classes = (BasicAuthentication,)
#     permission_classes = (IsAuthenticated,)
#
#     def post(self, request):
#         save_lbf_demand_letter(request.data)
#         return Response({"message": "request received"}, status=status.HTTP_200_OK)
@csrf_exempt
@api_view(['POST'])
def LBFDemandActivity(request):
    data = json.loads(request.body)
    # print("DATA",data)

    save_lbf_demand_letter(data)
    return Response({"message": "request received"}, status=status.HTTP_200_OK)


@csrf_exempt
def get_config():
    # Load configuration from the 'secrets.ini' file
    config = configparser.ConfigParser()
    config.read('secrets.ini')
    return config


@csrf_exempt
def sme_first_demand_notice(request):
    payload = json.loads(request.body)
    environment = payload['environment']
    accountId = payload['accountId']
    fullName = payload['fullName']
    collateraldetails = payload['collateraldetails']

    urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment.endswith(
        'sandbox') else 'https://platinumuganda.mambu.com/api/'

    config = get_config()
    api_key = config.get('Credentials', 'PLATINUMUG_PROD_APIKEY_DEP')

    headers = {"ApiKey": "{}".format(api_key)}
    geturl = urls + "loans/{0}".format(accountId)
    getdetails = requests.get(geturl, headers=headers)
    getloan_response = getdetails.json()

    # # GET CURRENT TIME AND DATE
    now = datetime.now()
    today_date_time = dateformat.format(now, 'jS F Y')
    reference_date = now.strftime("%d%m%Y")

    activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'], "%Y-%m-%dT%H:%M:%S+%f")
    activation_date = dateformat.format(activation, 'jS F Y')

    # GET TOTAL BALANCE
    total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(
        getloan_response['feesDue']) + float(getloan_response['penaltyDue'])
    loan_balance = float(getloan_response['principalBalance']) + float(getloan_response['interestBalance']) + float(
        getloan_response['feesBalance']) + float(getloan_response['penaltyBalance'])
    days_in_arrears = (datetime.today() - datetime.strptime(getloan_response["lastSetToArrearsDate"],
                                                            "%Y-%m-%dT%H:%M:%S+%f")).days

    # amount in words
    p = inflect.engine()
    total_due_words = p.number_to_words(round(total_due)).capitalize()
    # client details
    client_details = get_client_details(getloan_response["accountHolderKey"], urls, headers)
    # get users on the branch
    users_payload = {
        "branchID": client_details.get("assignedBranchKey", ""),
        "offset": "0",
        "limit": "1000",
        "fullDetails": "True"
    }
    geturl = urls + "users/"
    getdetails = requests.get(geturl, json=users_payload, headers=headers)
    # CREATE A TASK
    task_response = ""
    for obj in getdetails.json():
        # add Branch Coordinator role
        try:
            if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a858fbd59e06eb70159e46293a81d50" or obj[
                "userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a93876d784478b80178493f43046762":
                # print("username:",obj["username"])
                task_payload = {
                    "title": "SME First Demand Notice_{}.pdf".format(accountId),
                    "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                    "description": "SME Demand Notice attached for {account_id}".format(account_id=accountId),
                    "status": "OPEN",
                    "clientID": getloan_response['accountHolderKey'],
                    "username": obj["username"]
                }

                taskurl = urls + "tasks"
                taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                task_response = taskdetails.json()
        except Exception:
            pass
    context = {
        "full_name": fullName.title(),
        "Client_phone_number": client_details.get("mobilePhone1", ""),
        "today_date_time": today_date_time,
        "loan_account": accountId,
        "loan_balance": '{:,}'.format(round(loan_balance)),
        "Client_fname": client_details.get("firstName", "").title(),
        "days_in_arrears": days_in_arrears,
        "total_due": '{:,}'.format(round(total_due)),
        "reference_date": reference_date,
        "accountID": accountId,
        "clientID": client_details["id"],
        "activation_date": activation_date,
        "total_due_words": total_due_words,
        "collateraldetails": collateraldetails

    }
    rendered_html = render_to_string('DEP/sme/first_demand_notice.html', {'context': context}).encode(
        encoding="UTF-8")
    pdf_file = HTML(string=rendered_html, ).write_pdf(
        stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/sme/first_demand_notice.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                     ])
    response = HttpResponse(pdf_file, content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename=" SME Demand Notice_{}.pdf"'.format(accountId)

    # attachDocument
    encoded_string = base64.b64encode(response.content).decode('utf-8')
    attachdoc = {
        "document": {
            "documentHolderKey": getloan_response['encodedKey'],
            "documentHolderType": "LOAN_ACCOUNT",
            "name": "SME Demand Notice {}".format(accountId),
            "type": "PDF"
        },
        "documentContent": encoded_string
    }
    doc_url = urls + "loans/{0}/documents/".format(accountId)
    details = requests.post(doc_url, json=attachdoc, headers=headers)

    sme_demand_letter(
        accountId=accountId,
        product_name=getloan_response['loanName'],
        task_response=task_response,
        attachment_response=details.json()).save()
    return response


@csrf_exempt
@api_view(['POST'])
def SmesecondDemandNoticeActivity(request):
    data = json.loads(request.body)
    # print("DATA",data)

    urls = "https://platinumuganda.sandbox.mambu.com/api/" if data['environment'].endswith(
        'sandbox') else 'https://platinumuganda.mambu.com/api/'
    config = get_config()
    api_key = config.get('Credentials', 'PLATINUMUG_PROD_APIKEY_DEP')

    headers = {"ApiKey": "{}".format(api_key)}

    geturl = urls + "loans/{0}".format(data['accountId'])
    getdetails = requests.get(geturl, headers=headers)
    getloan_response = getdetails.json()

    # # GET CURRENT TIME AND DATE
    now = datetime.now()
    today_date_time = dateformat.format(now, 'jS F Y')
    reference_date = now.strftime("%d%m%Y")

    # GET TOTAL BALANCE
    total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(
        getloan_response['feesDue']) + float(getloan_response['penaltyDue'])
    loan_balance = float(getloan_response['principalBalance']) + float(getloan_response['interestBalance']) + float(
        getloan_response['feesBalance']) + float(getloan_response['penaltyBalance'])
    days_in_arrears = (datetime.today() - datetime.strptime(getloan_response["lastSetToArrearsDate"],
                                                            "%Y-%m-%dT%H:%M:%S+%f")).days

    # amount in words
    p = inflect.engine()
    total_due_words = p.number_to_words(round(total_due)).capitalize()
    # client details
    client_details = get_client_details(getloan_response["accountHolderKey"], urls, headers)

    activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'], "%Y-%m-%dT%H:%M:%S+%f")
    activation_date = dateformat.format(activation, 'jS F Y')

    users_payload = {
        "branchID": client_details.get("assignedBranchKey", ""),
        "offset": "0",
        "limit": "1000",
        "fullDetails": "True"
    }
    geturl = urls + "users/"
    getdetails = requests.get(geturl, json=users_payload, headers=headers)
    # CREATE A TASK
    task_response = ""
    for obj in getdetails.json():
        # add Branch Coordinator role
        try:
            if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a858fbd59e06eb70159e46293a81d50" or obj[
                "userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a93876d784478b80178493f43046762":
                # print("username:",obj["username"])
                task_payload = {
                    "title": "SME Second Demand Notice_{}.pdf".format(data['accountId']),
                    "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                    "description": "SME Second demand Notice attached for {account_id}".format(
                        account_id=data["accountId"]),
                    "status": "OPEN",
                    "clientID": getloan_response['accountHolderKey'],
                    "username": obj["username"]
                }

                taskurl = urls + "tasks"
                taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                task_response = taskdetails.json()
        except Exception:
            pass
    context = {
        "full_name": data["fullName"].title(),
        "Client_phone_number": client_details.get("mobilePhone1", ""),
        "today_date_time": today_date_time,
        "loan_account": data['accountId'],
        "loan_balance": '{:,}'.format(round(loan_balance)),
        "Client_fname": client_details.get("firstName", "").title(),
        "days_in_arrears": days_in_arrears,
        "total_due": '{:,}'.format(round(total_due)),
        "reference_date": reference_date,
        "accountID": data['accountId'],
        "clientID": client_details["id"],
        "activation_date": activation_date,
        "total_due_words": total_due_words,
        "collateraldetails": data["collateraldetails"]

    }
    rendered_html = render_to_string('DEP/sme/second_demand_notice.html', {'context': context}).encode(
        encoding="UTF-8")
    pdf_file = HTML(string=rendered_html, ).write_pdf(
        stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/sme/first_demand_notice.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                     ])
    response = HttpResponse(pdf_file, content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename=" SME Second Demand Notice_{}.pdf"'.format(
        data['accountId'])

    # attachDocument
    encoded_string = base64.b64encode(response.content).decode('utf-8')
    attachdoc = {
        "document": {
            "documentHolderKey": getloan_response['encodedKey'],
            "documentHolderType": "LOAN_ACCOUNT",
            "name": "SME Second Demand Notice {}".format(data['accountId']),
            "type": "PDF"
        },
        "documentContent": encoded_string
    }
    doc_url = urls + "loans/{0}/documents/".format(data['accountId'])
    details = requests.post(doc_url, json=attachdoc, headers=headers)

    sme_demand_letter(
        accountId=data["accountId"],
        product_name=getloan_response['loanName'],
        task_response=task_response,
        attachment_response=details.json()
    ).save()

    return response


# SME LOAN RECALL
# class smeLoanRecallActivityV2(APIView):
#     authentication_classes = (BasicAuthentication,)
#     permission_classes = (IsAuthenticated,)
#
#     def post(self, request):
#         smeLoanRecall(request.data)
#         return Response({"message": "request received"}, status=status.HTTP_200_OK)
@csrf_exempt
def smeLoanRecallActivityV2(request):
    payload = json.loads(request.body)
    environment = payload['environment']
    accountId = payload['accountId']
    fullName = payload['fullName']
    guarantorTwoFname = payload['guarantor2Fname']
    guarantorTwoSurnameFname = payload['guarantor2Sname']
    guarantorOneFname = payload["guarantorOneFname"]
    guarantorOneSurnameFname = payload["guarantorOneSurnameFname"]

    urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment.endswith(
        'sandbox') else 'https://platinumuganda.mambu.com/api/'
    headers = {'content-type': 'application/json', "apiKey": "dLnGyp8KCc6xvK84TCQ5IJG2SOLnxULU"}

    geturl = urls + "loans/{0}".format(accountId)
    getdetails = requests.get(geturl, headers=headers)
    getloan_response = getdetails.json()

    print("LoanObject", getloan_response)

    # GET LOAN ACTIVATION DATE
    activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'], "%Y-%m-%dT%H:%M:%S+%f")
    activation_date = dateformat.format(activation, 'jS F Y')

    now = datetime.now()
    today_date_time = dateformat.format(now, 'jS F Y')
    reference_date = now.strftime("%d%m%Y")

    # # GET CURRENT TIME AND DATE
    now = datetime.now()
    today_date_time = dateformat.format(now, 'jS F Y')
    due_date = date.today() + timedelta(days=7)
    due_date_final = due_date.strftime('%d %B %Y')

    # GET TOTAL BALANCE
    loan_balance = float(getloan_response['principalBalance']) + float(getloan_response['interestBalance']) + float(
        getloan_response['feesBalance']) + float(getloan_response['penaltyBalance'])

    # print("LoanDetails",getload)
    geturl = base_url + "/clients/{0}".format(getloan_response['accountHolderKey'])
    client_details = requests.get(geturl, headers=headers)
    getClient_details = client_details.json()

    total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(
        getloan_response['feesDue']) + float(
        getloan_response['penaltyDue'])
    # amount in words
    p = inflect.engine()
    total_due_words = p.number_to_words(round(total_due)).capitalize()

    due_dates = datetime.now() + timedelta(days=45)
    due_date_final = dateformat.format(due_dates, 'jS F Y')

    # print("Client_details", getClient_details)
    geturl = base_url + "/loans/{0}/repayments".format(accountId)
    getinstallments = requests.get(geturl, headers=headers)
    getinstallmentsresponse = getinstallments.json()
    installments = float(getinstallmentsresponse[0]['principalDue']) + float(
        getinstallmentsresponse[0]['interestDue']) + float(getinstallmentsresponse[0]['feesDue']) + float(
        getinstallmentsresponse[0]['penaltyDue'])
    total_balance = float(getloan_response['principalBalance']) + float(getloan_response['interestBalance']) + float(
        getloan_response['feesBalance']) + float(getloan_response['penaltyBalance'])
    loanAmount = float(getloan_response['loanAmount'])

    # amount in words
    p = inflect.engine()
    total_balance_words = p.number_to_words(round(total_balance)).capitalize()
    installments_words = p.number_to_words(round(installments)).capitalize()

    loanAmount_words = p.number_to_words(round(loanAmount)).capitalize()
    activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'], "%Y-%m-%dT%H:%M:%S+%f")
    activation_date = dateformat.format(activation, 'jS F Y')

    # GET TOTAL BALANCE
    total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(
        getloan_response['feesDue']) + float(getloan_response['penaltyDue'])
    loan_balance = float(getloan_response['principalBalance']) + float(getloan_response['interestBalance']) + float(
        getloan_response['feesBalance']) + float(getloan_response['penaltyBalance'])
    days_in_arrears = (datetime.today() - datetime.strptime(getloan_response["lastSetToArrearsDate"],
                                                            "%Y-%m-%dT%H:%M:%S+%f")).days

    # Get loan amount
    loan_amount = float(getloan_response['loanAmount'])

    # loan_amount in words
    p = inflect.engine()
    loan_bal_words = p.number_to_words(round(loan_balance)).capitalize()

    # get installment amount
    installment_amount = get_repayments(accountId, headers, urls)

    # installment amount in words
    p = inflect.engine()
    installment_amount_words = p.number_to_words(int(installment_amount)).capitalize()

    due_date = date.today() + timedelta(days=7)
    due_date_final = dateformat.format(due_date, 'jS F Y')

    # client details
    client_details = get_client_details(getloan_response["accountHolderKey"], urls, headers)

    users_payload = {
        "branchID": client_details.get("assignedBranchKey", ""),
        "offset": "0",
        "limit": "1000",
        "fullDetails": "True"
    }
    geturl = urls + "users/"
    getdetails = requests.get(geturl, json=users_payload, headers=headers)
    # CREATE A TASK
    task_response = ""
    for obj in getdetails.json():
        try:
            # add Branch Coordinator role
            if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a858fbd59e06eb70159e46293a81d50" or obj[
                "userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a93876d784478b80178493f43046762":
                task_payload = {
                    "title": "SME Loan Recall {}.pdf".format(accountId),
                    "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                    "description": "SME Loan Recall attached for {account_id}".format(account_id=accountId),
                    "status": "OPEN",
                    "clientID": getloan_response['accountHolderKey'],
                    "username": obj["username"]

                }
                taskurl = urls + "tasks"
                taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                task_response = taskdetails.json()
        except Exception:
            pass

    context = {
        "full_name": getClient_details['firstName'] + "  " + getClient_details['lastName'],
        "Client_phone_number": getClient_details["mobilePhone1"],
        "today_date_time": today_date_time,
        "due_dates": due_date_final,
        "loan_balance": '{:,}'.format(round(loan_balance)),
        "disbursementDate": activation_date,
        "loan_account": accountId,
        "loanAmount": '{:,}'.format(round(loanAmount)),
        "total_balance": '{:,}'.format(round(total_balance)),
        "Client_fname": getClient_details['firstName'],
        "total_balance_words": total_balance_words,
        "installments": '{:,}'.format(round(installments)),
        "installments_words": installments_words,
        "loanAmount_words": loanAmount_words,
        "clientID": client_details["id"],
        "total_due_words": total_due_words,
        "guarantorTwoFname": guarantorTwoFname,
        "guarantorTwoSurnameFname": guarantorTwoSurnameFname,
        "total_due": '{:,}'.format(round(total_due)),
        "days_in_arrears": days_in_arrears,
        "activation_date": activation_date,
        "loan_amount": '{:,}'.format(round(loan_amount)),
        "loan_bal_words": loan_bal_words,
        "installment_amount": '{:,}'.format(round(installment_amount)),
        "installment_amount_words": installment_amount_words,
        "date_due": due_date_final,
        "reference_date": reference_date,
        "accountID": accountId,
        "guarantorOneFname": guarantorOneFname,
        "guarantorOneSurnameFname": guarantorOneSurnameFname
    }
    rendered_html = render_to_string('DEP/sme/loan_recall.html', {'context': context}).encode(
        encoding="UTF-8")
    pdf_file = HTML(string=rendered_html, ).write_pdf(
        stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/sme/first_demand_notice.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                     ])
    response = HttpResponse(pdf_file, content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename=" SME Loan Recall {}.pdf"'.format(accountId)

    # attachDocument
    encoded_string = base64.b64encode(response.content).decode('utf-8')
    attachdoc = {
        "document": {
            "documentHolderKey": getloan_response['encodedKey'],
            "documentHolderType": "LOAN_ACCOUNT",
            "name": "SME Loan Recall {}".format(accountId),
            "type": "PDF"
        },
        "documentContent": encoded_string
    }
    doc_url = urls + "loans/{0}/documents/".format(accountId)
    details = requests.post(doc_url, json=attachdoc, headers=headers)

    print("LOAN RECALL ATTACH", details.json())

    sme_demand_letter(
        accountId=accountId,
        product_name=getloan_response['loanName'],
        task_response=task_response,
        attachment_response=details.json()
    ).save()

    rendered_html = render_to_string('DEP/newTemplates/guarantorDemand2.html', {'context': context}).encode(
        encoding="UTF-8")
    pdf_file = HTML(string=rendered_html, ).write_pdf(
        stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/sme/reposession.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                     ])
    response = HttpResponse(pdf_file, content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename=" SME Guarantor Demand Notice_{}.pdf"'.format(accountId)

    # attachDocument
    encoded_string = base64.b64encode(response.content).decode('utf-8')
    attachdoc = {
        "document": {
            "documentHolderKey": getloan_response['encodedKey'],
            "documentHolderType": "LOAN_ACCOUNT",
            "name": "SME Guarantor Two Demand Notice {}".format(accountId),
            "type": "PDF"
        },
        "documentContent": encoded_string
    }
    doc_url = urls + "loans/{0}/documents/".format(accountId)
    # print("doc_url",doc_url)
    details = requests.post(doc_url, json=attachdoc, headers=headers)
    # print("GUARANTOR TWO ATTACH",details.json())

    sme_demand_letter(
        accountId=accountId,
        product_name=getloan_response['loanName'],
        task_response=task_response,
        attachment_response=details.json()).save()

    # print("context",context)
    rendered_html = render_to_string('DEP/newTemplates/guarantorDemand.html', {'context': context}).encode(
        encoding="UTF-8")
    pdf_file = HTML(string=rendered_html, ).write_pdf(
        stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/sme/reposession.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                     ])
    response = HttpResponse(pdf_file, content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename=" SME Guarantor Demand Notice_{}.pdf"'.format(accountId)

    # attachDocument
    encoded_string = base64.b64encode(response.content).decode('utf-8')
    attachdoc = {
        "document": {
            "documentHolderKey": getloan_response['encodedKey'],
            "documentHolderType": "LOAN_ACCOUNT",
            "name": "SME Guarantor One Demand Notice {}".format(accountId),
            "type": "PDF"
        },
        "documentContent": encoded_string
    }
    doc_url = urls + "loans/{0}/documents/".format(accountId)
    details = requests.post(doc_url, json=attachdoc, headers=headers)

    sme_demand_letter(
        accountId=accountId,
        product_name=getloan_response['loanName'],
        task_response=task_response,
        attachment_response=details.json()).save()

    return HttpResponse("sent")


# # LBF DEMAND
# class LBFDemandActivity(APIView):
#     authentication_classes = (BasicAuthentication,)
#     permission_classes = (IsAuthenticated,)
#
#     def post(self, request):
#         # lbf_covid_friendly_demand_letter(request.data)
#         lbf_demand_letter(request.data)
#
#         return Response({"message": "request received"}, status=status.HTTP_200_OK)


# def lbf_demand_letter(request):
#     data ={
#         "environment":"platinumuganda",
#         "accountId":"*********",
#         "fullName":"OKOTH NICKY"
#     }
#
#     urls = "https://platinumuganda.sandbox.mambu.com/api/" if data['environment'].endswith(
#         'sandbox') else 'https://platinumuganda.mambu.com/api/'
#
#     headers = {"ApiKey":"0DyXjZ2cUyKjLRhImkvQSJ5D2B3ZjyCu"}
#     geturl = urls + "loans/{0}".format(data['accountId'])
#     getdetails = requests.get(geturl, headers=headers)
#     getloan_response = getdetails.json()
#
#     # check if the product is lbf bond
#     sentence = "." if getloan_response['loanName'].startswith('LBF Bond') else " subject to a late payment fee, 0.33% of the arrears per day."
#
#     # GET LOAN ACTIVATION DATE
#     activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'], "%Y-%m-%dT%H:%M:%S+%f")
#     activation_date = dateformat.format(activation, 'jS F Y')
#
#     now = datetime.now()
#     today_date_time = dateformat.format(now, 'jS F Y')
#     reference_date = now.strftime("%d%m%Y")
#
#
#     # GET TOTAL BALANCE
#     total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(getloan_response['feesDue']) + float(getloan_response['penaltyDue'])
#
#     # total due in
#     p = inflect.engine()
#     total_due_words = p.number_to_words(int(total_due)).capitalize()
#
#     # Vehicle number plate
#     get_details_veh_details = urls + "loans/{0}/custominformation/{1}".format(data["accountId"], 'reg_no')
#     get_veh_details = requests.get(get_details_veh_details, headers=headers)
#
#     # print('VEHICLE REG',get_loan_details.json())
#     try:
#         vehicle_reg_number = get_custom_field("reg_no", get_veh_details.json())
#     except TypeError:
#         vehicle_reg_number = ''
#
#
#     # client details
#     client_details = get_client_details(getloan_response["accountHolderKey"],urls,headers)
#     # client_email = "<EMAIL>"
#     client_email = client_details.get("emailAddress","")
#
#
#     context = {
#         "full_name": data["fullName"].title(),
#         "Client_phone_number": client_details.get("mobilePhone1", ""),
#         "today_date_time": today_date_time,
#         "Client_fname": client_details.get("firstName","").title(),
#         "total_due": '{:,}'.format(round(total_due)),
#         "total_due_words":total_due_words,
#         "activation_date":activation_date,
#         "vehicle_reg_number":vehicle_reg_number if vehicle_reg_number is not None else "",
#         "sentence":sentence,
#         "reference_date": reference_date,
#         "accountID": data['accountId'],
#         "clientID":client_details["id"]
#     }
#     rendered_html = render_to_string('DEP/lbf/lbf_demand.html', {'context': context}).encode(
#         encoding="UTF-8")
#     pdf_file = HTML(string=rendered_html,).write_pdf(
#         stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/sme/first_demand_notice.css'),
#                      CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
#                      CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
#                      ])
#     response = HttpResponse(pdf_file, content_type='application/pdf')
#     response['Content-Disposition'] = 'attachment; filename="LBF Demand Notice {}.pdf"'.format(data['accountId'])
#
#     # # search branch coordinator by Branch
#     # users_payload = {
#     #     "branchID": client_details.get("assignedBranchKey", ""),
#     #     "offset": "0",
#     #     "limit": "1000",
#     #     "fullDetails":"True"
#     # }
#     # geturl = urls + "users/"
#     # getdetails = requests.get(geturl, json=users_payload, headers=headers)
#     # # CREATE A TASK
#     # task_response =""
#     # for obj in getdetails.json():
#     #     # add Branch Coordinator role
#     #     try:
#     #         if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a858fbd59e06eb70159e46293a81d50":
#     #             # print("username:",obj["username"])
#     #             task_payload = {
#     #                 "title": "LBF Demand Notice_{}.pdf".format(data['accountId']),
#     #                 "dueDate": date.today().strftime('%Y-%m-%d')[:10],
#     #                 "description": "LBF Demand Notice attached for {account_id}".format(account_id=data["accountId"]),
#     #                 "status": "OPEN",
#     #                 "clientID": getloan_response['accountHolderKey'],
#     #                 "username": obj["username"]
#     #             }
#     #
#     #             taskurl = urls + "tasks"
#     #             taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
#     #             task_response = taskdetails.json()
#     #     except KeyError:
#     #         pass
#
#     # # search by userrole demand officer head office
#     # users_payload = {
#     #     "branchID": "8a858ee159de9b420159df2118cd2b67",
#     #     "offset": "0",
#     #     "limit": "1000",
#     #     "fullDetails":"True"
#     # }
#     # geturl = urls + "users/"
#     # getdetails_demand_officer = requests.get(geturl, json=users_payload, headers=headers)
#     # # CREATE A TASK
#     # task_response_demand_off = ""
#     # for obj in getdetails_demand_officer.json():
#     #     # add Branch Coordinator role
#     #     if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a93879f6bc116eb016bc21929e72e50":
#     #         task_payload = {
#     #             "title": "LBF Demand Notice_{}.pdf".format(data['accountId']),
#     #             "dueDate": date.today().strftime('%Y-%m-%d')[:10],
#     #             "description": "LBF Demand Notice attached for {account_id}".format(account_id=data["accountId"]),
#     #             "status": "OPEN",
#     #             "clientID": getloan_response['accountHolderKey'],
#     #             "username": obj["username"]
#     #         }
#     #
#     #         taskurl = urls + "tasks"
#     #         taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
#     #         task_response_demand_off = taskdetails.json()
#     #
#     # # attachDocument
#     # encoded_string = base64.b64encode(response.content).decode('utf-8')
#     # attachdoc = {
#     #     "document": {
#     #         "documentHolderKey": getloan_response['encodedKey'],
#     #         "documentHolderType": "LOAN_ACCOUNT",
#     #         "name": "LBF Demand Notice {}".format(data['accountId']),
#     #         "type": "PDF"
#     #     },
#     #     "documentContent": encoded_string
#     # }
#     # doc_url = urls + "loans/{0}/documents/".format(data['accountId'])
#     # details = requests.post(doc_url, json=attachdoc, headers=headers)
#
#
#     # # GMAIL API START
#     message = ""
#     # if client_email is not None or client_email!="":
#     #     CLIENT_SECRET_FILE = gmail_credentials_platug
#     #     service = Create_Service_platke(CLIENT_SECRET_FILE, 'gmail', 'v1', ['https://mail.google.com/'])
#     #
#     #     # email body
#     #     emailMsg = """\
#     #     <html>
#     #       <body>
#     #         <p>Dear {fistname},<p>
#     #         <p>Thank you for being our valued customer.</p>
#     #         <p>This is an urgent notice on your loan account obligation which you have failed to execute as promised.
#     #         Attached herein is a formal demand notice with respect to the same for your action. Please honor your obligation
#     #          and have the opportunity of improving and growing your credit worthiness with us.
#     #          </p>
#     #         <p>For any clarification, please contact your Relationship Manager or our contact center via **********.</p>
#     #         <p>PLATINUM CREDIT</p>
#     #
#     #       </body>
#     #     </html>
#     #     """.format(fistname=client_details.get('firstName', ''))
#     #     # create email message
#     #     mimeMessage = MIMEMultipart()
#     #     mimeMessage['to'] = client_email
#     #     mimeMessage['cc'] = '<EMAIL>'
#     #     mimeMessage['subject'] = "LBF Demand Notice {}.pdf".format(data["accountId"])
#     #     mimeMessage.attach(MIMEText(emailMsg, 'html'))
#     #
#     #     part = MIMEApplication(response.content, _subtype='application/pdf')
#     #     part.add_header('Content-Disposition', 'attachment',filename="LBF Demand Notice {}.pdf".format(data["accountId"]))
#     #
#     #     mimeMessage.attach(part)
#     #     raw_string = base64.urlsafe_b64encode(mimeMessage.as_bytes()).decode()
#     #     try:
#     #         message = service.users().messages().send(
#     #             userId='me',
#     #             body={'raw': raw_string}).execute()
#     #     except HttpError as err:
#     #         message = err
#     #         pass
#     #
#     #     lbf_demand_notice(
#     #         accountId=data["accountId"],
#     #         product_name=getloan_response['loanName'],
#     #         attachment_response=details.json(),
#     #         task_response=task_response,
#     #         email_response=message).save()
#
#     return response
# def lbf_covid_friendly_demand_letter(data):
#     urls = "https://platinumuganda.sandbox.mambu.com/api/" if data['environment'].endswith(
#         'sandbox') else 'https://platinumuganda.mambu.com/api/'
#     headers = {"ApiKey":"0DyXjZ2cUyKjLRhImkvQSJ5D2B3ZjyCu"}
#
#     # sandbox key
#     # headers = {"ApiKey":"********************************"}
#
#     geturl = urls + "loans/{0}".format(data['accountId'])
#     getdetails = requests.get(geturl, headers=headers)
#     getloan_response = getdetails.json()
#
#     # check if the product is lbf bond
#     sentence = "." if getloan_response['loanName'].startswith('LBF Bond') else " subject to a late payment fee, 0.33% of the arrears per day."
#
#     # GET LOAN ACTIVATION DATE
#     activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'], "%Y-%m-%dT%H:%M:%S+%f")
#     activation_date = dateformat.format(activation, 'jS F Y')
#
#     now = datetime.now()
#     today_date_time = dateformat.format(now, 'jS F Y')
#     reference_date = now.strftime("%d%m%Y")
#
#
#     # GET TOTAL BALANCE
#     total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(getloan_response['feesDue']) + float(getloan_response['penaltyDue'])
#
#     # total due in
#     p = inflect.engine()
#     total_due_words = p.number_to_words(int(total_due)).capitalize()
#
#     # Vehicle number plate
#     get_details_veh_details = urls + "loans/{0}/custominformation/{1}".format(data["accountId"], 'reg_no')
#     get_veh_details = requests.get(get_details_veh_details, headers=headers)
#
#     # print('VEHICLE REG',get_loan_details.json())
#     try:
#         vehicle_reg_number = get_custom_field("reg_no", get_veh_details.json())
#     except TypeError:
#         vehicle_reg_number = ''
#
#
#     # client details
#     client_details = get_client_details(getloan_response["accountHolderKey"],urls,headers)
#     # client_email = "<EMAIL>"
#     client_email = client_details.get("emailAddress","")
#
#     context = {
#         "full_name": data["fullName"].title(),
#         "Client_phone_number": client_details.get("mobilePhone1", ""),
#         "today_date_time": today_date_time,
#         "Client_fname": client_details.get("firstName","").title(),
#         "total_due": '{:,}'.format(round(total_due)),
#         "total_due_words":total_due_words,
#         "activation_date":activation_date,
#         "vehicle_reg_number":vehicle_reg_number if vehicle_reg_number is not None else "",
#         "sentence":sentence,
#         "reference_date": reference_date,
#         "accountID": data['accountId'],
#         "clientID":client_details["id"],
#         "loan_amount":'{:,}'.format(round(float(getloan_response["loanAmount"]))) }
#     rendered_html = render_to_string('DEP/lbf/lbf_covid_friendly.html', {'context': context}).encode(
#         encoding="UTF-8")
#     pdf_file = HTML(string=rendered_html,).write_pdf(
#         stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/lbf/covid_friendly.css'),
#                      CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
#                      CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
#                      ])
#     response = HttpResponse(pdf_file, content_type='application/pdf')
#     response['Content-Disposition'] = 'attachment; filename="LBF Demand Notice {}.pdf"'.format(data['accountId'])
#
#     # search branch coordinator by Branch
#     users_payload = {
#         "branchID": client_details.get("assignedBranchKey", ""),
#         "offset": "0",
#         "limit": "1000",
#         "fullDetails":"True"
#     }
#     geturl = urls + "users/"
#     getdetails = requests.get(geturl, json=users_payload, headers=headers)
#     # print("getdetails",getdetails.json())
#     # # CREATE A TASK
#     task_response =""
#     for obj in getdetails.json():
#         # print("OBJ",obj)
#         # add Branch Coordinator role and Regional sales manager
#         try:
#             if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a858fbd59e06eb70159e46293a81d50" or obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a9387236a7c6429016a7d1a4232189b" :
#                 # print("username:",obj["username"])
#                 task_payload = {
#                     "title": "LBF Demand Notice_{}.pdf".format(data['accountId']),
#                     "dueDate": date.today().strftime('%Y-%m-%d')[:10],
#                     "description": "LBF Demand Notice attached for {account_id}".format(account_id=data["accountId"]),
#                     "status": "OPEN",
#                     "clientID": getloan_response['accountHolderKey'],
#                     "username": obj["username"]
#                 }
#
#                 taskurl = urls + "tasks"
#                 taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
#                 task_response = taskdetails.json()
#         except KeyError:
#             pass
#      # search by userrole demand officer head office
#     users_payload = {
#         "branchID": "8a858ee159de9b420159df2118cd2b67",
#         "offset": "0",
#         "limit": "1000",
#         "fullDetails":"True"
#     }
#     geturl = urls + "users/"
#     getdetails_demand_officer = requests.get(geturl, json=users_payload, headers=headers)
#     # CREATE A TASK
#     task_response_demand_off = ""
#     for obj in getdetails_demand_officer.json():
#         # add Branch Coordinator role
#         if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a93879f6bc116eb016bc21929e72e50":
#             task_payload = {
#                 "title": "LBF Demand Notice_{}.pdf".format(data['accountId']),
#                 "dueDate": date.today().strftime('%Y-%m-%d')[:10],
#                 "description": "LBF Demand Notice attached for {account_id}".format(account_id=data["accountId"]),
#                 "status": "OPEN",
#                 "clientID": getloan_response['accountHolderKey'],
#                 "username": obj["username"]
#             }
#
#             taskurl = urls + "tasks"
#             taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
#             task_response_demand_off = taskdetails.json()
#
#     # attachDocument
#     encoded_string = base64.b64encode(response.content).decode('utf-8')
#     attachdoc = {
#         "document": {
#             "documentHolderKey": getloan_response['encodedKey'],
#             "documentHolderType": "LOAN_ACCOUNT",
#             "name": "LBF Demand Notice {}".format(data['accountId']),
#             "type": "PDF"
#         },
#         "documentContent": encoded_string
#     }
#     doc_url = urls + "loans/{0}/documents/".format(data['accountId'])
#     details = requests.post(doc_url, json=attachdoc, headers=headers)
#
#
#     # # GMAIL API START
#     message = ""
#     if client_email is not None or client_email!="":
#         CLIENT_SECRET_FILE = gmail_credentials_platug
#         service = Create_Service_platke(CLIENT_SECRET_FILE, 'gmail', 'v1', ['https://mail.google.com/'])
#
#         # email body
#         emailMsg = """\
#         <html>
#           <body>
#             <p>Dear {fistname},<p>
#             <p>Thank you for being our valued customer.</p>
#             <p>This is an urgent notice on your loan account obligation which you have failed to execute as promised.
#             Attached herein is a formal demand notice with respect to the same for your action. Please honor your obligation
#              and have the opportunity of improving and growing your credit worthiness with us.
#              </p>
#             <p>For any clarification, please contact your Relationship Manager or our contact center via **********.</p>
#             <p>PLATINUM CREDIT</p>
#
#           </body>
#         </html>
#         """.format(fistname=client_details.get('firstName', ''))
#         # create email message
#         mimeMessage = MIMEMultipart()
#         mimeMessage['to'] = client_email
#         mimeMessage['cc'] = '<EMAIL>'
#         mimeMessage['subject'] = "LBF Demand Notice {}.pdf".format(data["accountId"])
#         mimeMessage.attach(MIMEText(emailMsg, 'html'))
#
#         part = MIMEApplication(response.content, _subtype='application/pdf')
#         part.add_header('Content-Disposition', 'attachment',filename="LBF Demand Notice {}.pdf".format(data["accountId"]))
#
#         mimeMessage.attach(part)
#         raw_string = base64.urlsafe_b64encode(mimeMessage.as_bytes()).decode()
#         try:
#             message = service.users().messages().send(
#                 userId='me',
#                 body={'raw': raw_string}).execute()
#         except HttpError as err:
#             message = err
#             pass
#
#         lbf_demand_notice(
#             accountId=data["accountId"],
#             product_name=getloan_response['loanName'],
#             attachment_response=details.json(),
#             task_response=task_response,
#             email_response=message).save()
#     return response


def get_tracking_fee(productTypeKey, headers, urls):
    geturl = urls + "loanproducts/{}".format(productTypeKey)
    get_product_details = requests.get(geturl, headers=headers)
    for obj in get_product_details.json()["loanFees"]:
        if obj["name"] == "Car Tracking Fee":
            return obj["amount"]


def get_repayment(accountId, headers, urls):
    payload = {'offset': 0, 'limit': 200}
    geturl = urls + "loans/{0}/repayments".format(accountId)
    get_all_repayments = requests.get(geturl, params=payload, headers=headers)

    return get_all_repayments.json()


def get_document_generator(loggedin_user_id, urls, headers):
    geturl = urls + "users/{0}".format(loggedin_user_id)
    get_user_details = requests.get(geturl, headers=headers)
    return get_user_details.json()


@csrf_exempt
def offer_letter_schedule(request):
    signed_request = request.POST.get('signed_request')
    if signed_request is not None:
        encoded_string = signed_request.split('.')[1]
        encoded_string += "=" * ((4 - len(encoded_string) % 4) % 4)
        decoded_string = base64.b64decode(encoded_string)
        obj = json.loads(decoded_string)

        accountId = json.loads(decoded_string.decode('utf-8'))['OBJECT_ID']
        loggedin_user_id = json.loads(decoded_string.decode('utf-8'))['USER_KEY']

        environment_name = obj["DOMAIN"].split('.mambu.com', 1)[0]
        env = Environment.objects.get(url=environment_name)
        auth_user = Credential.objects.get(apikey="ApiKey", environment=env)

        mambu = Mambu(auth_user.apikey, auth_user.generatedkey, auth_user.environment.url)
        mambu_user = mambu.get_users(username=loggedin_user_id, fullDetails=True)

        role_key = mambu_user["role"]["encodedKey"] if "role" in mambu_user else ""
        # System Admin Relationship Managers,call center team,Portfolio manager,New Portfolio- AccountantLB,
        # Branch Manager LBF,Coordinators,Branch Admin LB
        allowed_roles = ["8a858ee159de9b420159debe33340dda", "8a858fd159dfee9c0159e040bce42db4"
            , "8a858ec559dff2250159e05b4e5b31cd", "8a9387627018d51501701a19f093238d",
                         "8a858fd159dfee9c0159e040bce42db6", "8a9387236a7c6429016a7d2bb8e31cc4"]
        if role_key in allowed_roles:
            mambu_user["canManage"] = True
            mambu_user["isAdministrator"] = True
            request.session['mambu_user'] = mambu_user
            environment = "live"
            request.session['environment'] = environment

            # environment="platinumuganda"
            # accountId="*********"
            # loggedin_user_id="8a9387fe685f356201685fa6444b07ce"

            urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment.endswith(
                'sandbox') else 'https://platinumuganda.mambu.com/api/'

            config = configparser.ConfigParser()
            config.read('secrets.ini')
            pclugOfferLetters = config.get('Credentials', 'PLATINUMUG_PROD_OFFER_LETTERS')
            headers = {"ApiKey": "{}".format(pclugOfferLetters)}

         
            geturl = urls + "loans/{0}".format(accountId)
            getdetails = requests.get(geturl, headers=headers)
            getloan_response = getdetails.json()

            # print("ACCOUNT STATUS",getloan_response)

            if getloan_response['loanName'].startswith('LBF') or getloan_response['loanName'].startswith(
                    'Car Bond Finance') or getloan_response['loanName'].startswith('PSL') or getloan_response[
                'loanName'].startswith('SME Secured') or getloan_response['loanName'].startswith('Commercial') or \
                    getloan_response['loanName'].startswith('Private Sector'):
                if getloan_response['accountState'] == "APPROVED" or getloan_response[
                    'accountState'] == "PENDING_APPROVAL":
                    # if getloan_response['accountState']:

                    # loan amount
                    loan_amount = getloan_response.get("loanAmount", "")

                    # get installment amount
                    installment_amount = get_repayments(accountId, headers, urls)

                    # loan term
                    loan_term = getloan_response.get("repaymentInstallments", "")

                    # get interest rate depending on product
                    # interest_per_month = "4%" if getloan_response["loanName"].startswith("LBF",1) else "6%" if getloan_response["loanName"].startswith("Bond",4) else "7.5%"
                    # interest_per_month = "4%" if "Bond" in getloan_response["loanName"] else "7.5%" if "PSV" in getloan_response["loanName"] else "4%"
                    interest_per_month = ""
                    if "Bond" in getloan_response["loanName"]:
                        interest_per_month = "2.8%"
                    elif "Private Sector" in getloan_response["loanName"]:
                        interest_per_month = "2.8%"
                    elif "SME secured" in getloan_response["loanName"]:
                        interest_per_month = "2.8%"
                    elif "Commercial" in getloan_response["loanName"]:
                        interest_per_month = "2.8%"
                    elif "PSL" in getloan_response["loanName"]:
                        interest_per_month = "2.8%"
                    elif "PSV" in getloan_response["loanName"]:
                        interest_per_month = "2.8%"
                    elif "PSV" in getloan_response["loanName"]:
                        interest_per_month = "2.8%"
                    else:
                        interest_per_month = "2.8%"

                    count = 0
                    record_data = []
                    capitalized_amount = 0
                    for obj in get_repayment(accountId, headers, urls):
                        capitalized_amount += float(obj['principalDue'])

                        date_dt1 = datetime.strptime(obj["dueDate"][:10], '%Y-%m-%d')
                        count += 1
                        record = {"due_date": date_dt1.strftime('%d-%m-%Y'),
                                  "count": count,
                                  'principalDue': '{:,.2f}'.format(float(obj['principalDue'])),
                                  'interestDue': '{:,.2f}'.format(float(obj['interestDue'])),
                                  'feesDue': '{:,.2f}'.format(float(obj['feesDue'])),
                                  'paymentDue': '{:,.2f}'.format(
                                      float(obj['principalDue']) + float(obj['interestDue']) + float(obj['feesDue'])),
                                  }
                        record_data.append(record)

                    now = datetime.now()
                    today_date_time = dateformat.format(now, 'd-m-Y')

                    # client details
                    client_details = get_client_details(getloan_response["accountHolderKey"], urls, headers)
                    get_user = get_document_generator(loggedin_user_id, urls, headers)

                    generated_by = "{0} {1}".format(get_user['firstName'], get_user['lastName'])

                    context = {
                        "full_name": '{0} {1} {2}'.format(client_details.get("firstName", ""),
                                                          client_details.get("middleName", ""),
                                                          client_details.get("lastName", "")).title(),
                        "loan_amount": '{:,}'.format(float(loan_amount)),
                        "installment_amount": '{:,}'.format(round(installment_amount)),
                        "interest_per_month": interest_per_month,
                        "repayment_schedule": record_data,
                        "today_date_time": today_date_time,
                        "clientID": client_details["id"],
                        "loan_term": loan_term,
                        "printed_by": generated_by,
                        "generation_date": today_date_time,
                        "accountid": accountId,
                        "capitalizedAmount": '{:,.2f}'.format(float(capitalized_amount)) if float(
                            capitalized_amount) > float(loan_amount) else "0"
                    }
                    rendered_html = render_to_string('DEP/offer/offer_schedule.html', {'context': context}).encode(
                        encoding="UTF-8")
                    pdf_file = HTML(string=rendered_html, ).write_pdf(
                        stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/offer/lbf_schedule.css'),
                                     CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                                     CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                                     ])
                    response = HttpResponse(pdf_file, content_type='application/pdf')
                    response['Content-Disposition'] = 'attachment; filename="{0} {1} {2}-LBF Schedule-{3}.pdf"'.format(
                        client_details.get("firstName", ""), client_details.get("middleName", ""),
                        client_details.get("lastName", ""), accountId)
                    return response
                else:
                    return redirect('Platinumuganda_DEP_Letters:account_state')
            else:
                return redirect('Platinumuganda_DEP_Letters:product_access')
        else:
            return redirect('Platinumuganda_DEP_Letters:rolenotallowed')
    return render(request, 'DEP/offer/letters/404.html')


def role_not_allowed(request):
    return render(request, 'DEP/offer/access_denied.html')


def account_state(request):
    return render(request, 'DEP/offer/account_state.html')


def product_not_allowed(request):
    return render(request, 'DEP/offer/product_access.html')


# LBF LETTERS
def account_state_lbf(request):
    return render(request, 'DEP/offer/letters/offer_account_state.html')


def offer_blank_check(request):
    return render(request, 'DEP/offer/letters/offer_letter_blank.html')


def product_not_allowed_lbf(request):
    return render(request, 'DEP/offer/letters/offer_product_access.html')


@csrf_exempt
def offer_lbf_psv_letter(request):
    # signed_request = "6759d1db7105fc1a7611c0dccc005111551a5ef277da70399d52dc4ce54462dd.***********************************************************************************************************************************************************************************************************************"
    signed_request = request.POST.get('signed_request')
    if signed_request is not None:
        encoded_string = signed_request.split('.')[1]
        encoded_string += "=" * ((4 - len(encoded_string) % 4) % 4)
        decoded_string = base64.b64decode(encoded_string)
        obj = json.loads(decoded_string)

        accountId = json.loads(decoded_string.decode('utf-8'))['OBJECT_ID']
        loggedin_user_id = json.loads(decoded_string.decode('utf-8'))['USER_KEY']

        environment_name = obj["DOMAIN"].split('.mambu.com', 1)[0]

        config = configparser.ConfigParser()
        config.read('secrets.ini')
        pclugOfferLetters = config.get('Credentials', 'PLATINUMUG_PROD_OFFER_LETTERS')
        headers = {"ApiKey": "{}".format(pclugOfferLetters)}

        mambu = Mambu("ApiKey", pclugOfferLetters, environment_name)
        mambu_user = mambu.get_users(username=loggedin_user_id, fullDetails=True)

        print("mambuUseer", mambu_user)

        userKey = mambu_user["encodedKey"] if "role" in mambu_user else ""
        # System Admin,Head of credit
        allowed_roles = ["8a858f955a61ad4e015a647a84bd449f", "8a858edb5a70bfe0015a7466a4696f35",
                         "8a9387d0623cd664016247fb53a64a68", "8a9386046611d9b9016614836c612ac8",
                         "8a858fa459e0a1340159ea1e00d74e4d", "8a858fa459e0a1340159e5f5c5832ceb",
                         "8a9387716669165601666cdf48e833ae", "8a858e9b5a61655b015a65faac1b18da",
                         "8a9387c16150537f016151b514402de5","8a858f955a61ad4e015a6458238a3eb1","8a9387fe685f356201685fa6444b07ce"]
        if userKey in allowed_roles:
            mambu_user["canManage"] = True
            mambu_user["isAdministrator"] = True
            urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment_name.endswith(
                'sandbox') else 'https://platinumuganda.mambu.com/api/'

            geturl = urls + "loans/{0}?fullDetails=true".format(accountId)
            getdetails = requests.get(geturl, headers=headers)
            getloan_response = getdetails.json()
            # print("disbursementDetails",getloan_response['disbursementDetails']['fees'])
            credit_life_insurance_amount = None

            # Loop through the list and get the amount for 'Appraisal Fees' and 'Caveat and Chattel Registration'
            for fee in getloan_response['disbursementDetails']['fees']:
                fee_name = fee['fee']['name']
                print("fee_name", fee_name)
                if fee_name == "Credit Life Insurance":
                    credit_life_insurance_amount = fee['amount']
                    break

            repaymentInstallments = getloan_response.get("repaymentInstallments", "")
            loanAmount = getloan_response.get("loanAmount", "")
            principalBalance = getloan_response.get("principalBalance", "")

            # Convert repaymentInstallments to an integer
            repaymentInstallments = int(repaymentInstallments)

            # get refincement details amount
            get_refin_details = urls + "loans/{0}/custominformation/{1}".format(accountId, 'rf01')
            getdetails = requests.get(get_refin_details, headers=headers)
            getloan_responseCF = getdetails.json()

            paymentDue_sum = 0
            for obj in get_repayment(accountId, headers, urls):
                paymentDue_sum += float(obj['principalDue'])

            if not getloan_responseCF:
                print("repaymentInstallments", repaymentInstallments)
                # new loan
                if repaymentInstallments <= 12:

                    fee_rate = 0.0030  # 0.30% expressed as decimal
                else:
                    print("executed else")
                    fee_rate = 0.0040  # 0.40% expressed as decimal

                creditLifeAmount = float(paymentDue_sum) * fee_rate
                # print("creditLifeAmount", creditLifeAmount)


            else:
                # Refinance loan
                creditLifeAmount = credit_life_insurance_amount

            if getloan_response['accountState'] == "PENDING_APPROVAL":

                # loan amount
                loan_amount = getloan_response.get("loanAmount", "")

                # loan term
                loan_term = getloan_response.get("repaymentInstallments", "")

                # get installment amount
                installment_amount = get_repayments(accountId, headers, urls)

                # get tracking fee from product
                product_tracking_fee = get_tracking_fee(getloan_response["productTypeKey"], headers, urls)
                # print("product_tracking_fee",product_tracking_fee)

                # Caveat & Chattel registration fees
                caveat_amount = 0
                for obj in getloan_response["disbursementDetails"]["fees"]:
                    if obj["fee"]["name"] == "Caveat and Chattel Registration":
                        try:
                            caveat_amount = obj["amount"]
                        except KeyError:
                            caveat_amount = obj['fee']['amount']

                # insurance premium
                insurance_prem_amount = 0
                for obj in getloan_response["disbursementDetails"]["fees"]:
                    if obj["fee"]["name"] == "Insurance Premium":
                        insurance_prem_amount = obj["amount"]

                # print("insurance_prem_amount", insurance_prem_amount)

                # get refinances
                refinanceUrl = urls + "loans/{0}/custominformation/{1}".format(accountId, 'rf01')
                ref_details = requests.get(refinanceUrl, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                refinanceAmount = get_custom_field("rf01", ref_details.json())

                if refinanceAmount is None:
                    refinanceAmount = 0

                print("refinanceAmount",refinanceAmount)       
                # arrangement fee

                if float(refinanceAmount) >0.0:
                    appraisalFeeAmt = (7.5/100)* float(refinanceAmount)
                else:    
                    appraisalFeeAmt =   appraisalFeeAmt =(7.5/100)* float(loanAmount)

                print("appraisalFeeAmt",appraisalFeeAmt)
                # TOP UP BALANCE
                top_up_bal = urls + "loans/{0}/custominformation/{1}".format(accountId, 'top_up_offer_letter')
                top_up_details = requests.get(top_up_bal, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                try:
                    top_up_balance_amount = get_custom_field("top_up_offer_letter", top_up_details.json())
                except TypeError:
                    top_up_balance_amount = ''
                top_up_balance = top_up_balance_amount if top_up_balance_amount is not None else 0

                now = datetime.now()
                today_date_time = dateformat.format(now, 'jS F Y')

                # client home address
                get_details_home_details = urls + "clients/{0}/custominformation/{1}".format(
                    getloan_response.get("accountHolderKey"), 'ha1')
                get_add_details = requests.get(get_details_home_details, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                try:
                    home_address = get_custom_field("ha1", get_add_details.json())
                except TypeError:
                    home_address = ''

                # buy off amount
                get_details_buy_details = urls + "loans/{0}/custominformation/{1}".format(accountId, 'buy_off_amount')
                buy_off_details = requests.get(get_details_buy_details, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                try:
                    buy_off_amount_check = get_custom_field("buy_off_amount", buy_off_details.json())
                except TypeError:
                    buy_off_amount_check = ''
                buy_off_amount = buy_off_amount_check if buy_off_amount_check is not None else 0

                # loan appraiser
                get_details_appraiser = urls + "loans/{0}/custominformation/{1}".format(accountId, '_APP_01')
                appraiser_details = requests.get(get_details_appraiser, headers=headers)
                # get users details
                users_details = ''
                for user_key in appraiser_details.json():
                    users_details = get_document_generator(user_key["linkedEntityKeyValue"], urls, headers)
                # print("users_details",users_details)

                get_user = get_document_generator(loggedin_user_id, urls, headers)

                generated_by = "{0} {1}".format(get_user['firstName'], get_user['lastName'])

                # Vehicle Reg number 1
                reg_no_1 = urls + "loans/{0}/custominformation/{1}".format(accountId, 'reg_no')
                reg_1_details = requests.get(reg_no_1, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                try:
                    reg_one = get_custom_field("reg_no", reg_1_details.json())
                except TypeError:
                    reg_one = ''

                # Vehicle Reg number 2
                reg_no_2 = urls + "loans/{0}/custominformation/{1}".format(accountId, 'reg_no02')
                reg_2_details = requests.get(reg_no_2, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                try:
                    reg_two = get_custom_field("reg_no02", reg_2_details.json())
                except TypeError:
                    reg_two = ''

                # other charges
                other_charges_details = urls + "loans/{0}/custominformation/{1}".format(accountId, 'other_charges')
                other_charges_res = requests.get(other_charges_details, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                try:
                    other_charges_amount = get_custom_field("other_charges", other_charges_res.json())
                except TypeError:
                    other_charges_amount = ''

                other_charges = other_charges_amount if other_charges_amount is not None else 0

                # narration other charges
                narration_url = urls + "loans/{0}/custominformation/{1}".format(accountId, 'other_narration_charges')
                narration_details = requests.get(narration_url, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                try:
                    other_narration = get_custom_field("other_narration_charges", narration_details.json())
                except TypeError:
                    other_narration = ''

                # offer letter CF
                offer_letter_url = urls + "loans/{0}/custominformation/{1}".format(accountId, 'offer_letter')
                offer_letter_details = requests.get(offer_letter_url, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                try:
                    offer_letter = get_custom_field("offer_letter", offer_letter_details.json())
                except TypeError:
                    offer_letter = ''

                # PAYEE NAME
                payee_letter_url = urls + "loans/{0}/custominformation/{1}".format(accountId, 'payee_name')
                payee_letter_details = requests.get(payee_letter_url, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                try:
                    payee_name = get_custom_field("payee_name", payee_letter_details.json())
                except TypeError:
                    payee_name = ''

                if offer_letter is None or offer_letter != 'Approved':
                    return redirect('Platinumuganda_DEP_Letters:offer_letter_blank')

                # print("Principal Due", float(paymentDue_sum))
                # print("loanAmount", float(loan_amount))

                # # get capitalized amount
                if float(paymentDue_sum) >= float(loan_amount):

                    expected_amount = float(loan_amount) - float(top_up_balance) - float(buy_off_amount) - float(
                        other_charges)

                else:
                    # expected amount
                    print("executed else p")

                    expected_amount = float(loan_amount) - (
                            float(caveat_amount) + float(65000) + float(insurance_prem_amount) +
                            float(top_up_balance) + float(10000) + float(
                        buy_off_amount) + float(other_charges))
                print("GeneratedBy", generated_by)
                print("ExpectedAmount", expected_amount)

                # client details
                client_details = get_client_details(getloan_response["accountHolderKey"], urls, headers)

                client_home_address = home_address.title() if home_address is not None else ""

                if getloan_response['loanName'] == 'LBF 12 Months PSV Refinance (1MV)':
                    newExpectedAmount = expected_amount - float(creditLifeAmount) - float(75000)

                else:
                    newExpectedAmount = expected_amount - float(creditLifeAmount)
                print("expected_amount", expected_amount)
                print("creditLife", creditLifeAmount)
                product_tracking_fee_admin_fee = float(product_tracking_fee) + float(75000)
                if "PSV" in getloan_response['loanName']:
                    print("executed if")
                    context = {
                        "full_name": '{0} {1} {2}'.format(client_details.get("firstName", ""),
                                                          client_details.get("middleName", ""),
                                                          client_details.get("lastName", "")).title(),
                        "Client_phone_number": client_details.get("mobilePhone1", ""),
                        "today_date_time": today_date_time,
                        "client_home_address": client_home_address,
                        "client_email": client_details.get("emailAddress", ""),
                        "Client_fname": client_details.get("firstName", "").title(),
                        "loan_amount": '{:,}'.format(round(float(loan_amount))),
                        "accountid": accountId,
                        "loan_term": loan_term,
                        "installment_amount": '{:,}'.format(round(installment_amount)),
                        "product_tracking_fee": '{:,}'.format(round(float(product_tracking_fee))),
                        "caveat_amount": '{:,}'.format(round(float(caveat_amount))),
                        "insurance_prem_amount": '{:,}'.format(round(float(insurance_prem_amount))),
                        "buy_off_amount": '{:,}'.format(round(float(buy_off_amount))),
                        "loan_appraiser": "{0} {1}".format(users_details.get('firstName', ""),
                                                           users_details.get('lastName',
                                                                             "")) if users_details != "" else "",
                        "other_charges": '{:,}'.format(float(other_charges)),
                        "top_up_balance": '{:,}'.format(round(float(top_up_balance))),
                        "other_narration": other_narration,
                        # "arrangement_fee": '{:,}'.format(round(float(arrangement_fee_amount))),
                        # "total_due_words":total_due_words,
                        "reg_one": reg_one if reg_one is not None else "",
                        "reg_two": reg_two if reg_two is not None else "",
                        "expected_amount": '{:,}'.format(round(float(newExpectedAmount))),
                        "date_generation": dateformat.format(now, 'd-m-Y'),
                        "payee_name": payee_name,
                        "generated_by": generated_by,
                        "creditLifeAmount": '{:,}'.format(round(float(creditLifeAmount))),

                    }

                    rendered_html = render_to_string('DEP/offer/lbf_psv_offer.html', {'context': context}).encode(
                        encoding="UTF-8")
                    pdf_file = HTML(string=rendered_html, ).write_pdf(
                        stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/offer/lbf_psv_offer.css'),
                                     CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                                     CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                                     ])
                    response = HttpResponse(pdf_file, content_type='application/pdf')

                    response['Content-Disposition'] = 'attachment; filename="{0} {1} {2}-Offer Letter-{3}.pdf"'.format(
                        client_details.get("firstName", ""),
                        client_details.get("middleName", ""), client_details.get("lastName", ""), accountId)
                    return response

                elif getloan_response['loanName'].startswith('Deducted', 14) or getloan_response['loanName'].startswith(
                        'Deducted', 13) \
                        or getloan_response['loanName'] in ["LBF USSD Top Up Loans (1MV)",
                                                            "LBF USSD Top Up Loans (2MVs)"] or \
                        getloan_response['loanName'].startswith('Capitalized', 13) or getloan_response[
                    'loanName'].startswith('Capitalized', 14) \
                        or getloan_response['loanName'].startswith('LBF Buy Off') or "Capitalized" in getloan_response[
                    'loanName']:

                    # Chasis Number
                    chasis_url = urls + "loans/{0}/custominformation/{1}".format(accountId, 'cha_no')
                    chasis_details = requests.get(chasis_url, headers=headers)
                    print("executed else")

                    # print('VEHICLE REG',get_loan_details.json())
                    try:
                        chasisNumber = get_custom_field("cha_no", chasis_details.json())
                    except TypeError:
                        chasisNumber = ''
                    if getloan_response['loanName'].startswith('Car'):
                        reg_one = chasisNumber
                        chasisLabel = "CHASSIS"
                    else:
                        reg_one = reg_one
                        chasisLabel = "REG"

                    # print("chasisNumber",chasisNumber)
                    # print("chasisLabel",chasisLabel)

                    # capitalised_ded_arrangement fee
                    capitalised_ded_arrangement_amount = ''
                    ref_amount = ''
                    rfamt_url = urls + "loans/{0}/custominformation/{1}".format(accountId, 'rf01')
                    refinance_details = requests.get(rfamt_url, headers=headers)

                    # print('VEHICLE REG',get_loan_details.json())
                    try:
                        ref_amount = get_custom_field("rf01", refinance_details.json())
                    except TypeError:
                        ref_amount = ''
                    if ref_amount is not None and "Refinance" in getloan_response['loanName']:
                        capitalised_ded_arrangement_amount = 0.028 * float(ref_amount)
                    else:
                        capitalised_ded_arrangement_amount = 0.0028 * float(loan_amount)

                    loan_amount_label = ""
                    capitalized_deducted_expected_amt = 0
                    if getloan_response['loanName'].startswith('Deducted', 14) or getloan_response[
                        'loanName'].startswith('Deducted', 13) or getloan_response['loanName'].startswith(
                        'LBF Buy Off') or "Deducted" in getloan_response['loanName'] or 'PSV' in getloan_response[
                        'loanName']:
                        # print("ded")

                        loan_amount_label = "Deducted"
                        # capitalized_deducted_expected_amt = float(loan_amount)-(float(top_up_balance)- float(creditLifeAmount) + float(buy_off_amount) + float(other_charges) + float(caveat_amount)+float(insurance_prem_amount) +float(capitalised_ded_arrangement_amount))

                        capitalized_deducted_expected_amt = float(loan_amount) - (
                                float(caveat_amount) + float(appraisalFeeAmt) + float(
                            insurance_prem_amount) + float(top_up_balance) + float(buy_off_amount) + float(
                            creditLifeAmount) + float(other_charges) + float(capitalised_ded_arrangement_amount))
                        print("kukuKaka", capitalized_deducted_expected_amt)

                    insurance_prem_amountRefined = ""
                    if float(paymentDue_sum) > float(loan_amount):
                        print("cap")
                        loan_amount_label = "Capitalized"
                        capitalized_deducted_expected_amt = float(loan_amount) - (
                                float(top_up_balance) + float(buy_off_amount) + float(other_charges)) - float(creditLifeAmount)

                        insurance_prem_amountRefined = "Refer to IPF" if float(
                            insurance_prem_amount) <= 0 else insurance_prem_amount
                        # get loan amount from shdedule for capitalised and deducted\
                    else:
                        print("another else")
                        capitalized_deducted_expected_amt = float(loan_amount) - (
                                float(caveat_amount) + float(appraisalFeeAmt) + float(
                            insurance_prem_amount) + float(top_up_balance) + float(buy_off_amount) + float(
                            creditLifeAmount) + float(other_charges))

                        insurance_prem_amountRefined = insurance_prem_amount

                    if insurance_prem_amountRefined == "Refer to IPF":
                        insuranceLabel = ""
                    else:
                        insuranceLabel = "Ush"

                    paymentDue_sum = 0
                    for obj in get_repayment(accountId, headers, urls):
                        paymentDue_sum += float(obj['principalDue'])

                    print("capitalized_deducted_expected_amt", capitalized_deducted_expected_amt)

                    context = {
                        "full_name": '{0} {1} {2}'.format(client_details.get("firstName", ""),
                                                          client_details.get("middleName", ""),
                                                          client_details.get("lastName", "")).title(),
                        "Client_phone_number": client_details.get("mobilePhone1", ""),
                        "today_date_time": today_date_time,
                        "client_home_address": client_home_address,
                        "client_email": client_details.get("emailAddress", ""),
                        "Client_fname": client_details.get("firstName", "").title(),
                        "loan_amount": '{:,}'.format(round(float(loan_amount))),
                        "accountid": accountId,
                        "loan_term": loan_term,
                        "installment_amount": '{:,}'.format(round(installment_amount)),
                        "product_tracking_fee_admin_fee": '{:,}'.format(round(float(product_tracking_fee_admin_fee))),
                        "caveat_amount": '{:,}'.format(round(float(caveat_amount))),
                        "insurance_prem_amount": '{}'.format(insurance_prem_amountRefined),
                        "buy_off_amount": '{:,}'.format(round(float(buy_off_amount))),
                        "loan_appraiser": "{0} {1}".format(users_details.get('firstName', ""),
                                                           users_details.get('lastName',
                                                                             "")) if users_details != "" else "",
                        "other_charges": '{:,}'.format(float(other_charges)),
                        "top_up_balance": '{:,}'.format(round(float(top_up_balance))),
                        "other_narration": other_narration,
                        "appraisalFeeAmt": '{:,}'.format(round(float(appraisalFeeAmt))),
                        "reg_one": reg_one if reg_one is not None else "",
                        "reg_two": reg_two if reg_two is not None else "",
                        "expected_amount": '{:,}'.format(round(float(newExpectedAmount))),
                        "loan_amount_label": loan_amount_label,
                        "cap_ded_loan_amount": '{:,}'.format(round(float(paymentDue_sum))),
                        "capitalized_deducted_expected_amt": '{:,}'.format(
                            round(float(capitalized_deducted_expected_amt))),
                        "date_generation": dateformat.format(now, 'd-m-Y'),
                        "payee_name": payee_name,
                        "chasisLabel": chasisLabel,
                        "insuranceLabel": insuranceLabel,
                        "generated_by": generated_by,
                        "creditLifeAmount": '{:,}'.format(round(float(creditLifeAmount))),

                    }
                    # print("CONTEXT",context)

                    rendered_html = render_to_string('DEP/offer/LBF Capitalized & Deducted.html',
                                                     {'context': context}).encode(encoding="UTF-8")
                    pdf_file = HTML(string=rendered_html, ).write_pdf(
                        stylesheets=[
                            CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/offer/lbf_capitalised_offer.css'),
                            CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                            CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')])
                    response = HttpResponse(pdf_file, content_type='application/pdf')
                    response['Content-Disposition'] = 'attachment; filename="{0} {1} {2}-Offer Letter-{3}.pdf"'.format(
                        client_details.get("firstName", ""), client_details.get("middleName", ""),
                        client_details.get("lastName", ""), accountId)
                    return response
                else:
                    return redirect('Platinumuganda_DEP_Letters:product_access_lbf_offer')
            else:
                return redirect('Platinumuganda_DEP_Letters:account_state_lbf')
        else:
            return redirect('Platinumuganda_DEP_Letters:rolenotallowed')
    return render(request, 'DEP/offer/letters/404.html')
# SME DEP New Products Demand Notices


from urllib import response
from django.http import HttpResponse
from django.shortcuts import render
from django.utils import dateformat
from datetime import date
from datetime import datetime, timedelta
import requests
from django.views.decorators.csrf import csrf_exempt
import inflect

base_url = 'https://platinumuganda.mambu.com/api'
headers = {'content-type': 'application/json', "apiKey": "dLnGyp8KCc6xvK84TCQ5IJG2SOLnxULU"}

now = datetime.now()
today_date_time = dateformat.format(now, 'jS F Y')


# Create your views here.


#  FIRST DEMAND NOTICE
class smeFirstDemandActivity(APIView):
    authentication_classes = (BasicAuthentication,)
    permission_classes = (IsAuthenticated,)

    def post(self, request):
        first_demand_notice_sme(request.data)
        return Response({"message": "request received"}, status=status.HTTP_200_OK)


@csrf_exempt
# def first_demand_notice(data):
def first_demand_notice_sme(data):
    loanID = data['loanID']

    geturl = base_url + "/loans/{0}".format(loanID)
    getdetails = requests.get(geturl, headers=headers)
    getload = getdetails.json()

    # print("LoanDetails",getload)

    geturl = base_url + "/clients/{0}".format(getload['accountHolderKey'])
    client_details = requests.get(geturl, headers=headers)
    getClient_details = client_details.json()

    # print("Client_details", getClient_details)

    total_balance = float(getload['principalBalance']) + float(getload['interestBalance']) + float(
        getload['feesBalance']) + float(getload['penaltyBalance'])
    total_due = float(getload['principalDue']) + float(getload['interestDue']) + float(getload['feesDue']) + float(
        getload['penaltyDue'])

    # amount in words
    p = inflect.engine()
    total_balance_words = p.number_to_words(round(total_balance)).capitalize()
    total_due_words = p.number_to_words(round(total_due)).capitalize()

    # get users on the branch
    users_payload = {
        "branchID": getClient_details.get("assignedBranchKey", ""),
        "offset": "0",
        "limit": "1000",
        "fullDetails": "True"
    }
    geturl = base_url + "/users"
    # print('geturl',geturl)

    getdetails = requests.get(geturl, json=users_payload, headers=headers)

    # CREATE A TASK
    task_response = ""
    for obj in getdetails.json():
        # add Branch Coordinator role
        if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a858fbd59e06eb70159e46293a81d50" or obj[
            "userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a93876d784478b80178493f43046762":
            # print("username:",obj["username"])

            task_payload = {
                "task": {
                    "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                    "title": "SME First Demand Notice_{}.pdf".format(loanID),
                    "description": "SME First demand Notice attached for {account_id}".format(account_id=loanID),
                    "status": "OPEN",
                    "assignedUserKey": obj["username"]
                }
            }

            taskurl = base_url + "/tasks"
            taskdetails = requests.post(taskurl, json=task_payload, headers=headers)
            task_response = taskdetails.json()

            # print('task_response' , task_response)

    context = {

        "full_name": getClient_details['firstName'] + "  " + getClient_details['lastName'],
        "Client_phone_number": getClient_details["mobilePhone1"],
        "today_date_time": today_date_time,
        "loan_account": loanID,
        "total_balance": '{:,}'.format(round(total_balance)),
        "Client_fname": getClient_details['firstName'],
        "total_due": '{:,}'.format(round(total_due)),
        "total_balance_words": total_balance_words,
        "total_due_words": total_due_words,
        "clientID": getClient_details['id']
    }

    rendered_html = render_to_string('DEP/sme_NewProducts/firstdemand.html', {'context': context}).encode(
        encoding="UTF-8")
    pdf_file = HTML(string=rendered_html, ).write_pdf(
        stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/offer/lbf_schedule.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                     ])
    response = HttpResponse(pdf_file, content_type='application/pdf')

    response['Content-Disposition'] = 'attachment; filename="SME FIRST DEMAND  {0} .pdf"'.format(loanID)

    # attachDocument
    encoded_string = base64.b64encode(response.content).decode('utf-8')
    attachdoc = {
        "document": {
            "documentHolderKey": getload['encodedKey'],
            "documentHolderType": "LOAN_ACCOUNT",
            "name": "SME First Demand {}".format(loanID),
            "type": "PDF"
        },
        "documentContent": encoded_string
    }
    doc_url = base_url + "/loans/{0}/documents/".format(loanID)
    details = requests.post(doc_url, json=attachdoc, headers=headers)

    # sme_demand_letter(
    #         accountId=data["accountId"],
    #         product_name=getload['loanName'],
    #         task_response=task_response,
    #         attachment_response=details.json()
    #     ).save()

    return response


#  SECOND DEMAND NOTICE
class smeSecondDemandActivity(APIView):
    authentication_classes = (BasicAuthentication,)
    permission_classes = (IsAuthenticated,)

    def post(self, request):
        second_demand_notice_sme(request.data)
        return Response({"message": "request received"}, status=status.HTTP_200_OK)


@csrf_exempt
def second_demand_notice_sme(data):
    loanID = data['loanID']

    geturl = base_url + "/loans/{0}".format(loanID)
    getdetails = requests.get(geturl, headers=headers)
    getload = getdetails.json()

    # print("LoanDetails",getload)

    geturl = base_url + "/clients/{0}".format(getload['accountHolderKey'])
    client_details = requests.get(geturl, headers=headers)
    getClient_details = client_details.json()

    # print("Client_details", getClient_details)
    total_balance = float(getload['principalBalance']) + float(getload['interestBalance']) + float(
        getload['feesBalance']) + float(getload['penaltyBalance'])
    total_due = float(getload['principalDue']) + float(getload['interestDue']) + float(getload['feesDue']) + float(
        getload['penaltyDue'])

    # amount in words
    p = inflect.engine()
    total_balance_words = p.number_to_words(round(total_balance)).capitalize()
    total_due_words = p.number_to_words(round(total_due)).capitalize()

    users_payload = {
        "branchID": getClient_details.get("assignedBranchKey", ""),
        "offset": "0",
        "limit": "1000",
        "fullDetails": "True"
    }
    geturl = base_url + "/users"
    # print('geturl',geturl)
    getdetails = requests.get(geturl, json=users_payload, headers=headers)

    # CREATE A TASK
    task_response = ""
    for obj in getdetails.json():
        # add Branch Coordinator role
        if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a858fbd59e06eb70159e46293a81d50" or obj[
            "userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a93876d784478b80178493f43046762":
            # print("username:",obj["username"])

            task_payload = {
                "task": {
                    "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                    "title": "SME Second Demand Notice_{}.pdf".format(loanID),
                    "description": "SME Second demand Notice attached for {account_id}".format(account_id=loanID),
                    "status": "OPEN",
                    "assignedUserKey": obj["username"]
                }
            }

            taskurl = base_url + "/tasks"
            taskdetails = requests.post(taskurl, json=task_payload, headers=headers)
            task_response = taskdetails.json()

            # print('task_response' , task_response)
    context = {
        "full_name": getClient_details['firstName'] + "  " + getClient_details['lastName'],
        "Client_phone_number": getClient_details["mobilePhone1"],
        "today_date_time": today_date_time,
        "loan_account": loanID,
        "total_balance": '{:,}'.format(round(total_balance)),
        "Client_fname": getClient_details['firstName'],
        "total_due": '{:,}'.format(round(total_due)),
        "total_balance_words": total_balance_words,
        "total_due_words": total_due_words,
        "clientID": getClient_details['id']

    }
    rendered_html = render_to_string('DEP/sme_NewProducts/seconddemand.html', {'context': context}).encode(
        encoding="UTF-8")
    pdf_file = HTML(string=rendered_html, ).write_pdf(
        stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/offer/lbf_schedule.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                     ])
    response = HttpResponse(pdf_file, content_type='application/pdf')

    response['Content-Disposition'] = 'attachment; filename="SME SECOND DEMAND {0} .pdf"'.format(loanID)

    # attachDocument
    encoded_string = base64.b64encode(response.content).decode('utf-8')
    attachdoc = {
        "document": {
            "documentHolderKey": getload['encodedKey'],
            "documentHolderType": "LOAN_ACCOUNT",
            "name": "SME Second Demand {}".format(loanID),
            "type": "PDF"
        },
        "documentContent": encoded_string
    }
    doc_url = base_url + "/loans/{0}/documents/".format(loanID)
    details = requests.post(doc_url, json=attachdoc, headers=headers)

    # sme_demand_letter(
    #         accountId=data["accountId"],
    #         product_name=getload['loanName'],
    #         task_response=task_response,
    #         attachment_response=details.json()
    #     ).save()

    return response


# LOAN RECALL DEMAND NOTICE
class smeLoanRecallActivity(APIView):
    authentication_classes = (BasicAuthentication,)
    permission_classes = (IsAuthenticated,)

    def post(self, request):
        loan_recall_notice_sme(request.data)
        return Response({"message": "request received"}, status=status.HTTP_200_OK)


@csrf_exempt
def loan_recall_notice_sme(data):
    loanID = data['loanID']
    # print(loanID)
    geturl = base_url + "/loans/{0}".format(loanID)
    getdetails = requests.get(geturl, headers=headers)
    getload = getdetails.json()

    guarantorOneFname = ''
    guarantorOneSurnameFname = ''
    guarantorTwoFname = ''
    guarantorTwoSurnameFname = ""

    for loan_info in getload['customFieldValues']:
        if loan_info['customField']['name'] == "Guarantor First Name":
            guarantorOneFname = loan_info['value']
        if loan_info['customField']['name'] == "Guarantor SurName":
            guarantorOneSurnameFname = loan_info['value']
        if loan_info['customField']['name'] == "Guarantor First Name 2":
            guarantorTwoFname = loan_info['value']
        if loan_info['customField']['name'] == "Guarantor SurName 2":
            guarantorTwoSurnameFname = loan_info['value']

    # # GET CURRENT TIME AND DATE
    now = datetime.now()
    today_date_time = dateformat.format(now, 'jS F Y')
    due_date = date.today() + timedelta(days=7)
    due_date_final = due_date.strftime('%d %B %Y')

    # GET TOTAL BALANCE
    loan_balance = float(getload['principalBalance']) + float(getload['interestBalance']) + float(
        getload['feesBalance']) + float(getload['penaltyBalance'])

    # print("LoanDetails",getload)
    geturl = base_url + "/clients/{0}".format(getload['accountHolderKey'])
    client_details = requests.get(geturl, headers=headers)
    getClient_details = client_details.json()

    total_due = float(getload['principalDue']) + float(getload['interestDue']) + float(getload['feesDue']) + float(
        getload['penaltyDue'])
    # amount in words
    p = inflect.engine()
    total_due_words = p.number_to_words(round(total_due)).capitalize()

    due_dates = datetime.now() + timedelta(days=45)
    due_date_final = dateformat.format(due_dates, 'jS F Y')

    # print("Client_details", getClient_details)
    geturl = base_url + "/loans/{0}/repayments".format(loanID)
    getinstallments = requests.get(geturl, headers=headers)
    getinstallmentsresponse = getinstallments.json()
    installments = float(getinstallmentsresponse[0]['principalDue']) + float(
        getinstallmentsresponse[0]['interestDue']) + float(getinstallmentsresponse[0]['feesDue']) + float(
        getinstallmentsresponse[0]['penaltyDue'])
    total_balance = float(getload['principalBalance']) + float(getload['interestBalance']) + float(
        getload['feesBalance']) + float(getload['penaltyBalance'])
    loanAmount = float(getload['loanAmount'])

    # amount in words
    p = inflect.engine()
    total_balance_words = p.number_to_words(round(total_balance)).capitalize()
    installments_words = p.number_to_words(round(installments)).capitalize()

    loanAmount_words = p.number_to_words(round(loanAmount)).capitalize()
    activation = datetime.strptime(getload['disbursementDetails']['disbursementDate'], "%Y-%m-%dT%H:%M:%S+%f")
    activation_date = dateformat.format(activation, 'jS F Y')

    users_payload = {
        "branchID": getClient_details.get("assignedBranchKey", ""),
        "offset": "0",
        "limit": "1000",
        "fullDetails": "True"
    }
    geturl = base_url + "/users"
    # print('geturl',geturl)
    getdetails = requests.get(geturl, json=users_payload, headers=headers)

    # CREATE A TASK
    task_response = ""
    for obj in getdetails.json():
        # add Branch Coordinator role
        if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a858fbd59e06eb70159e46293a81d50" or obj[
            "userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a93876d784478b80178493f43046762":
            # print("username:",obj["username"])
            task_payload = {
                "task": {
                    "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                    "title": "SME Loan Recall Notice_{}.pdf".format(loanID),
                    "description": "SME Loan Recall Notice attached for {account_id}".format(account_id=loanID),
                    "status": "OPEN",
                    "assignedUserKey": obj["username"]
                }
            }
            taskurl = base_url + "/tasks"
            taskdetails = requests.post(taskurl, json=task_payload, headers=headers)
            task_response = taskdetails.json()
            # print('task_response' , task_response)

    context = {
        "full_name": getClient_details['firstName'] + "  " + getClient_details['lastName'],
        "Client_phone_number": getClient_details["mobilePhone1"],
        "today_date_time": today_date_time,
        "due_dates": due_date_final,
        "disbursementDate": activation_date,
        "loan_account": loanID,
        "loanAmount": '{:,}'.format(round(loanAmount)),
        "total_balance": '{:,}'.format(round(total_balance)),
        "Client_fname": getClient_details['firstName'],
        "total_balance_words": total_balance_words,
        "installments": '{:,}'.format(round(installments)),
        "installments_words": installments_words,
        "loanAmount_words": loanAmount_words,
        "clientID": client_details["id"],
        "total_due_words": total_due_words,
        "guarantorOneFname": guarantorOneFname,
        "guarantorOneSurnameFname": guarantorOneSurnameFname,
        "guarantorTwoFname": guarantorTwoFname,
        "guarantorTwoSurnameFname": guarantorTwoSurnameFname,
        "total_due": '{:,}'.format(round(total_due)),
    }

    rendered_html = render_to_string('DEP/sme_NewProducts/loanrecallletter.html', {'context': context}).encode(
        encoding="UTF-8")
    pdf_file = HTML(string=rendered_html, ).write_pdf(
        stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/offer/lbf_schedule.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                     ])
    response = HttpResponse(pdf_file, content_type='application/pdf')

    response['Content-Disposition'] = 'attachment; filename="SME LOAN RECALL {0} .pdf"'.format

    # attachDocument
    encoded_string = base64.b64encode(response.content).decode('utf-8')
    attachdoc = {
        "document": {
            "documentHolderKey": getload['encodedKey'],
            "documentHolderType": "LOAN_ACCOUNT",
            "name": "SME Loan Recall {}".format(loanID),
            "type": "PDF"
        },
        "documentContent": encoded_string
    }
    doc_url = base_url + "/loans/{0}/documents/".format(loanID)
    details = requests.post(doc_url, json=attachdoc, headers=headers)

    rendered_html = render_to_string('DEP/newTemplates/guarantorDemand2.html', {'context': context}).encode(
        encoding="UTF-8")
    pdf_file = HTML(string=rendered_html, ).write_pdf(
        stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/sme/reposession.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                     ])
    response = HttpResponse(pdf_file, content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename=" SME Guarantor Demand Notice_{}.pdf"'.format(loanID)

    # attachDocument
    encoded_string = base64.b64encode(response.content).decode('utf-8')
    attachdoc = {
        "document": {
            "documentHolderKey": getload['encodedKey'],
            "documentHolderType": "LOAN_ACCOUNT",
            "name": "SME Guarantor Two Demand Notice {}".format(loanID),
            "type": "PDF"
        },
        "documentContent": encoded_string
    }
    doc_url = base_url + "loans/{0}/documents/".format(loanID)
    details = requests.post(doc_url, json=attachdoc, headers=headers)

    sme_demand_letter(
        accountId=loanID,
        product_name=getload['loanName'],
        task_response=task_response,
        attachment_response=details.json()).save()

    # sme_demand_letter(
    #         accountId=data["accountId"],
    #         product_name=getload['loanName'],
    #         task_response=task_response,
    #         attachment_response=details.json()
    #     ).save()

    return response


# Repossession and sme demand letters
@csrf_exempt
def repossesionDemand(request):
    payload = json.loads(request.body)

    environment = payload["environment"]
    accountId = payload["accountId"]
    fullName = payload["fullName"]
    collateraldetails = payload['collateraldetails']
    branch = payload['branch']

    urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment.endswith(
        'sandbox') else 'https://platinumuganda.mambu.com/api/'

    config = get_config()
    api_key = config.get('Credentials', 'PLATINUMUG_PROD_APIKEY_DEP')

    headers = {"ApiKey": "{}".format(api_key)}
    geturl = urls + "loans/{0}?fullDetails=true".format(accountId)
    getdetails = requests.get(geturl, headers=headers)
    getloan_response = getdetails.json()
    # print("getloan_response",getloan_response['guarantees'])
    guarantorOneFname = ''
    guarantorOneSurnameFname = ''
    guarantorTwoFname = ''
    guarantorTwoSurnameFname = ""

    for loan_info in getloan_response['customFieldValues']:
        if loan_info['customField']['name'] == "Guarantor First Name":
            guarantorOneFname = loan_info['value']
        if loan_info['customField']['name'] == "Guarantor SurName":
            guarantorOneSurnameFname = loan_info['value']
        if loan_info['customField']['name'] == "Guarantor First Name 2":
            guarantorTwoFname = loan_info['value']
        if loan_info['customField']['name'] == "Guarantor SurName 2":
            guarantorTwoSurnameFname = loan_info['value']

    # # GET CURRENT TIME AND DATE
    now = datetime.now()
    today_date_time = dateformat.format(now, 'jS F Y')
    reference_date = now.strftime("%d%m%Y")

    # GET TOTAL BALANCE
    loan_balance = float(getloan_response['principalBalance']) + float(getloan_response['interestBalance']) + float(
        getloan_response['feesBalance']) + float(getloan_response['penaltyBalance'])
    days_in_arrears = (datetime.today() - datetime.strptime(getloan_response["lastSetToArrearsDate"],
                                                            "%Y-%m-%dT%H:%M:%S+%f")).days

    # client details
    client_details = get_client_details(getloan_response["accountHolderKey"], urls, headers)
    assets = getloan_response['guarantees']
    print("assets", assets)

    newAsset = []
    for asset in assets:

        try:
            if 'lgf' not in asset['assetName']:
                newAsset.append(asset['assetName'])
        except KeyError:
            pass

    # get users on the branch
    users_payload = {
        "branchID": client_details.get("assignedBranchKey", ""),
        "offset": "0",
        "limit": "1000",
        "fullDetails": "True"
    }
    geturl = urls + "users/"
    getdetails = requests.get(geturl, json=users_payload, headers=headers)
    # CREATE A TASK
    task_response = ""
    for obj in getdetails.json():
        # add Branch Coordinator role
        try:
            if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a858fbd59e06eb70159e46293a81d50" or obj[
                "userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a93876d784478b80178493f43046762":
                # print("username:",obj["username"])
                task_payload = {
                    "title": "SME Reposession Demand Notice_{}.pdf".format(accountId),
                    "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                    "description": "SME Reposession demand Notice attached for {account_id}".format(
                        account_id=accountId),
                    "status": "OPEN",
                    "clientID": getloan_response['accountHolderKey'],
                    "username": obj["username"]
                }

                taskurl = urls + "tasks"
                taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                task_response = taskdetails.json()
        except Exception:
            pass
    context = {
        "clientID": client_details["id"],
        "full_name": fullName.title(),
        "Client_phone_number": client_details.get("mobilePhone1", ""),
        "Client_fname": client_details.get("firstName", "").title(),
        "total_balance": '{:,}'.format(round(loan_balance)),
        # "total_bl_words":"words",
        "today_date_time": today_date_time,
        "guarantorOneFname": guarantorOneFname,
        "guarantorOneSurnameFname": guarantorOneSurnameFname,
        "guarantorTwoFname": guarantorTwoFname,
        "guarantorTwoSurnameFname": guarantorTwoSurnameFname,
        "formattedAssetNames": newAsset,
        "collateraldetails": collateraldetails,
        "branch": branch

    }

    # print("context",context)
    rendered_html = render_to_string('DEP/newTemplates/repossesion.html', {'context': context}).encode(
        encoding="UTF-8")
    pdf_file = HTML(string=rendered_html, ).write_pdf(
        stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/sme/reposession.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                     ])
    response = HttpResponse(pdf_file, content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename=" SME Reposession Order{}.pdf"'.format(accountId)

    # attachDocument
    encoded_string = base64.b64encode(response.content).decode('utf-8')
    attachdoc = {
        "document": {
            "documentHolderKey": getloan_response['encodedKey'],
            "documentHolderType": "LOAN_ACCOUNT",
            "name": "SME Reposession Order {}".format(accountId),
            "type": "PDF"
        },
        "documentContent": encoded_string
    }
    doc_url = urls + "loans/{0}/documents/".format(accountId)
    details = requests.post(doc_url, json=attachdoc, headers=headers)

    sme_demand_letter(
        accountId=accountId,
        product_name=getloan_response['loanName'],
        task_response=task_response,
        attachment_response=details.json()).save()
    return response


# Guarantor demand
@csrf_exempt
def guarantorDemand(request):
    payload = json.loads(request.body)

    # environment="platinumuganda.sandbox"
    # accountId="*********"
    # fullName="JULIA KATUMUSIIME"

    environment = payload["environment"]
    accountId = payload["accountId"]
    fullName = payload["fullName"]
    guarantorOneFname = payload["guarantorOneFname"]
    guarantorOneSurnameFname = payload["guarantorOneSurnameFname"]
    guarantorTwoFname = payload["guarantorTwoFname"]
    guarantorTwoSurnameFname = payload["guarantorTwoSurnameFname"]

    urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment.endswith(
        'sandbox') else 'https://platinumuganda.mambu.com/api/'

    config = get_config()
    api_key = config.get('Credentials', 'PLATINUMUG_PROD_APIKEY_DEP')

    headers = {"ApiKey": "{}".format(api_key)}
    geturl = urls + "loans/{0}?fullDetails=true".format(accountId)
    getdetails = requests.get(geturl, headers=headers)
    getloan_response = getdetails.json()

    # # GET CURRENT TIME AND DATE
    now = datetime.now()
    today_date_time = dateformat.format(now, 'jS F Y')
    due_date = date.today() + timedelta(days=7)
    due_date_final = due_date.strftime('%d %B %Y')

    # GET TOTAL BALANCE
    loan_balance = float(getloan_response['principalBalance']) + float(getloan_response['interestBalance']) + float(
        getloan_response['feesBalance']) + float(getloan_response['penaltyBalance'])
    days_in_arrears = (datetime.today() - datetime.strptime(getloan_response["lastSetToArrearsDate"],
                                                            "%Y-%m-%dT%H:%M:%S+%f")).days

    # client details
    client_details = get_client_details(getloan_response["accountHolderKey"], urls, headers)
    total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(
        getloan_response['feesDue']) + float(getloan_response['penaltyDue'])

    activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'], "%Y-%m-%dT%H:%M:%S+%f")
    activation_date = dateformat.format(activation, 'jS F Y')

    # amount in words
    p = inflect.engine()
    total_due_words = p.number_to_words(round(total_due)).capitalize()

    # get users on the branch
    users_payload = {
        "branchID": client_details.get("assignedBranchKey", ""),
        "offset": "0",
        "limit": "1000",
        "fullDetails": "True"
    }
    geturl = urls + "users/"
    getdetails = requests.get(geturl, json=users_payload, headers=headers)
    # CREATE A TASK
    task_response = ""
    for obj in getdetails.json():
        # add Branch Coordinator role
        try:
            if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a858fbd59e06eb70159e46293a81d50" or obj[
                "userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a93876d784478b80178493f43046762":
                # print("username:",obj["username"])
                task_payload = {
                    "title": "Guarantor Demand Notices_{}.pdf".format(accountId),
                    "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                    "description": "Guarantor Demand notices have been attached for {account_id}".format(
                        account_id=accountId),
                    "status": "OPEN",
                    "clientID": getloan_response['accountHolderKey'],
                    "username": obj["username"]
                }

                taskurl = urls + "tasks"
                taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                task_response = taskdetails.json()
        except Exception:
            pass
    context = {
        "clientID": client_details["id"],
        "full_name": fullName.title(),
        "Client_phone_number": client_details.get("mobilePhone1", ""),
        "Client_fname": client_details.get("firstName", "").title(),
        "total_balance": '{:,}'.format(round(loan_balance)),
        "total_due_words": total_due_words,
        "today_date_time": today_date_time,
        "guarantorOneFname": guarantorOneFname,
        "guarantorOneSurnameFname": guarantorOneSurnameFname,
        "guarantorTwoFname": guarantorTwoFname,
        "guarantorTwoSurnameFname": guarantorTwoSurnameFname,
        "total_due": '{:,}'.format(round(total_due)),
        "due_date_final": due_date_final,
        "activation_date": activation_date
    }

    # print("context",context)
    rendered_html = render_to_string('DEP/newTemplates/guarantorDemand.html', {'context': context}).encode(
        encoding="UTF-8")
    pdf_file = HTML(string=rendered_html, ).write_pdf(
        stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/sme/reposession.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                     ])
    response = HttpResponse(pdf_file, content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename=" SME Guarantor Demand Notice_{}.pdf"'.format(accountId)

    # attachDocument
    encoded_string = base64.b64encode(response.content).decode('utf-8')
    attachdoc = {
        "document": {
            "documentHolderKey": getloan_response['encodedKey'],
            "documentHolderType": "LOAN_ACCOUNT",
            "name": "SME Guarantor One Demand Notice {}".format(accountId),
            "type": "PDF"
        },
        "documentContent": encoded_string
    }
    doc_url = urls + "loans/{0}/documents/".format(accountId)
    details = requests.post(doc_url, json=attachdoc, headers=headers)

    sme_demand_letter(
        accountId=accountId,
        product_name=getloan_response['loanName'],
        task_response=task_response,
        attachment_response=details.json()).save()

    rendered_html = render_to_string('DEP/newTemplates/guarantorDemand2.html', {'context': context}).encode(
        encoding="UTF-8")
    pdf_file = HTML(string=rendered_html, ).write_pdf(
        stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/sme/reposession.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                     ])
    response = HttpResponse(pdf_file, content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename=" SME Guarantor Demand Notice_{}.pdf"'.format(accountId)

    # attachDocument
    encoded_string = base64.b64encode(response.content).decode('utf-8')
    attachdoc = {
        "document": {
            "documentHolderKey": getloan_response['encodedKey'],
            "documentHolderType": "LOAN_ACCOUNT",
            "name": "SME Guarantor Two Demand Notice {}".format(accountId),
            "type": "PDF"
        },
        "documentContent": encoded_string
    }
    doc_url = urls + "loans/{0}/documents/".format(accountId)
    # print("doc_url",doc_url)
    details = requests.post(doc_url, json=attachdoc, headers=headers)
    # print("GUARANTOR TWO ATTACH",details.json())

    sme_demand_letter(
        accountId=accountId,
        product_name=getloan_response['loanName'],
        task_response=task_response,
        attachment_response=details.json()).save()

    return response


# Test Code
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication


def Create_Service_platke(client_secret_file, api_name, api_version, *scopes):
    # print(client_secret_file, api_name, api_version, scopes, sep='-')
    CLIENT_SECRET_FILE = client_secret_file
    API_SERVICE_NAME = api_name
    API_VERSION = api_version
    SCOPES = [scope for scope in scopes[0]]
    # print(SCOPES)
    cred = None

    pickle_file = Path().absolute() / 'Platinumuganda_DEP_Letters/token_{0}_{1}.pickle'.format(API_SERVICE_NAME,
                                                                                               API_VERSION)
    # print(pickle_file)

    if os.path.exists(pickle_file):
        with open(pickle_file, 'rb') as token:
            cred = pickle.load(token)

    if not cred or not cred.valid:
        if cred and cred.expired and cred.refresh_token:
            cred.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(CLIENT_SECRET_FILE, SCOPES)
            cred = flow.run_local_server()

        with open(pickle_file, 'wb') as token:
            pickle.dump(cred, token)

    try:
        service = build(API_SERVICE_NAME, API_VERSION, credentials=cred)
        # print(API_SERVICE_NAME, 'service created successfully')
        return service
    except Exception as e:
        print('Unable to connect.')
        print(e)
        return None


def email_test(request):
    # GMAIL API START
    CLIENT_SECRET_FILE = gmail_credentials_platug
    service = Create_Service_platke(CLIENT_SECRET_FILE, 'gmail', 'v1', ['https://mail.google.com/'])
    client_email = "<EMAIL>"
    sales_rep_email = "<EMAIL>"
    branch_coodinator_email = "<EMAIL> "

    accountID = "********"

    # email body
    emailMsg = """\
    <html>
      <head>PLEASE IGNORE TEST! TEST!</head>
      <body>
        <p>Dear {fistname},<p>
        <p>Thank you for being our valued customer.</p>
        <p>This is an urgent notice on your loan account obligation which you have failed to execute as promised.
        Attached herein is a formal demand notice with respect to the same for your action. Please honor your obligation
         and have the opportunity of improving and growing your credit worthiness with us.
         </p>
        <p>For any clarification, please contact your Relationship Manager or our contact center via **********.</p>
        <p>PLATINUM CREDIT</p>

      </body>
    </html>
    """.format(fistname="Evans")
    # create email message
    mimeMessage = MIMEMultipart()
    mimeMessage['to'] = client_email
    mimeMessage['cc'] = '{0},{1},{2},{3}'.format(branch_coodinator_email, "<EMAIL>",
                                                 "<EMAIL>", "<EMAIL>")
    mimeMessage['bcc'] = sales_rep_email
    mimeMessage['subject'] = "(PLEASE IGNORE!!!)PSL First Demand Notice {}.pdf".format(accountID)
    mimeMessage.attach(MIMEText(emailMsg, 'html'))

    mimeMessage.attach(MIMEText(emailMsg, 'html'))

    # part = MIMEApplication(response.content, _subtype='application/pdf')
    # part.add_header('Content-Disposition', 'attachment', filename="PSL First Demand Notice {}.pdf".format(accountID))
    # mimeMessage.attach(part)
    raw_string = base64.urlsafe_b64encode(mimeMessage.as_bytes()).decode()

    try:
        message = service.users().messages().send(
            userId='me',
            body={'raw': raw_string}).execute()
    except HttpError as err:
        message = err
        pass

    print("message", message)


def noticeOfDefault(request):
    # payload=json.loads(request.body)
    # environment = payload['environment']
    # accountId = payload['accountId']
    # fullName = payload['fullName']

    environment = "platinumuganda.sandbox"
    accountId = "*********"
    fullName = "Evans Test"

    urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment.endswith(
        'sandbox') else 'https://platinumuganda.mambu.com/api/'

    headers = {"ApiKey": "QSbVGnqI9ROyuchjadaPXHDjyJ4ttbwY"}
    geturl = urls + "loans/{0}".format(accountId)
    getdetails = requests.get(geturl, headers=headers)
    getloan_response = getdetails.json()

    # # GET CURRENT TIME AND DATE
    now = datetime.now()
    today_date_time = dateformat.format(now, 'jS F Y')
    reference_date = now.strftime("%d%m%Y")

    # GET TOTAL BALANCE
    total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(
        getloan_response['feesDue']) + float(getloan_response['penaltyDue'])
    loan_balance = float(getloan_response['principalBalance']) + float(getloan_response['interestBalance']) + float(
        getloan_response['feesBalance']) + float(getloan_response['penaltyBalance'])
    days_in_arrears = (datetime.today() - datetime.strptime(getloan_response["lastSetToArrearsDate"],
                                                            "%Y-%m-%dT%H:%M:%S+%f")).days

    # client details
    client_details = get_client_details(getloan_response["accountHolderKey"], urls, headers)
    # get users on the branch
    users_payload = {
        "branchID": client_details.get("assignedBranchKey", ""),
        "offset": "0",
        "limit": "1000",
        "fullDetails": "True"
    }
    geturl = urls + "users/"
    getdetails = requests.get(geturl, json=users_payload, headers=headers)
    # CREATE A TASK
    task_response = ""
    # for obj in getdetails.json():
    #     # add Branch Coordinator role
    #     try:
    #         if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a858fbd59e06eb70159e46293a81d50" or obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a93876d784478b80178493f43046762":
    #             # print("username:",obj["username"])
    #             task_payload = {
    #                 "title": "SME First Demand Notice_{}.pdf".format(accountId),
    #                 "dueDate": date.today().strftime('%Y-%m-%d')[:10],
    #                 "description": "SME First demand Notice attached for {account_id}".format(account_id=accountId),
    #                 "status": "OPEN",
    #                 "clientID": getloan_response['accountHolderKey'],
    #                 "username": obj["username"]
    #             }
    #
    #             taskurl = urls + "tasks"
    #             taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
    #             task_response = taskdetails.json()
    #     except Exception:
    #         pass
    context = {
        "full_name": fullName.title(),
        "Client_phone_number": client_details.get("mobilePhone1", ""),
        "today_date_time": today_date_time,
        "loan_account": accountId,
        "loan_balance": '{:,}'.format(round(loan_balance)),
        "Client_fname": client_details.get("firstName", "").title(),
        "days_in_arrears": days_in_arrears,
        "total_due": '{:,}'.format(round(total_due)),
        "reference_date": reference_date,
        "accountID": accountId,
        "clientID": client_details["id"]

    }
    rendered_html = render_to_string('DEP/sme/NoticeofDefault.html', {'context': context}).encode(
        encoding="UTF-8")
    pdf_file = HTML(string=rendered_html, ).write_pdf(
        stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/sme/first_demand_notice.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                     CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                     ])
    response = HttpResponse(pdf_file, content_type='application/pdf')
    response['Content-Disposition'] = 'attachment; filename=" Notice of Default_{}.pdf"'.format(accountId)

    # attachDocument
    # encoded_string = base64.b64encode(response.content).decode('utf-8')
    # attachdoc = {
    #     "document": {
    #         "documentHolderKey": getloan_response['encodedKey'],
    #         "documentHolderType": "LOAN_ACCOUNT",
    #         "name": "Notice of Default {}".format(accountId),
    #         "type": "PDF"
    #     },
    #     "documentContent": encoded_string
    # }
    # doc_url = urls + "loans/{0}/documents/".format(accountId)
    # details = requests.post(doc_url, json=attachdoc, headers=headers)

    # sme_demand_letter(
    #     accountId=accountId,
    #     product_name=getloan_response['loanName'],
    #     task_response=task_response,
    #     attachment_response=details.json()).save()
    return response


# NEW OFFER LETTERS
@csrf_exempt
def offerLetters(request):
    # signed_request="2b1e27b858a824c14c517062cd510f5e7e4fb5d6b6cf268d9adbe3959964e27c.**********************************************************************************************************************************************************************************************************************************"

    signed_request = request.POST.get('signed_request')
    if signed_request is not None:
        encoded_string = signed_request.split('.')[1]
        encoded_string += "=" * ((4 - len(encoded_string) % 4) % 4)
        decoded_string = base64.b64decode(encoded_string)
        obj = json.loads(decoded_string)

        accountId = json.loads(decoded_string.decode('utf-8'))['OBJECT_ID']
        loggedin_user_id = json.loads(decoded_string.decode('utf-8'))['USER_KEY']

        environment_name = obj["DOMAIN"].split('.mambu.com', 1)[0]

        config = configparser.ConfigParser()
        config.read('secrets.ini')
        pclugOfferLetters = config.get('Credentials', 'PLATINUMUG_SANDBOX_OFFER_LETTERS')
        headers = {"ApiKey": "{}".format(pclugOfferLetters)}

        mambu = Mambu("ApiKey", pclugOfferLetters, environment_name)
        mambu_user = mambu.get_users(username=loggedin_user_id, fullDetails=True)

        print("mambuUseer", mambu_user)

        userKey = mambu_user["encodedKey"] if "role" in mambu_user else ""
        # System Admin,Head of credit
        allowed_roles = ["8a858f955a61ad4e015a647a84bd449f", "8a858edb5a70bfe0015a7466a4696f35",
                         "8a9387d0623cd664016247fb53a64a68", "8a9386046611d9b9016614836c612ac8",
                         "8a858fa459e0a1340159ea1e00d74e4d", "8a858fa459e0a1340159e5f5c5832ceb",
                         "8a9387716669165601666cdf48e833ae", "8a858e9b5a61655b015a65faac1b18da",
                         "8a9387c16150537f016151b514402de5","8a858f955a61ad4e015a6458238a3eb1","8a9387fe685f356201685fa6444b07ce"]
        if userKey in allowed_roles:
            mambu_user["canManage"] = True
            mambu_user["isAdministrator"] = True
            urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment_name.endswith(
                'sandbox') else 'https://platinumuganda.mambu.com/api/'

            geturl = urls + "loans/{0}?fullDetails=true".format(accountId)
            getdetails = requests.get(geturl, headers=headers)
            getloan_response = getdetails.json()
            # print("disbursementDetails",getloan_response['disbursementDetails']['fees'])
            credit_life_insurance_amount = None

            # Loop through the list and get the amount for 'Appraisal Fees' and 'Caveat and Chattel Registration'
            for fee in getloan_response['disbursementDetails']['fees']:
                fee_name = fee['fee']['name']
                print("fee_name", fee_name)
                if fee_name == "Credit Life Insurance":
                    credit_life_insurance_amount = fee['amount']
                    break

            repaymentInstallments = getloan_response.get("repaymentInstallments", "")
            loanAmount = getloan_response.get("loanAmount", "")
            principalBalance = getloan_response.get("principalBalance", "")

            # Convert repaymentInstallments to an integer
            repaymentInstallments = int(repaymentInstallments)

            # get refincement details amount
            get_refin_details = urls + "loans/{0}/custominformation/{1}".format(accountId, 'rf01')
            getdetails = requests.get(get_refin_details, headers=headers)
            getloan_responseCF = getdetails.json()

            paymentDue_sum = 0
            for obj in get_repayment(accountId, headers, urls):
                paymentDue_sum += float(obj['principalDue'])

            if not getloan_responseCF:
                print("repaymentInstallments", repaymentInstallments)
                # new loan
                if repaymentInstallments <= 12:

                    fee_rate = 0.0030  # 0.30% expressed as decimal
                else:
                    print("executed else")
                    fee_rate = 0.0040  # 0.40% expressed as decimal

                creditLifeAmount = float(paymentDue_sum) * fee_rate
                # print("creditLifeAmount", creditLifeAmount)


            else:
                # Refinance loan
                creditLifeAmount = credit_life_insurance_amount

            if getloan_response['accountState'] == "PENDING_APPROVAL":

                # loan amount
                loan_amount = getloan_response.get("loanAmount", "")

                # loan term
                loan_term = getloan_response.get("repaymentInstallments", "")

                # get installment amount
                installment_amount = get_repayments(accountId, headers, urls)

                # get tracking fee from product
                product_tracking_fee = get_tracking_fee(getloan_response["productTypeKey"], headers, urls)
                # print("product_tracking_fee",product_tracking_fee)

                # Caveat & Chattel registration fees
                caveat_amount = 0
                for obj in getloan_response["disbursementDetails"]["fees"]:
                    if obj["fee"]["name"] == "Caveat and Chattel Registration":
                        caveat_amount = obj["amount"]

                # insurance premium
                insurance_prem_amount = 0
                for obj in getloan_response["disbursementDetails"]["fees"]:
                    if obj["fee"]["name"] == "Insurance Premium":
                        insurance_prem_amount = obj["amount"]

                # print("insurance_prem_amount", insurance_prem_amount)

                # get refinances
                refinanceUrl = urls + "loans/{0}/custominformation/{1}".format(accountId, 'rf01')
                ref_details = requests.get(refinanceUrl, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                refinanceAmount = get_custom_field("rf01", ref_details.json())

                if refinanceAmount is None:
                    refinanceAmount = 0

                print("refinanceAmount",refinanceAmount)       
                # arrangement fee

                if float(refinanceAmount) >0.0:
                    appraisalFeeAmt = (7.5/100)* float(refinanceAmount)
                else:    
                    appraisalFeeAmt =   appraisalFeeAmt =(7.5/100)* float(loanAmount)

                print("appraisalFeeAmt",appraisalFeeAmt)
                # TOP UP BALANCE
                top_up_bal = urls + "loans/{0}/custominformation/{1}".format(accountId, 'top_up_offer_letter')
                top_up_details = requests.get(top_up_bal, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                try:
                    top_up_balance_amount = get_custom_field("top_up_offer_letter", top_up_details.json())
                except TypeError:
                    top_up_balance_amount = ''
                top_up_balance = top_up_balance_amount if top_up_balance_amount is not None else 0

                now = datetime.now()
                today_date_time = dateformat.format(now, 'jS F Y')

                # client home address
                get_details_home_details = urls + "clients/{0}/custominformation/{1}".format(
                    getloan_response.get("accountHolderKey"), 'ha1')
                get_add_details = requests.get(get_details_home_details, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                try:
                    home_address = get_custom_field("ha1", get_add_details.json())
                except TypeError:
                    home_address = ''

                # buy off amount
                get_details_buy_details = urls + "loans/{0}/custominformation/{1}".format(accountId, 'buy_off_amount')
                buy_off_details = requests.get(get_details_buy_details, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                try:
                    buy_off_amount_check = get_custom_field("buy_off_amount", buy_off_details.json())
                except TypeError:
                    buy_off_amount_check = ''
                buy_off_amount = buy_off_amount_check if buy_off_amount_check is not None else 0

                # loan appraiser
                get_details_appraiser = urls + "loans/{0}/custominformation/{1}".format(accountId, '_APP_01')
                appraiser_details = requests.get(get_details_appraiser, headers=headers)
                # get users details
                users_details = ''
                for user_key in appraiser_details.json():
                    users_details = get_document_generator(user_key["linkedEntityKeyValue"], urls, headers)
                # print("users_details",users_details)

                get_user = get_document_generator(loggedin_user_id, urls, headers)

                generated_by = "{0} {1}".format(get_user['firstName'], get_user['lastName'])

                # Vehicle Reg number 1
                reg_no_1 = urls + "loans/{0}/custominformation/{1}".format(accountId, 'reg_no')
                reg_1_details = requests.get(reg_no_1, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                try:
                    reg_one = get_custom_field("reg_no", reg_1_details.json())
                except TypeError:
                    reg_one = ''

                # Vehicle Reg number 2
                reg_no_2 = urls + "loans/{0}/custominformation/{1}".format(accountId, 'reg_no02')
                reg_2_details = requests.get(reg_no_2, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                try:
                    reg_two = get_custom_field("reg_no02", reg_2_details.json())
                except TypeError:
                    reg_two = ''

                # other charges
                other_charges_details = urls + "loans/{0}/custominformation/{1}".format(accountId, 'other_charges')
                other_charges_res = requests.get(other_charges_details, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                try:
                    other_charges_amount = get_custom_field("other_charges", other_charges_res.json())
                except TypeError:
                    other_charges_amount = ''

                other_charges = other_charges_amount if other_charges_amount is not None else 0

                # narration other charges
                narration_url = urls + "loans/{0}/custominformation/{1}".format(accountId, 'other_narration_charges')
                narration_details = requests.get(narration_url, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                try:
                    other_narration = get_custom_field("other_narration_charges", narration_details.json())
                except TypeError:
                    other_narration = ''

                # offer letter CF
                offer_letter_url = urls + "loans/{0}/custominformation/{1}".format(accountId, 'offer_letter')
                offer_letter_details = requests.get(offer_letter_url, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                try:
                    offer_letter = get_custom_field("offer_letter", offer_letter_details.json())
                except TypeError:
                    offer_letter = ''

                # PAYEE NAME
                payee_letter_url = urls + "loans/{0}/custominformation/{1}".format(accountId, 'payee_name')
                payee_letter_details = requests.get(payee_letter_url, headers=headers)

                # print('VEHICLE REG',get_loan_details.json())
                try:
                    payee_name = get_custom_field("payee_name", payee_letter_details.json())
                except TypeError:
                    payee_name = ''

                if offer_letter is None or offer_letter != 'Approved':
                    return redirect('Platinumuganda_DEP_Letters:offer_letter_blank')

                # print("Principal Due", float(paymentDue_sum))
                # print("loanAmount", float(loan_amount))

                # # get capitalized amount
                if float(paymentDue_sum) >= float(loan_amount):

                    expected_amount = float(loan_amount) - float(top_up_balance) - float(buy_off_amount) - float(
                        other_charges)

                else:
                    # expected amount
                    print("executed else p")

                    expected_amount = float(loan_amount) - (
                            float(caveat_amount) + float(65000) + float(insurance_prem_amount) +
                            float(top_up_balance) + float(10000) + float(
                        buy_off_amount) + float(other_charges))
                print("GeneratedBy", generated_by)
                print("ExpectedAmount", expected_amount)

                # client details
                client_details = get_client_details(getloan_response["accountHolderKey"], urls, headers)

                client_home_address = home_address.title() if home_address is not None else ""

                if getloan_response['loanName'] == 'LBF 12 Months PSV Refinance (1MV)':
                    newExpectedAmount = expected_amount - float(creditLifeAmount) - float(75000)

                else:
                    newExpectedAmount = expected_amount - float(creditLifeAmount)
                print("expected_amount", expected_amount)
                print("creditLife", creditLifeAmount)
                product_tracking_fee_admin_fee = float(product_tracking_fee) + float(75000)
                if "PSV" in getloan_response['loanName']:
                    print("executed if")
                    context = {
                        "full_name": '{0} {1} {2}'.format(client_details.get("firstName", ""),
                                                          client_details.get("middleName", ""),
                                                          client_details.get("lastName", "")).title(),
                        "Client_phone_number": client_details.get("mobilePhone1", ""),
                        "today_date_time": today_date_time,
                        "client_home_address": client_home_address,
                        "client_email": client_details.get("emailAddress", ""),
                        "Client_fname": client_details.get("firstName", "").title(),
                        "loan_amount": '{:,}'.format(round(float(loan_amount))),
                        "accountid": accountId,
                        "loan_term": loan_term,
                        "installment_amount": '{:,}'.format(round(installment_amount)),
                        "product_tracking_fee": '{:,}'.format(round(float(product_tracking_fee))),
                        "caveat_amount": '{:,}'.format(round(float(caveat_amount))),
                        "insurance_prem_amount": '{:,}'.format(round(float(insurance_prem_amount))),
                        "buy_off_amount": '{:,}'.format(round(float(buy_off_amount))),
                        "loan_appraiser": "{0} {1}".format(users_details.get('firstName', ""),
                                                           users_details.get('lastName',
                                                                             "")) if users_details != "" else "",
                        "other_charges": '{:,}'.format(float(other_charges)),
                        "top_up_balance": '{:,}'.format(round(float(top_up_balance))),
                        "other_narration": other_narration,
                        "arrangement_fee": '{:,}'.format(round(float(arrangement_fee_amount))),
                        # "total_due_words":total_due_words,
                        "reg_one": reg_one if reg_one is not None else "",
                        "reg_two": reg_two if reg_two is not None else "",
                        "expected_amount": '{:,}'.format(round(float(newExpectedAmount))),
                        "date_generation": dateformat.format(now, 'd-m-Y'),
                        "payee_name": payee_name,
                        "generated_by": generated_by,
                        "creditLifeAmount": '{:,}'.format(round(float(creditLifeAmount))),

                    }

                    rendered_html = render_to_string('DEP/offer/lbf_psv_offer.html', {'context': context}).encode(
                        encoding="UTF-8")
                    pdf_file = HTML(string=rendered_html, ).write_pdf(
                        stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/offer/lbf_psv_offer.css'),
                                     CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                                     CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                                     ])
                    response = HttpResponse(pdf_file, content_type='application/pdf')

                    response['Content-Disposition'] = 'attachment; filename="{0} {1} {2}-Offer Letter-{3}.pdf"'.format(
                        client_details.get("firstName", ""),
                        client_details.get("middleName", ""), client_details.get("lastName", ""), accountId)
                    return response

                elif getloan_response['loanName'].startswith('Deducted', 14) or getloan_response['loanName'].startswith(
                        'Deducted', 13) \
                        or getloan_response['loanName'] in ["LBF USSD Top Up Loans (1MV)",
                                                            "LBF USSD Top Up Loans (2MVs)"] or \
                        getloan_response['loanName'].startswith('Capitalized', 13) or getloan_response[
                    'loanName'].startswith('Capitalized', 14) \
                        or getloan_response['loanName'].startswith('LBF Buy Off') or "Capitalized" in getloan_response[
                    'loanName']:

                    # Chasis Number
                    chasis_url = urls + "loans/{0}/custominformation/{1}".format(accountId, 'cha_no')
                    chasis_details = requests.get(chasis_url, headers=headers)
                    print("executed else")

                    # print('VEHICLE REG',get_loan_details.json())
                    try:
                        chasisNumber = get_custom_field("cha_no", chasis_details.json())
                    except TypeError:
                        chasisNumber = ''
                    if getloan_response['loanName'].startswith('Car'):
                        reg_one = chasisNumber
                        chasisLabel = "CHASSIS"
                    else:
                        reg_one = reg_one
                        chasisLabel = "REG"

                    # print("chasisNumber",chasisNumber)
                    # print("chasisLabel",chasisLabel)

                    # capitalised_ded_arrangement fee
                    capitalised_ded_arrangement_amount = ''
                    ref_amount = ''
                    rfamt_url = urls + "loans/{0}/custominformation/{1}".format(accountId, 'rf01')
                    refinance_details = requests.get(rfamt_url, headers=headers)

                    # print('VEHICLE REG',get_loan_details.json())
                    try:
                        ref_amount = get_custom_field("rf01", refinance_details.json())
                    except TypeError:
                        ref_amount = ''
                    if ref_amount is not None and "Refinance" in getloan_response['loanName']:
                        capitalised_ded_arrangement_amount = 0.028 * float(ref_amount)
                    else:
                        capitalised_ded_arrangement_amount = 0.0028 * float(loan_amount)

                    loan_amount_label = ""
                    capitalized_deducted_expected_amt = 0
                    if getloan_response['loanName'].startswith('Deducted', 14) or getloan_response[
                        'loanName'].startswith('Deducted', 13) or getloan_response['loanName'].startswith(
                        'LBF Buy Off') or "Deducted" in getloan_response['loanName'] or 'PSV' in getloan_response[
                        'loanName']:
                        # print("ded")

                        loan_amount_label = "Deducted"
                        # capitalized_deducted_expected_amt = float(loan_amount)-(float(top_up_balance)- float(creditLifeAmount) + float(buy_off_amount) + float(other_charges) + float(caveat_amount)+float(insurance_prem_amount) +float(capitalised_ded_arrangement_amount))

                        capitalized_deducted_expected_amt = float(loan_amount) - (
                                float(caveat_amount) + float(appraisalFeeAmt) + float(
                            insurance_prem_amount) + float(top_up_balance) + float(buy_off_amount) + float(
                            creditLifeAmount) + float(other_charges) + float(capitalised_ded_arrangement_amount))
                        print("kukuKaka", capitalized_deducted_expected_amt)

                    insurance_prem_amountRefined = ""
                    if float(paymentDue_sum) > float(loan_amount):
                        print("cap")
                        loan_amount_label = "Capitalized"
                        capitalized_deducted_expected_amt = float(loan_amount) - (
                                float(top_up_balance) + float(buy_off_amount) + float(other_charges)) - float(creditLifeAmount) 

                        insurance_prem_amountRefined = "Refer to IPF" if float(
                            insurance_prem_amount) <= 0 else insurance_prem_amount
                        # get loan amount from shdedule for capitalised and deducted\
                    else:
                        print("another else")
                        capitalized_deducted_expected_amt = float(loan_amount) - (
                                float(caveat_amount) + float(appraisalFeeAmt) + float(
                            insurance_prem_amount) + float(top_up_balance) + float(buy_off_amount) + float(
                            creditLifeAmount) + float(other_charges))

                        insurance_prem_amountRefined = insurance_prem_amount

                    if insurance_prem_amountRefined == "Refer to IPF":
                        insuranceLabel = ""
                    else:
                        insuranceLabel = "Ush"

                    paymentDue_sum = 0
                    for obj in get_repayment(accountId, headers, urls):
                        paymentDue_sum += float(obj['principalDue'])

                    print("capitalized_deducted_expected_amt", capitalized_deducted_expected_amt)

                    context = {
                        "full_name": '{0} {1} {2}'.format(client_details.get("firstName", ""),
                                                          client_details.get("middleName", ""),
                                                          client_details.get("lastName", "")).title(),
                        "Client_phone_number": client_details.get("mobilePhone1", ""),
                        "today_date_time": today_date_time,
                        "client_home_address": client_home_address,
                        "client_email": client_details.get("emailAddress", ""),
                        "Client_fname": client_details.get("firstName", "").title(),
                        "loan_amount": '{:,}'.format(round(float(loan_amount))),
                        "accountid": accountId,
                        "loan_term": loan_term,
                        "installment_amount": '{:,}'.format(round(installment_amount)),
                        "product_tracking_fee_admin_fee": '{:,}'.format(round(float(product_tracking_fee_admin_fee))),
                        "caveat_amount": '{:,}'.format(round(float(caveat_amount))),
                        "insurance_prem_amount": '{}'.format(insurance_prem_amountRefined),
                        "buy_off_amount": '{:,}'.format(round(float(buy_off_amount))),
                        "loan_appraiser": "{0} {1}".format(users_details.get('firstName', ""),
                                                           users_details.get('lastName',
                                                                             "")) if users_details != "" else "",
                        "other_charges": '{:,}'.format(float(other_charges)),
                        "top_up_balance": '{:,}'.format(round(float(top_up_balance))),
                        "other_narration": other_narration,
                        "appraisalFeeAmt": '{:,}'.format(round(float(appraisalFeeAmt))),
                        "reg_one": reg_one if reg_one is not None else "",
                        "reg_two": reg_two if reg_two is not None else "",
                        "expected_amount": '{:,}'.format(round(float(newExpectedAmount))),
                        "loan_amount_label": loan_amount_label,
                        "cap_ded_loan_amount": '{:,}'.format(round(float(paymentDue_sum))),
                        "capitalized_deducted_expected_amt": '{:,}'.format(
                            round(float(capitalized_deducted_expected_amt))),
                        "date_generation": dateformat.format(now, 'd-m-Y'),
                        "payee_name": payee_name,
                        "chasisLabel": chasisLabel,
                        "insuranceLabel": insuranceLabel,
                        "generated_by": generated_by,
                        "creditLifeAmount": '{:,}'.format(round(float(creditLifeAmount))),

                    }
                    # print("CONTEXT",context)

                    rendered_html = render_to_string('DEP/offer/LBF Capitalized & Deducted.html',
                                                     {'context': context}).encode(encoding="UTF-8")
                    pdf_file = HTML(string=rendered_html, ).write_pdf(
                        stylesheets=[
                            CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/offer/lbf_capitalised_offer.css'),
                            CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                            CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')])
                    response = HttpResponse(pdf_file, content_type='application/pdf')
                    response['Content-Disposition'] = 'attachment; filename="{0} {1} {2}-Offer Letter-{3}.pdf"'.format(
                        client_details.get("firstName", ""), client_details.get("middleName", ""),
                        client_details.get("lastName", ""), accountId)
                    return response
                else:
                    return redirect('Platinumuganda_DEP_Letters:product_access_lbf_offer')
            else:
                return redirect('Platinumuganda_DEP_Letters:account_state_lbf')
        else:
            return redirect('Platinumuganda_DEP_Letters:rolenotallowed')
    return render(request, 'DEP/offer/letters/404.html')
