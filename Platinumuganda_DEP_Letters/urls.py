from django.conf.urls import url
from . import views
app_name = 'Platinumuganda_DEP_Letters'

urlpatterns = [
    # PSL first demand notice
    # url(r'^pslfirstDemandNoticeActivity/', views.PslfirstDemandNoticeActivity.as_view()),
    url(r'^pslfirstDemandNoticeActivity/', views.PslfirstDemandNoticeActivity),

    # PSL second demand notice
    # url(r'^pslsecondDemandNoticeActivity/', views.PslsecondDemandNoticeActivity.as_view()),
    url(r'^pslsecondDemandNoticeActivity/', views.PslsecondDemandNoticeActivity),

    # PSLfinal demand notice
    # url(r'^pslfinalDemandNoticeActivity/', views.PslfinalDemandNoticeActivity.as_view()),

    url(r'^pslfinalDemandNoticeActivity/', views.PslfinalDemandNoticeActivity),

    # SME first demand notice
    # url(r'^smefirstDemandNoticeActivity/', views.SmefirstDemandNoticeActivity.as_view()),
    # edited version
    url(r'^smefirstDemandNoticeActivity/', views.sme_first_demand_notice),

    # SME second demand notice
    url(r'^smesecondDemandNoticeActivity/', views.SmesecondDemandNoticeActivity),

    # SME final demand notice
    # url(r'^smeLoanRecallActivityV2/', views.smeLoanRecallActivityV2.as_view()),
    url(r'^smeLoanRecallActivityV2/', views.smeLoanRecallActivityV2),

    # LBF DEMAND NOTICE
    # url(r'^LBFDemandActivity/', views.LBFDemandActivity.as_view()),
    url(r'^LBFDemandActivity/', views.LBFDemandActivity),

    # OFFER SCHEDULE
    url('lbf_offer_schedule/$', views.offer_letter_schedule, name='lbf_schedule'),
    url('role_not_allowed/', views.role_not_allowed, name='rolenotallowed'),
    url('product_not_allowed/', views.product_not_allowed, name='product_access'),
    url('account_state/', views.account_state, name='account_state'),


    # OFFER LETTER
    url('lbf_offer_letters/$', views.offer_lbf_psv_letter, name='offer_lbf_letter'),
    url('product_not_allowed_lbf/', views.product_not_allowed_lbf, name='product_access_lbf_offer'),
    url('account_state_lbf/', views.account_state_lbf, name='account_state_lbf'),
    url('offer_letter_blank/', views.offer_blank_check, name='offer_letter_blank'),


    # OFFER LETTERS
    url(r'^offerLetters/', views.offerLetters),

    url(r'firstDemandsme_test',views.first_demand_notice_sme,name = 'first_demand_notice_sme'),
    url(r'secondDemandsme_test',views.second_demand_notice_sme,name = 'second_demand_notice_sme'),
    url(r'loanRecallsme_test',views.loan_recall_notice_sme,name = 'loan_recall_notice_sme'),

    url(r'^smeFirstDemandActivity/', views.smeFirstDemandActivity.as_view()),
    url(r'^smeSecondDemandActivity/', views.smeSecondDemandActivity.as_view()),
    url(r'^smeLoanRecallActivity/', views.smeLoanRecallActivity.as_view()),
    

    # Reposession and sme new docs
    url(r'repossesionDemand', views.repossesionDemand),
    url(r'guarantorDemand', views.guarantorDemand),

    url(r'^test_email/', views.email_test, name="test_email"),

    # Notice of Default
    url(r'^noticeOfDefault/', views.noticeOfDefault),

    # url(r'^testLbfDemand/', views.lbf_demand_letter),
]
