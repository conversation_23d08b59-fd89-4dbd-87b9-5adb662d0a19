from django.db import models

class psl_demand_letter_new(models.Model):
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    accountId = models.CharField(max_length=255, null=True, blank=True,)
    attachment_response = models.TextField(null=True)
    email_response = models.TextField(null=True)
    product_name = models.CharField(max_length=255, null=True)
    task_response = models.TextField(null=True)



    def __str__(self):
        return "{0}: {1}".format(self.accountId, self.attachment_response)

    class Meta:
        ordering = ('accountId',)

class sme_demand_letter(models.Model):
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    accountId = models.CharField(max_length=255, null=True, blank=True,)
    attachment_response = models.TextField(null=True)
    email_response = models.TextField(null=True)
    product_name = models.CharField(max_length=255, null=True)
    task_response = models.TextField(null=True)



    def __str__(self):
        return "{0}: {1}".format(self.accountId, self.attachment_response)

    class Meta:
        ordering = ('accountId',)


class lbf_demand_notice(models.Model):
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    accountId = models.CharField(max_length=255, null=True, blank=True,)
    attachment_response = models.TextField(null=True)
    email_response = models.TextField(null=True)
    product_name = models.CharField(max_length=255, null=True)
    task_response = models.TextField(null=True)
    task_response_demand_off = models.TextField(null=True)




    def __str__(self):
        return "{0}: {1}".format(self.accountId, self.attachment_response)

    class Meta:
        ordering = ('accountId',)


# SCHEDULING TASKS
# PSL FIRST DEMAND NOTICE SCHEDULING TASKS
class psl_first_demand_weekdays(models.Model):
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    accountId = models.CharField(max_length=255, null=True, blank=True,unique=True)
    environment = models.CharField(max_length=255, null=True, blank=True,)
    fullName = models.CharField(max_length=255, null=True, blank=True,)
    employer_Name = models.CharField(max_length=255, null=True, blank=True,)

    def __str__(self):
        return "{0}".format(self.accountId)

    class Meta:
        ordering = ('accountId',)

class psl_first_demand_weekend(models.Model):
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    accountId = models.CharField(max_length=255, null=True, blank=True,unique=True)
    environment = models.CharField(max_length=255, null=True, blank=True,)
    fullName = models.CharField(max_length=255, null=True, blank=True,)
    employer_Name = models.CharField(max_length=255, null=True, blank=True,)

    def __str__(self):
        return "{0}".format(self.accountId)

    class Meta:
        ordering = ('accountId',)

# PSL SECOND DEMAND NOTICE SCHEDULING TASKS
class psl_second_demand_weekdays(models.Model):
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    accountId = models.CharField(max_length=255, null=True, blank=True,unique=True)
    environment = models.CharField(max_length=255, null=True, blank=True,)
    fullName = models.CharField(max_length=255, null=True, blank=True,)
    employer_Name = models.CharField(max_length=255, null=True, blank=True,)

    def __str__(self):
        return "{0}".format(self.accountId)

    class Meta:
        ordering = ('accountId',)

class psl_second_demand_weekend(models.Model):
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    accountId = models.CharField(max_length=255, null=True, blank=True,unique=True)
    environment = models.CharField(max_length=255, null=True, blank=True,)
    fullName = models.CharField(max_length=255, null=True, blank=True,)
    employer_Name = models.CharField(max_length=255, null=True, blank=True,)

    def __str__(self):
        return "{0}".format(self.accountId)

    class Meta:
        ordering = ('accountId',)


# PSL FINAL DEMAND NOTICE SCHEDULING TASKS
class psl_final_demand_weekdays(models.Model):
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    accountId = models.CharField(max_length=255, null=True, blank=True,unique=True)
    environment = models.CharField(max_length=255, null=True, blank=True,)
    fullName = models.CharField(max_length=255, null=True, blank=True,)
    employer_Name = models.CharField(max_length=255, null=True, blank=True,)

    def __str__(self):
        return "{0}".format(self.accountId)

    class Meta:
        ordering = ('accountId',)

class psl_final_demand_weekend(models.Model):
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    accountId = models.CharField(max_length=255, null=True, blank=True,unique=True)
    environment = models.CharField(max_length=255, null=True, blank=True,)
    fullName = models.CharField(max_length=255, null=True, blank=True,)
    employer_Name = models.CharField(max_length=255, null=True, blank=True,)

    def __str__(self):
        return "{0}".format(self.accountId)

    class Meta:
        ordering = ('accountId',)


#LBF DEMAND NOTICE SCHEDULING TASKS
class LBF_demand_weekdays(models.Model):
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    accountId = models.CharField(max_length=255, null=True, blank=True,unique=True)
    environment = models.CharField(max_length=255, null=True, blank=True,)
    fullName = models.CharField(max_length=255, null=True, blank=True,)

    def __str__(self):
        return "{0}".format(self.accountId)

    class Meta:
        ordering = ('accountId',)


class LBF_demand_weekend(models.Model):
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    accountId = models.CharField(max_length=255, null=True, blank=True,unique=True)
    environment = models.CharField(max_length=255, null=True, blank=True,)
    fullName = models.CharField(max_length=255, null=True, blank=True,)

    def __str__(self):
        return "{0}".format(self.accountId)

    class Meta:
        ordering = ('accountId',)





