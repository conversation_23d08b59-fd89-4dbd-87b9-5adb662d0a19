from django.contrib import admin
from .models import psl_demand_letter_new,sme_demand_letter,lbf_demand_notice,psl_first_demand_weekdays,psl_first_demand_weekend,\
psl_second_demand_weekdays,psl_second_demand_weekend,psl_final_demand_weekdays,psl_final_demand_weekend,LBF_demand_weekdays,LBF_demand_weekend


class psl_demand_letter_newAdmin(admin.ModelAdmin):
    list_display = ('accountId','task_response','created_at','attachment_response','email_response')
    search_fields = ('accountId','attachment_response')
    list_filter = ('created_at',)


class sme_demand_letterAdmin(admin.ModelAdmin):
    list_display = ('accountId','created_at','attachment_response','email_response')
    search_fields = ('accountId','attachment_response')
    list_filter = ('created_at',)


class lbf_demand_noticeAdmin(admin.ModelAdmin):
    list_display = ('accountId','created_at','attachment_response','email_response')
    search_fields = ('accountId','attachment_response')
    list_filter = ('created_at',)


admin.site.register(lbf_demand_notice, lbf_demand_noticeAdmin)
admin.site.register(psl_demand_letter_new, psl_demand_letter_newAdmin)
admin.site.register(sme_demand_letter, sme_demand_letterAdmin)



# PSL FIRST DEMAND SCHEDULING
class psl_first_demand_weekdaysAdmin(admin.ModelAdmin):
    list_display = ('accountId','fullName','environment','employer_Name')
    search_fields = ('accountId',)
    list_filter = ('created_at',)


class psl_first_demand_weekendAdmin(admin.ModelAdmin):
    list_display = ('accountId','fullName','environment','employer_Name')
    search_fields = ('accountId',)
    list_filter = ('created_at',)


# PSL SECOND DEMAND SCHEDULING
class psl_second_demand_weekdaysAdmin(admin.ModelAdmin):
    list_display = ('accountId','fullName','environment','employer_Name')
    search_fields = ('accountId',)
    list_filter = ('created_at',)


class psl_second_demand_weekendAdmin(admin.ModelAdmin):
    list_display = ('accountId','fullName','environment','employer_Name')
    search_fields = ('accountId',)
    list_filter = ('created_at',)


# PSL FINAL DEMAND SCHEDULING
class psl_final_demand_weekdaysAdmin(admin.ModelAdmin):
    list_display = ('accountId','fullName','environment','employer_Name')
    search_fields = ('accountId',)
    list_filter = ('created_at',)


class psl_final_demand_weekendAdmin(admin.ModelAdmin):
    list_display = ('accountId','fullName','environment','employer_Name')
    search_fields = ('accountId',)
    list_filter = ('created_at',)

# LBF DEMAND
class LBF_demand_weekdaysAdmin(admin.ModelAdmin):
    list_display = ('accountId','fullName','environment')
    search_fields = ('accountId',)
    list_filter = ('created_at',)


class LBF_demand_weekendAdmin(admin.ModelAdmin):
    list_display = ('accountId','fullName','environment')
    search_fields = ('accountId',)
    list_filter = ('created_at',)

admin.site.register(psl_first_demand_weekdays, psl_first_demand_weekdaysAdmin)
admin.site.register(psl_first_demand_weekend, psl_first_demand_weekendAdmin)

admin.site.register(psl_second_demand_weekdays, psl_second_demand_weekdaysAdmin)
admin.site.register(psl_second_demand_weekend, psl_second_demand_weekendAdmin)

admin.site.register(psl_final_demand_weekdays, psl_final_demand_weekdaysAdmin)
admin.site.register(psl_final_demand_weekend, psl_final_demand_weekendAdmin)


admin.site.register(LBF_demand_weekdays, LBF_demand_weekdaysAdmin)
admin.site.register(LBF_demand_weekend, LBF_demand_weekendAdmin)



