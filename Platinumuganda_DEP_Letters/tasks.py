from __future__ import absolute_import, unicode_literals

import configparser

from newugandalms.celery import app
from django.shortcuts import HttpResponse,render,redirect
from django.template.loader import render_to_string
from django.conf import settings
from django.utils import dateformat
from weasyprint import HTML, CSS
from googleapiclient.errors import HttpError
import requests
from datetime import date
from mambu.models import Credential, Environment
from datetime import datetime
import inflect
import time
from pathlib import Path


import base64
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication

# gmail api
import pickle
import os
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from google.auth.transport.requests import Request
from .models import lbf_demand_notice, psl_demand_letter_new,psl_first_demand_weekdays,psl_first_demand_weekend,psl_second_demand_weekdays,\
    psl_second_demand_weekend,psl_final_demand_weekdays,psl_final_demand_weekend,LBF_demand_weekdays,LBF_demand_weekend
def get_custom_field(fieldID, entity):
    for custom_field in entity:
        if custom_field["customFieldID"] == fieldID:
            return custom_field["value"]
    return None

def Create_Service_platke(client_secret_file, api_name, api_version, *scopes):
    # print(client_secret_file, api_name, api_version, scopes, sep='-')
    CLIENT_SECRET_FILE = client_secret_file
    API_SERVICE_NAME = api_name
    API_VERSION = api_version
    SCOPES = [scope for scope in scopes[0]]
    # print(SCOPES)
    cred = None

    pickle_file = Path().absolute()/'Platinumuganda_DEP_Letters/token_{0}_{1}.pickle'.format(API_SERVICE_NAME,API_VERSION)
    # print(pickle_file)

    if os.path.exists(pickle_file):
        with open(pickle_file, 'rb') as token:
            cred = pickle.load(token)

    if not cred or not cred.valid:
        if cred and cred.expired and cred.refresh_token:
            cred.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(CLIENT_SECRET_FILE, SCOPES)
            cred = flow.run_local_server()

        with open(pickle_file, 'wb') as token:
            pickle.dump(cred, token)

    try:
        service = build(API_SERVICE_NAME, API_VERSION, credentials=cred)
        # print(API_SERVICE_NAME, 'service created successfully')
        return service
    except Exception as e:
        print('Unable to connect.')
        print(e)
        return None

# path to gmail_credentials.json
gmail_credentials_platug = Path().absolute()/'Platinumuganda_DEP_Letters/gmail_credentials.json'

def get_client_details(clientID,urls,headers):
    geturl = urls + "clients/{0}".format(clientID)
    get_client_details = requests.get(geturl,headers=headers)
    return get_client_details.json()

def get_repayments(accountid,headers,url):
    loanfindurl = url+"loans/{0}/repayments".format(accountid)
    # print("loanfindurl", loanfindurl)
    getloan = requests.get(loanfindurl, headers=headers)

    findloan = getloan.json()
    installment = 0

    try:

        if len(findloan) <= 2:
            principalDue = findloan[0]['principalDue']
            interestDue = findloan[0]['interestDue']
            feesDue = findloan[0]['feesDue']
            installment = float(principalDue)+float(interestDue)+float(feesDue)
        else:
            principalDue = findloan[2]['principalDue']
            interestDue = findloan[2]['interestDue']
            feesDue = findloan[2]['feesDue']

            installment = float(principalDue)+float(interestDue)+float(feesDue)
    except KeyError:
        installment = 0

    return installment

def get_sales_rep_email(assigneduserKey,headers,urls):
    geturl = urls + "users/{0}".format(assigneduserKey)
    get_sales_rep_details = requests.get(geturl,headers=headers)
    return get_sales_rep_details.json().get("email",'')



# psl first demand notices
@app.task
def save_psl_first_demand(data):
    import datetime
    weekno = datetime.datetime.today().weekday()
    if weekno < 5:
        psl_first_demand_weekdays(
            accountId=data["accountId"],
            environment=data["environment"],
            fullName=data["fullName"],
            employer_Name=data["Employer_Name"]
        ).save()
    else:
        psl_first_demand_weekend(
            accountId=data["accountId"],
            environment=data["environment"],
            fullName=data["fullName"],
            employer_Name=data["Employer_Name"]
        ).save()


# week days psl first demand notice runs everyday at 8 am of week days
@app.task(name='Platinumuganda_DEP_Letters.tasks.psl_first_demand_notice')
def psl_first_demand_notice():
    config = configparser.ConfigParser()
    config.read('secrets.ini')
    DEPConsumer = config.get('Credentials', 'PLATINUMUG_PROD_APIKEY_DEP')
    headers = {"ApiKey": "{}".format(DEPConsumer)}

    for obj in psl_first_demand_weekdays.objects.all():
        accountID= obj.accountId
        environment = obj.environment
        fullName = obj.fullName
        employer_Name = obj.employer_Name

        urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment.endswith(
            'sandbox') else 'https://platinumuganda.mambu.com/api/'

        geturl = urls + "loans/{0}".format(accountID)
        getdetails = requests.get(geturl, headers=headers)
        getloan_response = getdetails.json()

        # GET CURRENT TIME AND DATE
        now = datetime.now()
        today_date_time = dateformat.format(now, 'jS F Y')
        reference_date = now.strftime("%d%m%Y")

        # GET LOAN ACTIVATION DATE
        activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'], "%Y-%m-%dT%H:%M:%S+%f")
        activation_date = dateformat.format(activation, 'jS F Y')

        # GET TOTAL BALANCE
        total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(getloan_response['feesDue']) + float(getloan_response['penaltyDue'])
        if total_due > 4999.99:
            # total due in words
            p = inflect.engine()
            total_due_words = p.number_to_words(round(total_due)).capitalize()

            # client details
            client_details = get_client_details(getloan_response["accountHolderKey"],urls,headers)

            # get sales agent email address
            sales_rep_email = get_sales_rep_email(client_details.get("assignedUserKey",''),headers,urls)


            users_payload = {
                "branchID": client_details.get("assignedBranchKey",""),
                "offset": "0",
                "limit": "1000",
                "fullDetails":"True"
            }
            geturl = urls + "users/"
            getdetails = requests.get(geturl, json=users_payload, headers=headers)

            # CREATE A TASK
            branch_coodinator_email = ""
            task_response = ""
            for obj in getdetails.json():
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a9387f96786a5c30167975ceddc61b8":
                    branch_coodinator_email =obj.get('email','')

            # PSL Append Task on private sector branch
            users_payload = {
                "branchID": "8a858fd05adc4bcf015adc5a51650f3a",
                "offset": "0",
                "limit": "1000",
                "fullDetails":"True"
            }
            geturl = urls + "users/"
            getdetails = requests.get(geturl, json=users_payload, headers=headers)
            # CREATE A TASK
            task_response = ""
            for obj in getdetails.json():
                # Portfolio manager PSL Role
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a9387f96786a5c30167975ceddc61b8":
                    task_payload = {
                        "title": "PSL First Demand Notice_{}.pdf".format(accountID),
                        "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                        "description": "PSL First Demand Notice attached for {account_id}".format(account_id=accountID),
                        "status": "OPEN",
                        "clientID": getloan_response['accountHolderKey'],
                        "username": obj["username"]
                    }

                    taskurl = urls + "tasks"
                    taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                    task_response = taskdetails.json()

            # client_email = "<EMAIL>"
            client_email = client_details.get("emailAddress","")

            # get Branch Coordinator Email address
            context = {
                "full_name": fullName.title(),
                "Client_fname": client_details.get("firstName","").title(),
                "Employer_Name": employer_Name.title(),
                "Client_phone_number":client_details.get("mobilePhone1",""),
                "total_due_words": total_due_words,
                "activation_date": activation_date,
                "total_due": '{:,}'.format(round(total_due)),
                "today_date_time": today_date_time,
                "reference_date":reference_date,
                "accountID": accountID,
                "clientID": client_details.get("id", "")

            }
            rendered_html = render_to_string('DEP/psl_first_demand.html', {'context': context}).encode(
                encoding="UTF-8")
            pdf_file = HTML(string=rendered_html,).write_pdf(
                stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/psl_first_demand.css'),
                             CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                             CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                             ])
            response = HttpResponse(pdf_file, content_type='application/pdf')
            response['Content-Disposition'] = 'attachment; filename=" PSL First Demand Notice_{}.pdf"'.format(accountID)


            # attachDocument
            encoded_string = base64.b64encode(response.content).decode('utf-8')
            attachdoc = {
                "document": {
                    "documentHolderKey": getloan_response['encodedKey'],
                    "documentHolderType": "LOAN_ACCOUNT",
                    "name": "PSL First Demand Notice {}".format(accountID),
                    "type": "PDF"
                },
                "documentContent": encoded_string
            }
            doc_url = urls + "loans/{0}/documents/".format(accountID)
            details = requests.post(doc_url, json=attachdoc, headers=headers)

            # GMAIL API START
            CLIENT_SECRET_FILE = gmail_credentials_platug
            service = Create_Service_platke(CLIENT_SECRET_FILE, 'gmail', 'v1', ['https://mail.google.com/'])

            # email body
            emailMsg = """\
            <html>
              <head></head>
              <body>
                <p>Dear {fistname},<p>
                <p>Thank you for being our valued customer.</p>
                <p>This is an urgent notice on your loan account obligation which you have failed to execute as promised.
                Attached herein is a formal demand notice with respect to the same for your action. Please honor your obligation
                 and have the opportunity of improving and growing your credit worthiness with us.
                 </p>
                <p>For any clarification, please contact your Relationship Manager or our contact center via **********.</p>
                <p>PLATINUM CREDIT</p>
    
              </body>
            </html>
            """.format(fistname=client_details.get('firstName', ''))
            # create email message
            mimeMessage = MIMEMultipart()
            mimeMessage['to'] = client_email
            mimeMessage['cc'] = "{0},{1},{2},{3}".format(branch_coodinator_email,"<EMAIL>","<EMAIL>","<EMAIL>")
            mimeMessage['bcc'] = sales_rep_email
            mimeMessage['subject'] = "PSL First Demand Notice {}.pdf".format(accountID)
            mimeMessage.attach(MIMEText(emailMsg, 'html'))

            part = MIMEApplication(response.content, _subtype='application/pdf')
            part.add_header('Content-Disposition', 'attachment',filename="PSL First Demand Notice {}.pdf".format(accountID))
            mimeMessage.attach(part)
            raw_string = base64.urlsafe_b64encode(mimeMessage.as_bytes()).decode()

            try:
                message = service.users().messages().send(
                    userId='me',
                    body={'raw': raw_string}).execute()
            except HttpError as err:
                message = err
                pass
            # print(message)
            psl_demand_letter_new(
                accountId=accountID,
                product_name=getloan_response['loanName'],
                attachment_response=details.json(),
                task_response=task_response,
                email_response=message).save()

            # CLEAR THE ACCOUNTID
            time.sleep(2)
            psl_first_demand_weekdays.objects.get(accountId=accountID).delete()
        else:
            psl_first_demand_weekdays.objects.get(accountId=accountID).delete()


# psl first demand runs every monday at 8 am
@app.task(name='Platinumuganda_DEP_Letters.tasks.psl_first_demand_notice_weekend')
def psl_first_demand_notice_weekend():
    config = configparser.ConfigParser()
    config.read('secrets.ini')
    DEPConsumer = config.get('Credentials', 'PLATINUMUG_PROD_APIKEY_DEP')
    headers = {"ApiKey": "{}".format(DEPConsumer)}
    for obj in psl_first_demand_weekend.objects.all():
        accountID= obj.accountId
        environment = obj.environment
        fullName = obj.fullName
        employer_Name = obj.employer_Name

        urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment.endswith(
            'sandbox') else 'https://platinumuganda.mambu.com/api/'

        geturl = urls + "loans/{0}".format(accountID)
        getdetails = requests.get(geturl, headers=headers)
        getloan_response = getdetails.json()

        # GET CURRENT TIME AND DATE
        now = datetime.now()
        today_date_time = dateformat.format(now, 'jS F Y')
        reference_date = now.strftime("%d%m%Y")

        # GET LOAN ACTIVATION DATE
        activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'], "%Y-%m-%dT%H:%M:%S+%f")
        activation_date = dateformat.format(activation, 'jS F Y')

        # GET TOTAL BALANCE
        total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(getloan_response['feesDue']) + float(getloan_response['penaltyDue'])

        if total_due > 4999.99:
            # total due in words
            p = inflect.engine()
            total_due_words = p.number_to_words(round(total_due)).capitalize()

            # client details
            client_details = get_client_details(getloan_response["accountHolderKey"],urls,headers)

            # get sales agent email address
            sales_rep_email = get_sales_rep_email(client_details.get("assignedUserKey",''),headers,urls)


            users_payload = {
                "branchID": client_details.get("assignedBranchKey",""),
                "offset": "0",
                "limit": "1000",
                "fullDetails":"True"
            }
            geturl = urls + "users/"
            getdetails = requests.get(geturl, json=users_payload, headers=headers)

            # CREATE A TASK
            branch_coodinator_email = ""
            task_response = ""
            for obj in getdetails.json():
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a9387f96786a5c30167975ceddc61b8":
                    branch_coodinator_email =obj.get('email','')

            # PSL Append Task on private sector branch
            users_payload = {
                "branchID": "8a858fd05adc4bcf015adc5a51650f3a",
                "offset": "0",
                "limit": "1000",
                "fullDetails":"True"
            }
            geturl = urls + "users/"
            getdetails = requests.get(geturl, json=users_payload, headers=headers)
            # CREATE A TASK
            task_response = ""
            for obj in getdetails.json():
                # Portfolio manager PSL Role
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a9387f96786a5c30167975ceddc61b8":
                    task_payload = {
                        "title": "PSL First Demand Notice_{}.pdf".format(accountID),
                        "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                        "description": "PSL First Demand Notice attached for {account_id}".format(account_id=accountID),
                        "status": "OPEN",
                        "clientID": getloan_response['accountHolderKey'],
                        "username": obj["username"]
                    }

                    taskurl = urls + "tasks"
                    taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                    task_response = taskdetails.json()

            # client_email = "<EMAIL>"
            client_email = client_details.get("emailAddress","")

            # get Branch Coordinator Email address
            context = {
                "full_name": fullName.title(),
                "Client_fname": client_details.get("firstName","").title(),
                "Employer_Name": employer_Name.title(),
                "Client_phone_number":client_details.get("mobilePhone1",""),
                "total_due_words": total_due_words,
                "activation_date": activation_date,
                "total_due": '{:,}'.format(round(total_due)),
                "today_date_time": today_date_time,
                "reference_date":reference_date,
                "accountID": accountID,
                "clientID": client_details.get("id", "")

            }
            rendered_html = render_to_string('DEP/psl_first_demand.html', {'context': context}).encode(
                encoding="UTF-8")
            pdf_file = HTML(string=rendered_html,).write_pdf(
                stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/psl_first_demand.css'),
                             CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                             CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                             ])
            response = HttpResponse(pdf_file, content_type='application/pdf')
            response['Content-Disposition'] = 'attachment; filename=" PSL First Demand Notice_{}.pdf"'.format(accountID)


            # attachDocument
            encoded_string = base64.b64encode(response.content).decode('utf-8')
            attachdoc = {
                "document": {
                    "documentHolderKey": getloan_response['encodedKey'],
                    "documentHolderType": "LOAN_ACCOUNT",
                    "name": "PSL First Demand Notice {}".format(accountID),
                    "type": "PDF"
                },
                "documentContent": encoded_string
            }
            doc_url = urls + "loans/{0}/documents/".format(accountID)
            details = requests.post(doc_url, json=attachdoc, headers=headers)

            # GMAIL API START
            CLIENT_SECRET_FILE = gmail_credentials_platug
            service = Create_Service_platke(CLIENT_SECRET_FILE, 'gmail', 'v1', ['https://mail.google.com/'])

            # email body
            emailMsg = """\
            <html>
              <head></head>
              <body>
                <p>Dear {fistname},<p>
                <p>Thank you for being our valued customer.</p>
                <p>This is an urgent notice on your loan account obligation which you have failed to execute as promised.
                Attached herein is a formal demand notice with respect to the same for your action. Please honor your obligation
                 and have the opportunity of improving and growing your credit worthiness with us.
                 </p>
                <p>For any clarification, please contact your Relationship Manager or our contact center via **********.</p>
                <p>PLATINUM CREDIT</p>
    
              </body>
            </html>
            """.format(fistname=client_details.get('firstName', ''))
            # create email message
            mimeMessage = MIMEMultipart()
            mimeMessage['to'] = client_email
            mimeMessage['cc'] = "{0},{1},{2},{3}".format(branch_coodinator_email,"<EMAIL>","<EMAIL>","<EMAIL>")
            mimeMessage['bcc'] = sales_rep_email
            mimeMessage['subject'] = "PSL First Demand Notice {}.pdf".format(accountID)
            mimeMessage.attach(MIMEText(emailMsg, 'html'))

            part = MIMEApplication(response.content, _subtype='application/pdf')
            part.add_header('Content-Disposition', 'attachment',filename="PSL First Demand Notice {}.pdf".format(accountID))
            mimeMessage.attach(part)
            raw_string = base64.urlsafe_b64encode(mimeMessage.as_bytes()).decode()

            try:
                message = service.users().messages().send(
                    userId='me',
                    body={'raw': raw_string}).execute()
            except HttpError as err:
                message = err
                pass
            # print(message)
            psl_demand_letter_new(
                accountId=accountID,
                product_name=getloan_response['loanName'],
                attachment_response=details.json(),
                task_response=task_response,
                email_response=message).save()

            # CLEAR THE ACCOUNTID
            time.sleep(2)
            psl_first_demand_weekend.objects.get(accountId=accountID).delete()
        else:
            psl_first_demand_weekend.objects.get(accountId=accountID).delete()


# psl second demand notices
@app.task
def save_psl_second_demand(data):
    import datetime
    weekno = datetime.datetime.today().weekday()
    if weekno < 5:
        psl_second_demand_weekdays(
            accountId=data["accountId"],
            environment=data["environment"],
            fullName=data["fullName"],
            employer_Name=data["Employer_Name"]
        ).save()
    else:
        psl_second_demand_weekend(
            accountId=data["accountId"],
            environment=data["environment"],
            fullName=data["fullName"],
            employer_Name=data["Employer_Name"]
        ).save()

# week days psl second demand notice runs everyday at 8 am of week days
@app.task(name='Platinumuganda_DEP_Letters.tasks.psl_second_demand_notice')
def psl_second_demand_notice():
    config = configparser.ConfigParser()
    config.read('secrets.ini')
    DEPConsumer = config.get('Credentials', 'PLATINUMUG_PROD_APIKEY_DEP')
    headers = {"ApiKey": "{}".format(DEPConsumer)}
    for obj in psl_second_demand_weekdays.objects.all():
        accountID= obj.accountId
        environment = obj.environment
        fullName = obj.fullName
        employer_Name = obj.employer_Name

        urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment.endswith(
            'sandbox') else 'https://platinumuganda.mambu.com/api/'

        geturl = urls + "loans/{0}".format(accountID)
        getdetails = requests.get(geturl, headers=headers)
        getloan_response = getdetails.json()

        # # GET CURRENT TIME AND DATE
        now = datetime.now()
        today_date_time = dateformat.format(now, 'jS F Y')
        reference_date = now.strftime("%d%m%Y")

        # GET LOAN ACTIVATION DATE
        activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'],"%Y-%m-%dT%H:%M:%S+%f")
        activation_date = dateformat.format(activation, 'jS F Y')

        # GET TOTAL BALANCE
        total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(getloan_response['feesDue']) + float(getloan_response['penaltyDue'])
        if total_due > 4999.99:

            # total due in words
            p = inflect.engine()
            total_due_words = p.number_to_words(round(total_due)).capitalize()

            # client details
            client_details = get_client_details(getloan_response["accountHolderKey"], urls, headers)

            # get sales agent email address
            sales_rep_email = get_sales_rep_email(client_details.get("assignedUserKey", ''), headers, urls)

            client_email = client_details.get("emailAddress", "")
            # client_email = "<EMAIL>"

            context = {
                "full_name": fullName.title(),
                "Client_fname": client_details.get("firstName", "").title(),
                "Employer_Name": employer_Name.title(),
                "Client_phone_number": client_details.get("mobilePhone1", ""),
                "total_due_words": total_due_words,
                "activation_date": activation_date,
                "total_due": '{:,}'.format(round(total_due)),
                "today_date_time": today_date_time,
                "reference_date": reference_date,
                "accountID": accountID,
                "clientID": client_details.get("id", "")

            }

            rendered_html = render_to_string('DEP/psl_second_demand.html', {'context': context}).encode(
                encoding="UTF-8")
            pdf_file = HTML(string=rendered_html, ).write_pdf(
                stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/psl_first_demand.css'),
                             CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                             CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                             ])
            response = HttpResponse(pdf_file, content_type='application/pdf')
            response['Content-Disposition'] = 'attachment; filename=" PSL Second Demand Notice_{}.pdf"'.format(accountID)

            users_payload = {
                "branchID": client_details.get("assignedBranchKey", ""),
                "offset": "0",
                "limit": "1000",
                "fullDetails": "True"
            }
            geturl = urls + "users/"
            getdetails = requests.get(geturl, json=users_payload, headers=headers)

            # CREATE A TASK
            branch_coodinator_email = ""
            task_response = ""
            for obj in getdetails.json():
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a9387f96786a5c30167975ceddc61b8":
                    branch_coodinator_email = obj.get('email', '')

            # PSL Append Task on private sector branch
            users_payload = {
                "branchID": "8a858fd05adc4bcf015adc5a51650f3a",
                "offset": "0",
                "limit": "1000",
                "fullDetails": "True"
            }
            geturl = urls + "users/"
            getdetails = requests.get(geturl, json=users_payload, headers=headers)
            # CREATE A TASK
            task_response = ""
            for obj in getdetails.json():
                # Portfolio manager PSL Role
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a9387f96786a5c30167975ceddc61b8":
                    task_payload = {
                        "title": "PSL Second Demand Notice_{}.pdf".format(accountID),
                        "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                        "description": "PSL Second Demand Notice attached for {account_id}".format(account_id=accountID),
                        "status": "OPEN",
                        "clientID": getloan_response['accountHolderKey'],
                        "username": obj["username"]
                    }

                    taskurl = urls + "tasks"
                    taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                    task_response = taskdetails.json()
            encoded_string = base64.b64encode(response.content).decode('utf-8')
            attachdoc = {
                "document": {
                    "documentHolderKey": getloan_response['encodedKey'],
                    "documentHolderType": "LOAN_ACCOUNT",
                    "name": "PSL Second Demand Notice {}".format(accountID),
                    "type": "PDF"
                },
                "documentContent": encoded_string
            }
            doc_url = urls + "loans/{0}/documents/".format(accountID)
            details = requests.post(doc_url, json=attachdoc, headers=headers)

            # GMAIL API START
            CLIENT_SECRET_FILE = gmail_credentials_platug
            service = Create_Service_platke(CLIENT_SECRET_FILE, 'gmail', 'v1', ['https://mail.google.com/'])
            # # email body
            emailMsg = """\
            <html>
              <head></head>
              <body>
                <p>Dear {fistname},<p>
                <p>Thank you for being our valued customer.</p>
                <p>This is an urgent notice on your loan account obligation which you have failed to execute as promised.
                Attached herein is a formal demand notice with respect to the same for your action. Please honor your obligation
                 and have the opportunity of improving and growing your credit worthiness with us.
                 </p>
                <p>For any clarification, please contact your Relationship Manager or our contact center via **********.</p>
                <p>PLATINUM CREDIT</p>
              </body>
            </html>
            """.format(fistname=client_details.get('firstName', ''))
            # create email message
            mimeMessage = MIMEMultipart()
            mimeMessage['to'] = client_email
            mimeMessage['cc'] = "{0},{1},{2},{3}".format(branch_coodinator_email,"<EMAIL>","<EMAIL>","<EMAIL>")
            mimeMessage['bcc'] = sales_rep_email

            mimeMessage['subject'] = "PSL Second Demand Notice {}.pdf".format(accountID)
            mimeMessage.attach(MIMEText(emailMsg, 'html'))

            part = MIMEApplication(response.content, _subtype='application/pdf')
            part.add_header('Content-Disposition', 'attachment',
                            filename="PSL Second Demand Notice {}.pdf".format(accountID))

            mimeMessage.attach(part)
            raw_string = base64.urlsafe_b64encode(mimeMessage.as_bytes()).decode()
            try:
                message = service.users().messages().send(
                    userId='me',
                    body={'raw': raw_string}).execute()
            except HttpError as err:
                message = err
                pass

            psl_demand_letter_new(
                accountId=accountID,
                product_name=getloan_response['loanName'],
                attachment_response=details.json(),
                task_response=task_response,
                email_response=message).save()

            # CLEAR THE ACCOUNTID
            time.sleep(2)
            psl_second_demand_weekdays.objects.get(accountId=accountID).delete()
        else:
            psl_second_demand_weekdays.objects.get(accountId=accountID).delete()


# psl second demand runs every monday at 8 am
@app.task(name='Platinumuganda_DEP_Letters.tasks.psl_second_demand_notice_weekend')
def psl_second_demand_notice_weekend():
    config = configparser.ConfigParser()
    config.read('secrets.ini')
    DEPConsumer = config.get('Credentials', 'PLATINUMUG_PROD_APIKEY_DEP')
    headers = {"ApiKey": "{}".format(DEPConsumer)}
    for obj in psl_second_demand_weekend.objects.all():
        accountID = obj.accountId
        environment = obj.environment
        fullName = obj.fullName
        employer_Name = obj.employer_Name

        urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment.endswith(
            'sandbox') else 'https://platinumuganda.mambu.com/api/'


        geturl = urls + "loans/{0}".format(accountID)
        getdetails = requests.get(geturl, headers=headers)
        getloan_response = getdetails.json()

        # # GET CURRENT TIME AND DATE
        now = datetime.now()
        today_date_time = dateformat.format(now, 'jS F Y')
        reference_date = now.strftime("%d%m%Y")

        # GET LOAN ACTIVATION DATE
        activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'],
                                       "%Y-%m-%dT%H:%M:%S+%f")
        activation_date = dateformat.format(activation, 'jS F Y')

        # GET TOTAL BALANCE
        total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(
            getloan_response['feesDue']) + float(getloan_response['penaltyDue'])

        if total_due >4999.99:

            # total due in words
            p = inflect.engine()
            total_due_words = p.number_to_words(round(total_due)).capitalize()

            # client details
            client_details = get_client_details(getloan_response["accountHolderKey"], urls, headers)

            # get sales agent email address
            sales_rep_email = get_sales_rep_email(client_details.get("assignedUserKey", ''), headers, urls)

            client_email = client_details.get("emailAddress", "")
            # client_email = "<EMAIL>"

            context = {
                "full_name": fullName.title(),
                "Client_fname": client_details.get("firstName", "").title(),
                "Employer_Name": employer_Name.title(),
                "Client_phone_number": client_details.get("mobilePhone1", ""),
                "total_due_words": total_due_words,
                "activation_date": activation_date,
                "total_due": '{:,}'.format(round(total_due)),
                "today_date_time": today_date_time,
                "reference_date": reference_date,
                "accountID": accountID,
                "clientID": client_details.get("id", "")

            }

            rendered_html = render_to_string('DEP/psl_second_demand.html', {'context': context}).encode(
                encoding="UTF-8")
            pdf_file = HTML(string=rendered_html, ).write_pdf(
                stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/psl_first_demand.css'),
                             CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                             CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                             ])
            response = HttpResponse(pdf_file, content_type='application/pdf')
            response['Content-Disposition'] = 'attachment; filename=" PSL Second Demand Notice_{}.pdf"'.format(accountID)

            users_payload = {
                "branchID": client_details.get("assignedBranchKey", ""),
                "offset": "0",
                "limit": "1000",
                "fullDetails": "True"
            }
            geturl = urls + "users/"
            getdetails = requests.get(geturl, json=users_payload, headers=headers)

            # CREATE A TASK
            branch_coodinator_email = ""
            task_response = ""
            for obj in getdetails.json():
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a9387f96786a5c30167975ceddc61b8":
                    branch_coodinator_email = obj.get('email', '')

            # PSL Append Task on private sector branch
            users_payload = {
                "branchID": "8a858fd05adc4bcf015adc5a51650f3a",
                "offset": "0",
                "limit": "1000",
                "fullDetails": "True"
            }
            geturl = urls + "users/"
            getdetails = requests.get(geturl, json=users_payload, headers=headers)
            # CREATE A TASK
            task_response = ""
            for obj in getdetails.json():
                # Portfolio manager PSL Role
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a9387f96786a5c30167975ceddc61b8":
                    task_payload = {
                        "title": "PSL Second Demand Notice_{}.pdf".format(accountID),
                        "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                        "description": "PSL Second Demand Notice attached for {account_id}".format(account_id=accountID),
                        "status": "OPEN",
                        "clientID": getloan_response['accountHolderKey'],
                        "username": obj["username"]
                    }

                    taskurl = urls + "tasks"
                    taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                    task_response = taskdetails.json()
            encoded_string = base64.b64encode(response.content).decode('utf-8')
            attachdoc = {
                "document": {
                    "documentHolderKey": getloan_response['encodedKey'],
                    "documentHolderType": "LOAN_ACCOUNT",
                    "name": "PSL Second Demand Notice {}".format(accountID),
                    "type": "PDF"
                },
                "documentContent": encoded_string
            }
            doc_url = urls + "loans/{0}/documents/".format(accountID)
            details = requests.post(doc_url, json=attachdoc, headers=headers)

            # GMAIL API START
            CLIENT_SECRET_FILE = gmail_credentials_platug
            service = Create_Service_platke(CLIENT_SECRET_FILE, 'gmail', 'v1', ['https://mail.google.com/'])
            # # email body
            emailMsg = """\
            <html>
              <head></head>
              <body>
                <p>Dear {fistname},<p>
                <p>Thank you for being our valued customer.</p>
                <p>This is an urgent notice on your loan account obligation which you have failed to execute as promised.
                Attached herein is a formal demand notice with respect to the same for your action. Please honor your obligation
                 and have the opportunity of improving and growing your credit worthiness with us.
                 </p>
                <p>For any clarification, please contact your Relationship Manager or our contact center via **********.</p>
                <p>PLATINUM CREDIT</p>
              </body>
            </html>
            """.format(fistname=client_details.get('firstName', ''))
            # create email message
            mimeMessage = MIMEMultipart()
            mimeMessage['to'] = client_email
            mimeMessage['cc'] = "{0},{1},{2},{3}".format(branch_coodinator_email,"<EMAIL>","<EMAIL>","<EMAIL>")
            mimeMessage['bcc'] = sales_rep_email

            mimeMessage['subject'] = "PSL Second Demand Notice {}.pdf".format(accountID)
            mimeMessage.attach(MIMEText(emailMsg, 'html'))

            part = MIMEApplication(response.content, _subtype='application/pdf')
            part.add_header('Content-Disposition', 'attachment',
                            filename="PSL Second Demand Notice {}.pdf".format(accountID))

            mimeMessage.attach(part)
            raw_string = base64.urlsafe_b64encode(mimeMessage.as_bytes()).decode()
            try:
                message = service.users().messages().send(
                    userId='me',
                    body={'raw': raw_string}).execute()
            except HttpError as err:
                message = err
                pass

            psl_demand_letter_new(
                accountId=accountID,
                product_name=getloan_response['loanName'],
                attachment_response=details.json(),
                task_response=task_response,
                email_response=message).save()

            # CLEAR THE ACCOUNTID
            time.sleep(2)
            psl_second_demand_weekend.objects.get(accountId=accountID).delete()
        else:
            psl_second_demand_weekend.objects.get(accountId=accountID).delete()



# psl final demand notices
@app.task
def save_psl_final_demand(data):
    import datetime
    weekno = datetime.datetime.today().weekday()
    if weekno < 5:
        psl_final_demand_weekdays(
            accountId=data["accountId"],
            environment=data["environment"],
            fullName=data["fullName"],
            employer_Name=data["Employer_Name"]
        ).save()
    else:
        psl_final_demand_weekend(
            accountId=data["accountId"],
            environment=data["environment"],
            fullName=data["fullName"],
            employer_Name=data["Employer_Name"]
        ).save()

# week days psl final demand notice runs everyday at 8 am of week days
@app.task(name='Platinumuganda_DEP_Letters.tasks.psl_final_demand_notice')
def psl_final_demand_notice():
    config = configparser.ConfigParser()
    config.read('secrets.ini')
    DEPConsumer = config.get('Credentials', 'PLATINUMUG_PROD_APIKEY_DEP')
    headers = {"ApiKey": "{}".format(DEPConsumer)}
    for obj in psl_final_demand_weekdays.objects.all():
        accountID= obj.accountId
        environment = obj.environment
        fullName = obj.fullName
        employer_Name = obj.employer_Name

        urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment.endswith(
            'sandbox') else 'https://platinumuganda.mambu.com/api/'

        geturl = urls + "loans/{0}".format(accountID)
        getdetails = requests.get(geturl, headers=headers)
        getloan_response = getdetails.json()

        # # GET CURRENT TIME AND DATE
        now = datetime.now()
        today_date_time = dateformat.format(now, 'jS F Y')
        reference_date = now.strftime("%d%m%Y")

        # GET LOAN ACTIVATION DATE
        activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'],"%Y-%m-%dT%H:%M:%S+%f")
        activation_date = dateformat.format(activation, 'jS F Y')

        # GET TOTAL BALANCE
        total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(
            getloan_response['feesDue']) + float(getloan_response['penaltyDue'])

        if total_due > 4999.99:

            # total due in words
            p = inflect.engine()
            total_due_words = p.number_to_words(round(total_due)).capitalize()

            # client details
            client_details = get_client_details(getloan_response["accountHolderKey"], urls, headers)

            client_email = client_details.get("emailAddress", "")
            sales_rep_email = get_sales_rep_email(client_details.get("assignedUserKey", ''), headers, urls)

            context = {
                "full_name": fullName.title(),
                "Client_fname": client_details.get("firstName", "").title(),
                "Employer_Name": employer_Name.title(),
                "Client_phone_number": client_details.get("mobilePhone1", ""),
                "total_due_words": total_due_words,
                "activation_date": activation_date,
                "total_due": '{:,}'.format(round(total_due)),
                "today_date_time": today_date_time,
                "reference_date": reference_date,
                "accountID": accountID,
                "clientID": client_details.get("id", "")

            }
            rendered_html = render_to_string('DEP/psl_final_demand.html', {'context': context}).encode(
                encoding="UTF-8")
            pdf_file = HTML(string=rendered_html, ).write_pdf(
                stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/psl_first_demand.css'),
                             CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                             CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                             ])
            response = HttpResponse(pdf_file, content_type='application/pdf')
            response['Content-Disposition'] = 'attachment; filename=" Loan Recall {}.pdf"'.format(accountID)

            users_payload = {
                "branchID": client_details.get("assignedBranchKey", ""),
                "offset": "0",
                "limit": "1000",
                "fullDetails": "True"
            }
            geturl = urls + "users/"
            getdetails = requests.get(geturl, json=users_payload, headers=headers)

            branch_coodinator_email = ""
            task_response = ""
            for obj in getdetails.json():
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a9387f96786a5c30167975ceddc61b8":
                    branch_coodinator_email = obj.get('email', '')

            # PSL Append Task on private sector branch
            users_payload = {
                "branchID": "8a858fd05adc4bcf015adc5a51650f3a",
                "offset": "0",
                "limit": "1000",
                "fullDetails": "True"
            }
            geturl = urls + "users/"
            getdetails = requests.get(geturl, json=users_payload, headers=headers)
            # CREATE A TASK
            task_response = ""
            for obj in getdetails.json():
                # Portfolio manager PSL Role
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a9387f96786a5c30167975ceddc61b8":
                    task_payload = {
                        "title": "Loan Recall {}.pdf".format(accountID),
                        "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                        "description": "Loan Recall attached for {account_id}".format(account_id=accountID),
                        "status": "OPEN",
                        "clientID": getloan_response['accountHolderKey'],
                        "username": obj["username"]
                    }

                    taskurl = urls + "tasks"
                    taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                    task_response = taskdetails.json()

            # attachDocument
            encoded_string = base64.b64encode(response.content).decode('utf-8')
            attachdoc = {
                "document": {
                    "documentHolderKey": getloan_response['encodedKey'],
                    "documentHolderType": "LOAN_ACCOUNT",
                    "name": "Loan Recall {}".format(accountID),
                    "type": "PDF"
                },
                "documentContent": encoded_string
            }
            doc_url = urls + "loans/{0}/documents/".format(accountID)
            details = requests.post(doc_url, json=attachdoc, headers=headers)
            # print("DETAILS",details.json())

            # GMAIL API START
            CLIENT_SECRET_FILE = gmail_credentials_platug
            service = Create_Service_platke(CLIENT_SECRET_FILE, 'gmail', 'v1', ['https://mail.google.com/'])
            # # email body
            emailMsg = """\
            <html>
              <head></head>
              <body>
                <p>Dear {fistname},<p>
                <p>Thank you for being our valued customer.</p>
                <p>This is an urgent notice on your loan account obligation which you have failed to execute as promised.
                Attached herein is a formal demand notice with respect to the same for your action. Please honor your obligation
                 and have the opportunity of improving and growing your credit worthiness with us.
                 </p>
                <p>For any clarification, please contact your Relationship Manager or our contact center via **********.</p>
                <p>PLATINUM CREDIT</p>
              </body>
            </html>
            """.format(fistname=client_details.get('firstName', ''))
            # create email message
            mimeMessage = MIMEMultipart()
            mimeMessage['to'] = client_email
            # mimeMessage['to'] = "<EMAIL>"

            mimeMessage['cc'] = "{0},{1},{2},{3}".format(branch_coodinator_email,"<EMAIL>","<EMAIL>","<EMAIL>")
            mimeMessage['bcc'] = sales_rep_email
            mimeMessage['subject'] = "Loan Recall {}.pdf".format(accountID)
            mimeMessage.attach(MIMEText(emailMsg, 'html'))

            part = MIMEApplication(response.content, _subtype='application/pdf')
            part.add_header('Content-Disposition', 'attachment', filename="Loan Recall {}.pdf".format(accountID))

            mimeMessage.attach(part)
            raw_string = base64.urlsafe_b64encode(mimeMessage.as_bytes()).decode()
            try:
                message = service.users().messages().send(
                    userId='me',
                    body={'raw': raw_string}).execute()
            except HttpError as err:
                message = err
                pass

            psl_demand_letter_new(
                accountId=accountID,
                product_name=getloan_response['loanName'],
                attachment_response=details.json(),
                task_response=task_response,
                email_response=message).save()

            # CLEAR THE ACCOUNTID
            time.sleep(2)
            psl_final_demand_weekdays.objects.get(accountId=accountID).delete()
        else:
            psl_final_demand_weekdays.objects.get(accountId=accountID).delete()


# psl final demand runs every monday at 8 am
@app.task(name='Platinumuganda_DEP_Letters.tasks.psl_final_demand_notice_weekends')
def psl_final_demand_notice_weekends():
    config = configparser.ConfigParser()
    config.read('secrets.ini')
    DEPConsumer = config.get('Credentials', 'PLATINUMUG_PROD_APIKEY_DEP')
    headers = {"ApiKey": "{}".format(DEPConsumer)}
    for obj in psl_final_demand_weekend.objects.all():
        accountID = obj.accountId
        environment = obj.environment
        fullName = obj.fullName
        employer_Name = obj.employer_Name

        urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment.endswith(
            'sandbox') else 'https://platinumuganda.mambu.com/api/'

        geturl = urls + "loans/{0}".format(accountID)
        getdetails = requests.get(geturl, headers=headers)
        getloan_response = getdetails.json()

        # # GET CURRENT TIME AND DATE
        now = datetime.now()
        today_date_time = dateformat.format(now, 'jS F Y')
        reference_date = now.strftime("%d%m%Y")

        # GET LOAN ACTIVATION DATE
        activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'],
                                       "%Y-%m-%dT%H:%M:%S+%f")
        activation_date = dateformat.format(activation, 'jS F Y')

        # GET TOTAL BALANCE
        total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(
            getloan_response['feesDue']) + float(getloan_response['penaltyDue'])
        if total_due > 4999.99:

            # total due in words
            p = inflect.engine()
            total_due_words = p.number_to_words(round(total_due)).capitalize()

            # client details
            client_details = get_client_details(getloan_response["accountHolderKey"], urls, headers)

            client_email = client_details.get("emailAddress", "")
            sales_rep_email = get_sales_rep_email(client_details.get("assignedUserKey", ''), headers, urls)

            context = {
                "full_name": fullName.title(),
                "Client_fname": client_details.get("firstName", "").title(),
                "Employer_Name": employer_Name.title(),
                "Client_phone_number": client_details.get("mobilePhone1", ""),
                "total_due_words": total_due_words,
                "activation_date": activation_date,
                "total_due": '{:,}'.format(round(total_due)),
                "today_date_time": today_date_time,
                "reference_date": reference_date,
                "accountID": accountID,
                "clientID": client_details.get("id", "")

            }
            rendered_html = render_to_string('DEP/psl_final_demand.html', {'context': context}).encode(
                encoding="UTF-8")
            pdf_file = HTML(string=rendered_html, ).write_pdf(
                stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/psl_first_demand.css'),
                             CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                             CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                             ])
            response = HttpResponse(pdf_file, content_type='application/pdf')
            response['Content-Disposition'] = 'attachment; filename=" Loan Recall {}.pdf"'.format(accountID)

            users_payload = {
                "branchID": client_details.get("assignedBranchKey", ""),
                "offset": "0",
                "limit": "1000",
                "fullDetails": "True"
            }
            geturl = urls + "users/"
            getdetails = requests.get(geturl, json=users_payload, headers=headers)

            branch_coodinator_email = ""
            task_response = ""
            for obj in getdetails.json():
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a9387f96786a5c30167975ceddc61b8":
                    branch_coodinator_email = obj.get('email', '')

            # PSL Append Task on private sector branch
            users_payload = {
                "branchID": "8a858fd05adc4bcf015adc5a51650f3a",
                "offset": "0",
                "limit": "1000",
                "fullDetails": "True"
            }
            geturl = urls + "users/"
            getdetails = requests.get(geturl, json=users_payload, headers=headers)
            # CREATE A TASK
            task_response = ""
            for obj in getdetails.json():
                # Portfolio manager PSL Role
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a9387f96786a5c30167975ceddc61b8":
                    task_payload = {
                        "title": "Loan Recall {}.pdf".format(accountID),
                        "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                        "description": "Loan Recall attached for {account_id}".format(account_id=accountID),
                        "status": "OPEN",
                        "clientID": getloan_response['accountHolderKey'],
                        "username": obj["username"]
                    }

                    taskurl = urls + "tasks"
                    taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                    task_response = taskdetails.json()

            # attachDocument
            encoded_string = base64.b64encode(response.content).decode('utf-8')
            attachdoc = {
                "document": {
                    "documentHolderKey": getloan_response['encodedKey'],
                    "documentHolderType": "LOAN_ACCOUNT",
                    "name": "Loan Recall {}".format(accountID),
                    "type": "PDF"
                },
                "documentContent": encoded_string
            }
            doc_url = urls + "loans/{0}/documents/".format(accountID)
            details = requests.post(doc_url, json=attachdoc, headers=headers)
            # print("DETAILS",details.json())

            # GMAIL API START
            CLIENT_SECRET_FILE = gmail_credentials_platug
            service = Create_Service_platke(CLIENT_SECRET_FILE, 'gmail', 'v1', ['https://mail.google.com/'])
            # # email body
            emailMsg = """\
            <html>
              <head></head>
              <body>
                <p>Dear {fistname},<p>
                <p>Thank you for being our valued customer.</p>
                <p>This is an urgent notice on your loan account obligation which you have failed to execute as promised.
                Attached herein is a formal demand notice with respect to the same for your action. Please honor your obligation
                 and have the opportunity of improving and growing your credit worthiness with us.
                 </p>
                <p>For any clarification, please contact your Relationship Manager or our contact center via **********.</p>
                <p>PLATINUM CREDIT</p>
              </body>
            </html>
            """.format(fistname=client_details.get('firstName', ''))
            # create email message
            mimeMessage = MIMEMultipart()
            mimeMessage['to'] = client_email
            # mimeMessage['to'] = "<EMAIL>"

            mimeMessage['cc'] = "{0},{1},{2},{3}".format(branch_coodinator_email,"<EMAIL>","<EMAIL>","<EMAIL>")
            mimeMessage['bcc'] = sales_rep_email
            mimeMessage['subject'] = "Loan Recall {}.pdf".format(accountID)
            mimeMessage.attach(MIMEText(emailMsg, 'html'))

            part = MIMEApplication(response.content, _subtype='application/pdf')
            part.add_header('Content-Disposition', 'attachment', filename="Loan Recall {}.pdf".format(accountID))

            mimeMessage.attach(part)
            raw_string = base64.urlsafe_b64encode(mimeMessage.as_bytes()).decode()
            try:
                message = service.users().messages().send(
                    userId='me',
                    body={'raw': raw_string}).execute()
            except HttpError as err:
                message = err
                pass

            psl_demand_letter_new(
                accountId=accountID,
                product_name=getloan_response['loanName'],
                attachment_response=details.json(),
                task_response=task_response,
                email_response=message).save()

            # CLEAR THE ACCOUNTID
            time.sleep(2)
            psl_final_demand_weekend.objects.get(accountId=accountID).delete()
        else:
            psl_final_demand_weekend.objects.get(accountId=accountID).delete()



# LBF save demand notices
@app.task
def save_lbf_demand_letter(data):
    import datetime
    weekno = datetime.datetime.today().weekday()
    if weekno < 5:
        LBF_demand_weekdays(
            accountId=data["accountId"],
            environment=data["environment"],
            fullName=data["fullName"]
        ).save()
    else:
        LBF_demand_weekend(
            accountId=data["accountId"],
            environment=data["environment"],
            fullName=data["fullName"]
        ).save()

# # week days LBF demand notice runs everyday at 8 am of week days
def get_IPF_bal(client_id,url):
    # live
    headers = {"ApiKey": "0DyXjZ2cUyKjLRhImkvQSJ5D2B3ZjyCu"}
    payload = {'offset': 0, 'limit': 100}
    geturl = url + "clients/{0}/loans".format(client_id)
    get_details = requests.get(geturl, json=payload, headers=headers)

    IPF_total_due = 0.0
    for obj in get_details.json():
        if obj['loanName'].startswith("Insurance Premium") and obj['accountState'] in ["ACTIVE","ACTIVE_IN_ARREARS"]:
            # IPF Current month Interest
            IPF_total_due += float(obj['principalDue']) + float(obj['interestDue']) + float(obj['feesDue']) + float(obj['penaltyDue'])

        else:
            pass

    return IPF_total_due
@app.task(name='Platinumuganda_DEP_Letters.tasks.LBF_demand_notice')
def LBF_demand_notice():
    for obj in LBF_demand_weekdays.objects.all():
        accountID= obj.accountId
        environment = obj.environment
        fullName = obj.fullName

        urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment.endswith(
            'sandbox') else 'https://platinumuganda.mambu.com/api/'

        # sandbox
        # headers = {"ApiKey": "********************************"}

        # live
        headers = {"ApiKey": "0DyXjZ2cUyKjLRhImkvQSJ5D2B3ZjyCu"}

        geturl = urls + "loans/{0}".format(accountID)
        getdetails = requests.get(geturl, headers=headers)
        getloan_response = getdetails.json()

        clientID = getloan_response['accountHolderKey']


        # check if the product is lbf bond
        sentence = "." if getloan_response['loanName'].startswith(
            'Car') else " Please note that your account continues to incur a daily charge of 0.33% of the arrears until the same are cleared, and as such the amount in arrears is bound to change."

        # GET LOAN ACTIVATION DATE
        activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'],
                                       "%Y-%m-%dT%H:%M:%S+%f")
        activation_date = dateformat.format(activation, 'jS F Y')

        now = datetime.now()
        today_date_time = dateformat.format(now, 'jS F Y')
        reference_date = now.strftime("%d%m%Y")

        ipfBalance = get_IPF_bal(clientID,urls)

        # GET TOTAL BALANCE
        total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(
            getloan_response['feesDue']) + float(getloan_response['penaltyDue']) +float(ipfBalance)
        if total_due > 4999.99:
            # total due in
            p = inflect.engine()
            total_due_words = p.number_to_words(int(total_due)).capitalize()

            # Vehicle number plate
            get_details_veh_details = urls + "loans/{0}/custominformation/{1}".format(accountID, 'reg_no')
            get_veh_details = requests.get(get_details_veh_details, headers=headers)

            # print('VEHICLE REG',get_loan_details.json())
            try:
                vehicle_reg_number = get_custom_field("reg_no", get_veh_details.json())
            except TypeError:
                vehicle_reg_number = ''

            # client details
            client_details = get_client_details(getloan_response["accountHolderKey"], urls, headers)
            # client_email = "<EMAIL>"
            client_email = client_details.get("emailAddress", "")

            context = {
                "full_name": fullName.title(),
                "Client_phone_number": client_details.get("mobilePhone1", ""),
                "today_date_time": today_date_time,
                "Client_fname": client_details.get("firstName", "").title(),
                "total_due": '{:,}'.format(round(total_due)),
                "total_due_words": total_due_words,
                "activation_date": activation_date,
                "vehicle_reg_number": vehicle_reg_number if vehicle_reg_number is not None else "",
                "sentence": sentence,
                "reference_date": reference_date,
                "accountID": accountID,
                "clientID": client_details["id"]
            }
            rendered_html = render_to_string('DEP/lbf/lbf_demand.html', {'context': context}).encode(
                encoding="UTF-8")
            pdf_file = HTML(string=rendered_html, ).write_pdf(
                stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/sme/first_demand_notice.css'),
                             CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                             CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                             ])
            response = HttpResponse(pdf_file, content_type='application/pdf')
            response['Content-Disposition'] = 'attachment; filename="LBF Demand Notice {}.pdf"'.format(accountID)

            # search branch coordinator by Branch
            users_payload = {
                "branchID": client_details.get("assignedBranchKey", ""),
                "offset": "0",
                "limit": "1000",
                "fullDetails": "True"
            }
            geturl = urls + "users/"
            getdetails = requests.get(geturl, json=users_payload, headers=headers)
            # CREATE A TASK
            task_response = ""
            branchManagerEmailAddress = ""
            for obj in getdetails.json():
                # add Branch Coordinator role
                try:
                    if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a9387d07ffd388301800392131060ae":
                        branchManagerEmailAddress = obj["email"]
                        task_payload = {
                            "title": "LBF Demand Notice_{}.pdf".format(accountID),
                            "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                            "description": "LBF Demand Notice attached for {account_id}".format(
                                account_id=accountID),
                            "status": "OPEN",
                            "clientID": getloan_response['accountHolderKey'],
                            "username": obj["username"]
                        }

                        taskurl = urls + "tasks"
                        taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                        task_response = taskdetails.json()
                except KeyError:
                    pass

            # search by userrole demand officer head office
            users_payload = {
                "branchID": "8a858ee159de9b420159df2118cd2b67",
                "offset": "0",
                "limit": "1000",
                "fullDetails": "True"
            }
            geturl = urls + "users/"
            getdetails_demand_officer = requests.get(geturl, json=users_payload, headers=headers)
            # CREATE A TASK
            task_response_demand_off = ""
            for obj in getdetails_demand_officer.json():
                # add Branch Coordinator role
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a9387d07ffd388301800392131060ae":
                    task_payload = {
                        "title": "LBF Demand Notice_{}.pdf".format(accountID),
                        "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                        "description": "LBF Demand Notice attached for {account_id}".format(account_id=accountID),
                        "status": "OPEN",
                        "clientID": getloan_response['accountHolderKey'],
                        "username": obj["username"]
                    }

                    taskurl = urls + "tasks"
                    taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                    task_response_demand_off = taskdetails.json()

            # attachDocument
            encoded_string = base64.b64encode(response.content).decode('utf-8')
            attachdoc = {
                "document": {
                    "documentHolderKey": getloan_response['encodedKey'],
                    "documentHolderType": "LOAN_ACCOUNT",
                    "name": "LBF Demand Notice {}".format(accountID),
                    "type": "PDF"
                },
                "documentContent": encoded_string
            }
            doc_url = urls + "loans/{0}/documents/".format(accountID)
            details = requests.post(doc_url, json=attachdoc, headers=headers)

            # get RO Email address
            get_details_RO_details = urls + "loans/{0}/custominformation/{1}".format(accountID, 'RO01')
            get_RO_details = requests.get(get_details_RO_details, headers=headers)

            # print('VEHICLE REG',get_loan_details.json())
            try:
                relationShipOfficerName = get_custom_field("RO01", get_RO_details.json())
            except TypeError:
                relationShipOfficerName = ''

            relationShipOfficerEmail = ''
            if relationShipOfficerName=='Disan Mukalazi':
                relationShipOfficerEmail ="<EMAIL>"
            elif relationShipOfficerName == 'Arnold Kajjoba':
                relationShipOfficerEmail ="<EMAIL>"
            elif relationShipOfficerName == 'Ainomugisha Clare':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Constance Namakoye':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Granet Nahabwe':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Sebufu Geofrey':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Isaac Kajumba':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Moreen Nakayiza':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Norman Mugabe':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Patience Ayebazibwe':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Ritah Kaahwa':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Sophie Nabulondela':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Sandra Namuganyi':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Yvonne Nanseko':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Kazungu Wisely':
                relationShipOfficerEmail ="<EMAIL> "

            elif relationShipOfficerName == 'Irene Nakimuli':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Ronah nabayizi':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Doris Natukunda':
                relationShipOfficerEmail ="<EMAIL>"

            elif getloan_response['loanName'].startswith('SME'):
                relationShipOfficerEmail ="<EMAIL>"

            elif getloan_response['loanName'].startswith('Working'):
                relationShipOfficerEmail ="<EMAIL>"

            else:
                relationShipOfficerEmail = "<EMAIL> "


            # # GMAIL API START
            message = ""
            if client_email is not None or client_email != "":
                CLIENT_SECRET_FILE = gmail_credentials_platug
                service = Create_Service_platke(CLIENT_SECRET_FILE, 'gmail', 'v1', ['https://mail.google.com/'])

                # email body
                emailMsg = """\
                <html>
                  <body>
                    <p>Dear {fistname},<p>
                    <p>Thank you for being our valued customer.</p>
                    <p>This is an urgent notice on your loan account obligation which you have failed to execute as promised.
                    Attached herein is a formal demand notice with respect to the same for your action. Please honor your obligation
                     and have the opportunity of improving and growing your credit worthiness with us.
                     </p>
                    <p>For any clarification, please contact your Relationship Manager or our contact center via **********.</p>
                    <p>PLATINUM CREDIT</p>
    
                  </body>
                </html>
                """.format(fistname=client_details.get('firstName', ''))
                # create email message
                mimeMessage = MIMEMultipart()
                mimeMessage['to'] = client_email
                mimeMessage['cc'] = '<EMAIL>'
                mimeMessage['bcc'] = '{0},{1}'.format(relationShipOfficerEmail,branchManagerEmailAddress)

                mimeMessage['subject'] = "LBF Demand Notice {}.pdf".format(accountID)
                mimeMessage.attach(MIMEText(emailMsg, 'html'))

                part = MIMEApplication(response.content, _subtype='application/pdf')
                part.add_header('Content-Disposition', 'attachment',
                                filename="LBF Demand Notice {}.pdf".format(accountID))

                mimeMessage.attach(part)
                raw_string = base64.urlsafe_b64encode(mimeMessage.as_bytes()).decode()
                try:
                    message = service.users().messages().send(
                        userId='me',
                        body={'raw': raw_string}).execute()
                except HttpError as err:
                    message = err
                    pass

                lbf_demand_notice(
                    accountId=accountID,
                    product_name=getloan_response['loanName'],
                    attachment_response=details.json(),
                    task_response=task_response,
                    email_response=message).save()

                # CLEAR THE ACCOUNTID
                time.sleep(2)
                LBF_demand_weekdays.objects.get(accountId=accountID).delete()
        else:
            LBF_demand_weekdays.objects.get(accountId=accountID).delete()


# LBF demand notices runs every monday at 8 am
@app.task(name='Platinumuganda_DEP_Letters.tasks.LBF_demand_notice_weekends')
def LBF_demand_notice_weekends():
    for obj in LBF_demand_weekend.objects.all():
        accountID= obj.accountId
        environment = obj.environment
        fullName = obj.fullName

        urls = "https://platinumuganda.sandbox.mambu.com/api/" if environment.endswith(
            'sandbox') else 'https://platinumuganda.mambu.com/api/'

        # sandbox
        # headers = {"ApiKey": "********************************"}


        # live
        headers = {"ApiKey": "0DyXjZ2cUyKjLRhImkvQSJ5D2B3ZjyCu"}

        geturl = urls + "loans/{0}".format(accountID)
        getdetails = requests.get(geturl, headers=headers)
        getloan_response = getdetails.json()

        # check if the product is lbf bond
        sentence = "." if getloan_response['loanName'].startswith(
            'LBF Bond') else " subject to a late payment fee, 0.33% of the arrears per day."

        # GET LOAN ACTIVATION DATE
        activation = datetime.strptime(getloan_response['disbursementDetails']['disbursementDate'],
                                       "%Y-%m-%dT%H:%M:%S+%f")
        activation_date = dateformat.format(activation, 'jS F Y')

        now = datetime.now()
        today_date_time = dateformat.format(now, 'jS F Y')
        reference_date = now.strftime("%d%m%Y")

        # GET TOTAL BALANCE
        total_due = float(getloan_response['principalDue']) + float(getloan_response['interestDue']) + float(
            getloan_response['feesDue']) + float(getloan_response['penaltyDue'])
        if total_due > 4999.99:
            # total due in
            p = inflect.engine()
            total_due_words = p.number_to_words(int(total_due)).capitalize()

            # Vehicle number plate
            get_details_veh_details = urls + "loans/{0}/custominformation/{1}".format(accountID, 'reg_no')
            get_veh_details = requests.get(get_details_veh_details, headers=headers)

            # print('VEHICLE REG',get_loan_details.json())
            try:
                vehicle_reg_number = get_custom_field("reg_no", get_veh_details.json())
            except TypeError:
                vehicle_reg_number = ''

            # client details
            client_details = get_client_details(getloan_response["accountHolderKey"], urls, headers)
            # client_email = "<EMAIL>"
            client_email = client_details.get("emailAddress", "")

            context = {
                "full_name": fullName.title(),
                "Client_phone_number": client_details.get("mobilePhone1", ""),
                "today_date_time": today_date_time,
                "Client_fname": client_details.get("firstName", "").title(),
                "total_due": '{:,}'.format(round(total_due)),
                "total_due_words": total_due_words,
                "activation_date": activation_date,
                "vehicle_reg_number": vehicle_reg_number if vehicle_reg_number is not None else "",
                "sentence": sentence,
                "reference_date": reference_date,
                "accountID": accountID,
                "clientID": client_details["id"]
            }
            rendered_html = render_to_string('DEP/lbf/lbf_demand.html', {'context': context}).encode(
                encoding="UTF-8")
            pdf_file = HTML(string=rendered_html, ).write_pdf(
                stylesheets=[CSS(settings.STATIC_ROOT + 'css/platinumuganda_DEP/sme/first_demand_notice.css'),
                             CSS(settings.STATIC_ROOT + 'bootstrap/css/bootstrap.min.css'),
                             CSS(settings.STATIC_ROOT + 'bootstrap/js/bootstrap.min.js')
                             ])
            response = HttpResponse(pdf_file, content_type='application/pdf')
            response['Content-Disposition'] = 'attachment; filename="LBF Demand Notice {}.pdf"'.format(accountID)

            # search branch coordinator by Branch
            users_payload = {
                "branchID": client_details.get("assignedBranchKey", ""),
                "offset": "0",
                "limit": "1000",
                "fullDetails": "True"
            }
            geturl = urls + "users/"
            getdetails = requests.get(geturl, json=users_payload, headers=headers)
            # CREATE A TASK
            task_response = ""
            branchManagerEmailAddress = ""
            for obj in getdetails.json():
                # add Branch Coordinator role
                try:
                    if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a9387d07ffd388301800392131060ae":

                        branchManagerEmailAddress = obj["email"]
                        # print("username:",obj["username"])
                        task_payload = {
                            "title": "LBF Demand Notice_{}.pdf".format(accountID),
                            "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                            "description": "LBF Demand Notice attached for {account_id}".format(
                                account_id=accountID),
                            "status": "OPEN",
                            "clientID": getloan_response['accountHolderKey'],
                            "username": obj["username"]
                        }

                        taskurl = urls + "tasks"
                        taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                        task_response = taskdetails.json()
                except KeyError:
                    pass

            # search by userrole demand officer head office
            users_payload = {
                "branchID": "8a858ee159de9b420159df2118cd2b67",
                "offset": "0",
                "limit": "1000",
                "fullDetails": "True"
            }
            geturl = urls + "users/"
            getdetails_demand_officer = requests.get(geturl, json=users_payload, headers=headers)
            # CREATE A TASK
            task_response_demand_off = ""
            for obj in getdetails_demand_officer.json():
                # add Branch Coordinator role
                if obj["userState"] == "ACTIVE" and obj["role"]["encodedKey"] == "8a93879f6bc116eb016bc21929e72e50":
                    task_payload = {
                        "title": "LBF Demand Notice_{}.pdf".format(accountID),
                        "dueDate": date.today().strftime('%Y-%m-%d')[:10],
                        "description": "LBF Demand Notice attached for {account_id}".format(account_id=accountID),
                        "status": "OPEN",
                        "clientID": getloan_response['accountHolderKey'],
                        "username": obj["username"]
                    }

                    taskurl = urls + "tasks"
                    taskdetails = requests.post(taskurl, params=task_payload, headers=headers)
                    task_response_demand_off = taskdetails.json()

            # attachDocument
            encoded_string = base64.b64encode(response.content).decode('utf-8')
            attachdoc = {
                "document": {
                    "documentHolderKey": getloan_response['encodedKey'],
                    "documentHolderType": "LOAN_ACCOUNT",
                    "name": "LBF Demand Notice {}".format(accountID),
                    "type": "PDF"
                },
                "documentContent": encoded_string
            }
            doc_url = urls + "loans/{0}/documents/".format(accountID)
            details = requests.post(doc_url, json=attachdoc, headers=headers)

            # get RO Email address
            get_details_RO_details = urls + "loans/{0}/custominformation/{1}".format(accountID, 'RO01')
            get_RO_details = requests.get(get_details_RO_details, headers=headers)

            # print('VEHICLE REG',get_loan_details.json())
            try:
                relationShipOfficerName = get_custom_field("RO01", get_RO_details.json())
            except TypeError:
                relationShipOfficerName = ''

            relationShipOfficerEmail = ''
            if relationShipOfficerName=='Disan Mukalazi':
                relationShipOfficerEmail ="<EMAIL>"
            elif relationShipOfficerName == 'Arnold Kajjoba':
                relationShipOfficerEmail ="<EMAIL>"
            elif relationShipOfficerName == 'Ainomugisha Clare':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Constance Namakoye':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Granet Nahabwe':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Sebufu Geofrey':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Isaac Kajumba':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Moreen Nakayiza':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Norman Mugabe':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Patience Ayebazibwe':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Ritah Kaahwa':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Sophie Nabulondela':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Sandra Namuganyi':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Yvonne Nanseko':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Kazungu Wisely':
                relationShipOfficerEmail ="<EMAIL> "

            elif relationShipOfficerName == 'Irene Nakimuli':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Ronah nabayizi':
                relationShipOfficerEmail ="<EMAIL>"

            elif relationShipOfficerName == 'Doris Natukunda':
                relationShipOfficerEmail ="<EMAIL>"
            elif getloan_response['loanName'].startswith('SME'):
                relationShipOfficerEmail ="<EMAIL>"

            elif getloan_response['loanName'].startswith('Working'):
                relationShipOfficerEmail ="<EMAIL>"
            else:
                relationShipOfficerEmail = "<EMAIL> "


            # # GMAIL API START
            message = ""
            if client_email is not None or client_email != "":
                CLIENT_SECRET_FILE = gmail_credentials_platug
                service = Create_Service_platke(CLIENT_SECRET_FILE, 'gmail', 'v1', ['https://mail.google.com/'])

                # email body
                emailMsg = """\
                <html>
                  <body>
                    <p>Dear {fistname},<p>
                    <p>Thank you for being our valued customer.</p>
                    <p>This is an urgent notice on your loan account obligation which you have failed to execute as promised.
                    Attached herein is a formal demand notice with respect to the same for your action. Please honor your obligation
                     and have the opportunity of improving and growing your credit worthiness with us.
                     </p>
                    <p>For any clarification, please contact your Relationship Manager or our contact center via **********.</p>
                    <p>PLATINUM CREDIT</p>
    
                  </body>
                </html>
                """.format(fistname=client_details.get('firstName', ''))
                # create email message
                mimeMessage = MIMEMultipart()
                mimeMessage['to'] = client_email
                mimeMessage['cc'] = '<EMAIL>'
                mimeMessage['bcc'] = '{0},{1}'.format(relationShipOfficerEmail,branchManagerEmailAddress)
                mimeMessage['subject'] = "LBF Demand Notice {}.pdf".format(accountID)
                mimeMessage.attach(MIMEText(emailMsg, 'html'))

                part = MIMEApplication(response.content, _subtype='application/pdf')
                part.add_header('Content-Disposition', 'attachment',
                                filename="LBF Demand Notice {}.pdf".format(accountID))

                mimeMessage.attach(part)
                raw_string = base64.urlsafe_b64encode(mimeMessage.as_bytes()).decode()
                try:
                    message = service.users().messages().send(
                        userId='me',
                        body={'raw': raw_string}).execute()
                except HttpError as err:
                    message = err
                    pass

                lbf_demand_notice(
                    accountId=accountID,
                    product_name=getloan_response['loanName'],
                    attachment_response=details.json(),
                    task_response=task_response,
                    email_response=message).save()

                # CLEAR THE ACCOUNTID
                time.sleep(2)
                LBF_demand_weekend.objects.get(accountId=accountID).delete()
        else:
            LBF_demand_weekend.objects.get(accountId=accountID).delete()



