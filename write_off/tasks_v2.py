import configparser

from newugandalms.celery import app
from mambu.models import Credential, Environment
from datetime import datetime, date, timedelta
import time
from .models import Uploaded_Files_writeoff,WriteOffDetailsPremUg,WriteOffDetailsPlatUg,Uploaded_Files_writeoff_platug
# from django.db import transaction
import requests
import json
import datetime

def get_client_id(headers,urls,account_id):
    # Use V2 API headers for loan operations with full details
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey']
    }
    # V2 API endpoint 
    geturl = urls + "loans/{0}?detailsLevel=FULL".format(account_id)
    get_clients_details = requests.get(geturl, headers=v2_headers)
    return get_clients_details.json().get('accountHolderKey','')


def get_lgf_balance(urls,headers,client_id):
    # Use V2 API headers and search endpoint for savings operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
        'Content-Type': 'application/json'
    }
    # V2 API uses search endpoint with filter criteria
    geturl = urls + "deposits:search?detailsLevel=FULL"
    search_payload = {
        "filterCriteria": [
            {
                "field": "accountHolderKey",
                "operator": "EQUALS",
                "value": client_id
            }
        ]
    }
    get_lgf_details = requests.post(geturl, json=search_payload, headers=v2_headers)

    # Process search results
    if get_lgf_details.status_code == 200:
        search_results = get_lgf_details.json()
        for obj in search_results:
            return obj.get('balance','')
    return ''


def get_lgf_id(urls,headers,client_id,account_id):
    # Use V2 API headers and search endpoint for savings operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
        'Content-Type': 'application/json'
    }
    # V2 API uses search endpoint with filter criteria
    geturl = urls + "deposits:search?detailsLevel=FULL"
    search_payload = {
        "filterCriteria": [
            {
                "field": "accountHolderKey",
                "operator": "EQUALS",
                "value": client_id
            }
        ]
    }
    get_lgf_details = requests.post(geturl, json=search_payload, headers=v2_headers)

    # Process search results
    if get_lgf_details.status_code == 200:
        search_results = get_lgf_details.json()
        for obj in search_results:
            if obj['accountState'] =="ACTIVE":
                if int(obj['balance']) > 0:
                    # TRANSFER PROCESS START
                    tranfer_to_loanaccount=transfer_lgf_amount(account_id,obj['balance'],obj['id'],urls,headers)
                    close_lgf_account=close_lgf_after_transfer(urls,headers,obj['id'])
                else:
                    close_lgf_account=close_lgf_after_transfer(urls,headers,obj['id'])
            elif obj['accountState'] == "APPROVED":
                # V2 headers for POST operations
                v2_post_headers = {
                    'Accept': 'application/vnd.mambu.v2+json',
                    'ApiKey': headers['ApiKey'],
                    'Content-Type': 'application/json'
                }
                payload = {
                    'type': "withdraw",
                    'notes': "account withdrawn",
                }
                geturl = urls + "deposits/{0}/transactions".format(obj['id'])
                lgf_close_details = requests.post(geturl, json=payload, headers=v2_post_headers)
            else:
                close_lgf_account = close_lgf_after_transfer(urls, headers, obj['id'])


def transfer_lgf_amount(account_id,lgf_balance,lgf_id,urls,headers):
    # Use V2 API headers for loan and savings operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
        'Content-Type': 'application/json'
    }
    
    # check account substatus
    transfer_results = ""
    url = urls + "loans/{0}?detailsLevel=FULL".format(account_id)
    get_account_substatus = requests.get(url, headers=v2_headers)

    if "accountSubState" in get_account_substatus.json():
        if get_account_substatus.json()['accountState'] =='ACTIVE_IN_ARREARS'  or get_account_substatus.json()['accountState'] =='ACTIVE' and get_account_substatus.json()['accountSubState'] =='LOCKED':
            # V2 unlock endpoint
            geturl = urls + "loans/{0}/unlock-transactions".format(account_id)
            post_transaction = requests.post(geturl, json={}, headers=v2_headers)
            
            # V2 transfer payload structure
            payload = {
                'amount': lgf_balance,
                'notes': "deposit balance transferred to loan account before write off",
                'transferDetails': {
                    'linkedAccountId': account_id,
                    'linkedAccountType': 'LOAN'
                }
            }

            geturl = urls + "deposits/{0}/transfer-transactions".format(lgf_id)
            post_lgf_balance = requests.post(geturl, json=payload, headers=v2_headers)
            transfer_results = post_lgf_balance.json()
    else:
        # V2 transfer payload structure
        payload = {
            'amount': lgf_balance,
            'notes': "deposit balance transferred to loan account before write off",
            'transferDetails': {
                'linkedAccountId': account_id,
                'linkedAccountType': 'LOAN'
            }
        }

        geturl = urls + "deposits/{0}/transfer-transactions".format(lgf_id)
        post_lgf_balance = requests.post(geturl, json=payload, headers=v2_headers)
        transfer_results = post_lgf_balance.json()

    return transfer_results


def close_lgf_after_transfer(urls,headers,lgf_id):
    # Use V2 API headers for savings operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
        'Content-Type': 'application/json'
    }
    # V2 API uses changeState endpoint
    payload = {
        'action': 'CLOSE',
        'notes': 'account closed',
    }
    geturl = urls + "deposits/{0}:changeState".format(lgf_id)
    lgf_close_details = requests.post(geturl, json=payload, headers=v2_headers)
    return lgf_close_details.json()


def get_write_off(urls,headers,account_id,reason_writeoff):
    # Use V2 API headers for loan write-off operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
        'Content-Type': 'application/json'
    }
    # V2 API uses writeOff endpoint
    payload = {
        "notes": reason_writeoff
    }
    geturl = urls + "loans/{0}:writeOff".format(account_id)
    writeoff_details = requests.post(geturl, json=payload, headers=v2_headers)
    return writeoff_details.json()


def close_written_off_account(urls,headers,account_id):
    # Use V2 API headers for loan operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
        'Content-Type': 'application/json'
    }
    # V2 API uses changeState endpoint
    payload = {
        "action": "CLOSE",
        "notes": "Account closed after writeoff"
    }
    geturl = urls + "loans/{0}:changeState".format(account_id)
    close_account_details = requests.post(geturl, json=payload, headers=v2_headers)
    return close_account_details.json()


def get_loan_balance(urls,headers,account_id):
    # Use V2 API headers for loan operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey']
    }
    geturl = urls + "loans/{0}?detailsLevel=FULL".format(account_id)
    get_loan_details = requests.get(geturl, headers=v2_headers)
    # V2 API: Access balance fields from nested balances object
    loan_data = get_loan_details.json()
    balances = loan_data.get('balances', {})

    principal_balance = float(balances.get('principalBalance', 0))
    interest_balance = float(balances.get('interestBalance', 0))
    fees_balance = float(balances.get('feesBalance', 0))
    penalty_balance = float(balances.get('penaltyBalance', 0))
    accrued_interest = float(loan_data.get('accruedInterest', 0))

    total_bal = principal_balance + interest_balance + fees_balance + penalty_balance + accrued_interest
    return total_bal


def get_transactions(deposit_id,urls,headers):
    # Use V2 API headers for deposit transactions
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey']
    }
    geturl = urls + "deposits/{0}/transactions".format(deposit_id)
    payload = {'offset': 0, 'limit': 1000}

    deposit_details = requests.get(geturl,params=payload, headers=v2_headers)
    return deposit_details.json()


def get_loan_name(urls,headers,account_id):
    # Use V2 API headers for loan operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey']
    }
    geturl = urls + "loans/{0}?detailsLevel=FULL".format(account_id)
    get_loan_details = requests.get(geturl, headers=v2_headers)
    return get_loan_details.json().get('loanName','')


def get_balance_to_maturity(urls,headers,account_id):
    # Use V2 API headers for loan repayment schedule
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey']
    }
    payload = {'offset': 0, 'limit': 1000}
    geturl = urls + "loans/{0}/schedule".format(account_id)
    get_repayment_schedule = requests.get(geturl,params=payload, headers=v2_headers)
    
    # V2 API response structure: schedule has installments array
    schedule_response = get_repayment_schedule.json()
    total_due = 0
    total_paid = 0
    
    if 'installments' in schedule_response:
        for installment in schedule_response['installments']:
            # V2 structure: nested amount objects
            total_due += float(installment.get('principal', {}).get('amount', {}).get('due', 0))
            total_due += float(installment.get('interest', {}).get('amount', {}).get('due', 0))
            total_due += float(installment.get('fee', {}).get('amount', {}).get('due', 0))
            total_due += float(installment.get('penalty', {}).get('amount', {}).get('due', 0))
            
            total_paid += float(installment.get('principal', {}).get('amount', {}).get('paid', 0))
            total_paid += float(installment.get('interest', {}).get('amount', {}).get('paid', 0))
            total_paid += float(installment.get('fee', {}).get('amount', {}).get('paid', 0))
            total_paid += float(installment.get('penalty', {}).get('amount', {}).get('paid', 0))
    else:
        # Fallback: if response is direct array
        for obj in schedule_response:
            total_due += float(obj.get("principalDue", 0)) + float(obj.get("interestDue", 0)) + float(obj.get("feesDue", 0)) + float(obj.get("penaltyDue", 0))
            total_paid += float(obj.get("principalPaid", 0)) + float(obj.get("interestPaid", 0)) + float(obj.get("feesPaid", 0)) + float(obj.get("penaltyPaid", 0))
    
    return total_due-total_paid


def create_bad_debt_account(urls ,headers,client_id,account_id, new_loan_balance, balance_to_maturity,loan_name,lgf_balance):
    # Use V2 API headers for deposit account creation
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey'],
        'Content-Type': 'application/json'
    }

    # product key for the bad debt account
    productTypeKey="8a2ac4eb5056ccc801505b4e6bdd6d91"
    leo = datetime.datetime.today().strftime('%Y-%m-%d')[:10]

    # V2 API payload structure
    bad_debt_payload = {
        "accountHolderKey": client_id,
        "accountHolderType": "CLIENT",
        "productTypeKey": productTypeKey,
        "accountType": "REGULAR_SAVINGS",
        "accountState": "APPROVED",
        "name": "Bad Debt Recovered A/C",
        # V2 custom fields
        "_customFieldValues": {
            "WOD_01": str(leo),  # writeoff date
            "WLO_01": account_id,  # written off loan ID
            "WOB_01": new_loan_balance,  # written off balance
            "BM_01": new_loan_balance,  # balance maturity
            "writen_off_loan_name": loan_name,  # written off loan name
            "balance_to_recover": new_loan_balance  # balance to recover
        }
    }

    # V2 API endpoint for creating deposits
    geturl = urls + "deposits"
    create_bad_debt_details = requests.post(geturl, json=bad_debt_payload, headers=v2_headers)
    return create_bad_debt_details.json()


def blacklist_client(urls,headers_details,client_id,reasons_blacklisting):
    # V2 API headers for client operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers_details['ApiKey'],
        'Content-Type': 'application/json'
    }

    geturl = urls + "clients/{0}".format(client_id)

    # V2 API PATCH payload for custom field update
    v2_payload_custom_field = [
        {
            "op": "REPLACE",
            "path": "/_customFieldValues/BR001",
            "value": str(reasons_blacklisting)
        }
    ]

    updateResults = requests.patch(geturl, json=v2_payload_custom_field, headers=v2_headers)
    print("updateResults",updateResults.json())

    # V2 API PATCH payload for blacklisting
    v2_payload_blacklist = [
        {
            "op": "REPLACE",
            "path": "state",
            "value": "BLACKLISTED"
        }
    ]

    blacklist_details = requests.patch(geturl, json=v2_payload_blacklist, headers=v2_headers)
    print("blacklist_details",blacklist_details.json())

    if blacklist_details.status_code in [400, 401]:
        return blacklist_details.json()
    else:
        return blacklist_details.status_code


# check client state
def check_client_state(client_id,urls,headers):
    # V2 headers for client operations
    v2_headers = {
        'Accept': 'application/vnd.mambu.v2+json',
        'ApiKey': headers['ApiKey']
    }
    client_url = urls + "clients/{}?detailsLevel=FULL".format(client_id)
    client_details = requests.get(client_url, headers=v2_headers)

    if client_details.status_code == 200:
        if client_details.json()['state'] == "BLACKLISTED":
            # V2 PATCH payload for state change
            undo_blacklist = [
                {
                    "op": "REPLACE",
                    "path": "state",
                    "value": "ACTIVE"
                }
            ]
            v2_patch_headers = {
                'Accept': 'application/vnd.mambu.v2+json',
                'ApiKey': headers['ApiKey'],
                'Content-Type': 'application/json'
            }
            patch_client_details = requests.patch(client_url, json=undo_blacklist, headers=v2_patch_headers)


def get_custom_field_value(fieldID, entity):
    for custom_field in entity:
        if custom_field["customFieldID"] == fieldID:
            return custom_field["value"]
    return None


@app.task
def approve_writeoff(account_id,environment, reason_writeoff, writeoff_id, req, user,reasons_blacklisting):
    urls = "https://premieruganda.sandbox.mambu.com/api/" if environment.endswith(
        'sandbox') else 'https://premieruganda.mambu.com/api/'

    config = configparser.ConfigParser()
    config.read('secrets.ini')
    bulkWritOff = config.get('Credentials', 'PREMIERUGANDA_PROD_APIKEY_BULKWRITEOFF')

    # V2 API headers
    headers = {
        "Accept": "application/vnd.mambu.v2+json",
        "ApiKey": "{}".format(bulkWritOff)
    }

    # GET LOAN NAME BEFORE WRITEOFF
    loan_name = get_loan_name(urls, headers, account_id)

    # GET ACCOUNTHOLDER ENCODEDEDKEY/CLIENT ID
    client_id= get_client_id(headers,urls,account_id)

    # check client state
    client_state = check_client_state(client_id, urls, headers)

    # GET LGF DETAILS using V2 search endpoint
    v2_headers_with_content = {
        "Accept": "application/vnd.mambu.v2+json",
        "ApiKey": "{}".format(bulkWritOff),
        "Content-Type": "application/json"
    }

    # V2 search for deposits by client
    search_payload = {
        "filterCriteria": [
            {
                "field": "accountHolderKey",
                "operator": "EQUALS",
                "value": client_id
            }
        ]
    }
    geturl = urls + "deposits:search?detailsLevel=FULL"
    get_lgf_details = requests.post(geturl, json=search_payload, headers=v2_headers_with_content)
    lgf_balance = ''

    # Process V2 search results
    if get_lgf_details.status_code == 200:
        search_results = get_lgf_details.json()
        for obj in search_results:
            if obj['accountState'] =="ACTIVE":
                if int(obj['balance']) > 0:
                    lgf_balance = obj['balance']
                    # TRANSFER PROCESS START
                    tranfer_to_loanaccount=transfer_lgf_amount(account_id,lgf_balance,obj['id'],urls,headers)

                    # CLOSE LGF ACCOUNT AFTER TRANSFER IS DONE
                    close_lgf_account=close_lgf_after_transfer(urls,headers,obj['id'])
                else:
                    # CLOSE LGF ACCOUNT WHOSE STATE IS ACTIVE WITH ZERO BALANCE
                    close_lgf_account=close_lgf_after_transfer(urls,headers,obj['id'])
            # WITHDRAW LGF ACCOUNTS WHOSE STATE IS APPROVED
            elif obj['accountState'] == "APPROVED":
                lgf_balance=0
                payload = {
                    'type': "withdraw",
                    'notes': "account withdrawn",
                }
                geturl = urls + "deposits/{0}/transactions".format(obj['id'])
                lgf_close_details = requests.post(geturl, json=payload, headers=v2_headers_with_content)
            else:
                lgf_balance=0
                #CLOSE LGF ACCOUNTS WHOSE STATE IS NEITHER ACTIVE NOR APPROVED
                close_lgf_account = close_lgf_after_transfer(urls, headers, obj['id'])

    # GET LOAN BALANCE BEFORE WRITEOFF
    loan_balance = get_loan_balance(urls,headers,account_id)
    new_loan_balance = int(round(float(loan_balance) * (110/100),2))

    # WRITING OFF LOAN ACCOUNT START
    write_off_loan=get_write_off(urls,headers,account_id,reason_writeoff)

    # GET BALANCE TO MATURITY
    balance_to_maturity = get_balance_to_maturity(urls, headers, account_id)

    # CREATE A BAD DEBT RECOVERED ACCOUNT AND PATCH RESPECTIVE CUSTOMFIELDS
    bad_debt_details = create_bad_debt_account(urls,headers,client_id,account_id,new_loan_balance,balance_to_maturity,loan_name,lgf_balance)

    # BLACKLIST CLIENT AFTER CREATION OF BAD DEBT ACCOUNT
    blacklist_details = blacklist_client(urls,headers,client_id,reasons_blacklisting)

    # UPDATE STATUS AFTER WRITEOFF PROCESS
    writeoff_status = WriteOffDetailsPremUg.objects.get(id=writeoff_id)
    writeoff_status.status = 'Approved'
    writeoff_status.write_off_loan_response = write_off_loan
    writeoff_status.badDebt_details_response = bad_debt_details
    writeoff_status.blacklistDetails_response = blacklist_details
    writeoff_status.approved_rejected_by = user["firstName"] + " " + user["lastName"]
    writeoff_status.save()

    #UPDATING THE UPLOADED FILES STATUS
    objs=Uploaded_Files_writeoff.objects.get(id=req)
    objs.status="Approved"
    objs.save()


@app.task
def reject_writeoff(environment, writeoff_id, req, user):
    env = Environment.objects.get(url=environment)
    urls = "https://premieruganda.sandbox.mambu.com/api/" if environment.endswith(
        'sandbox') else 'https://premieruganda.mambu.com/api/'
    auth_user = Credential.objects.get(apikey="ApiKey", environment=env)

    # V2 API headers (though not used for API calls in this function)
    headers = {
        "Accept": "application/vnd.mambu.v2+json",
        auth_user.apikey: auth_user.generatedkey
    }

    # UPDATE STATUS AFTER WRITEOFF PROCESS
    writeoff_status = WriteOffDetailsPremUg.objects.get(id=writeoff_id)
    writeoff_status.status = 'Rejected'
    writeoff_status.approved_rejected_by = user["firstName"] + " " + user["lastName"]
    writeoff_status.save()

    #UPDATING THE UPLOADED FILES STATUS
    objs=Uploaded_Files_writeoff.objects.get(id=req)
    objs.status="Rejected"
    objs.save()


@app.task
def deposit_activities(deposit_id, environment):
    env = Environment.objects.get(url=environment)
    urls = "https://premieruganda.sandbox.mambu.com/api/" if environment.endswith(
        'sandbox') else 'https://premieruganda.mambu.com/api/'
    config = configparser.ConfigParser()
    config.read('secrets.ini')
    bulkWritOff = config.get('Credentials', 'PREMIERUGANDA_PROD_APIKEY_BULKWRITEOFF')

    # V2 API headers
    headers = {
        "Accept": "application/vnd.mambu.v2+json",
        "ApiKey": "{}".format(bulkWritOff)
    }

    # GET TOTAL DEPOSITS AND DEPOSIT ADJUSTMENTS
    total_depos = get_transactions(deposit_id,urls,headers)

    total_deposits = 0
    total_adjustment = 0
    total_withdrawals = 0
    total_withdrawaw_adjustment = 0
    for obj in total_depos:
        if obj['type'] == "DEPOSIT":
            total_deposits += float(obj['amount'])
        elif obj['type'] == "ADJUSTMENT":
            total_adjustment += float(obj['amount'])
        elif obj['type'] == "WITHDRAWAL":
            total_withdrawals += float(obj['amount'])
        elif obj['type'] == "WITHDRAWAL_ADJUSTMENT":
            total_withdrawaw_adjustment += float(obj['amount'])
        else:
            pass
    actual_depo_diff = abs(total_deposits)-abs(total_adjustment) - abs(total_withdrawals) + abs(total_withdrawaw_adjustment)

    actual_depo_amount = abs(actual_depo_diff)

    # V2 API headers for PATCH operations
    v2_patch_headers = {
        "Accept": "application/vnd.mambu.v2+json",
        "ApiKey": "{}".format(bulkWritOff),
        "Content-Type": "application/json"
    }

    # GET TOTAL AMOUNT RECOVERED AND UPDATE WITH DEPOSIT AMOUNT (V2 API)
    # First get the deposit account to access custom fields
    deposit_url = urls + "deposits/{0}?detailsLevel=FULL".format(deposit_id)
    deposit_details = requests.get(deposit_url, headers=headers)

    # Extract current amount recovered value and writeoff balance
    amt_recovered = 0
    writeoff_bal = 0
    if deposit_details.status_code == 200:
        deposit_data = deposit_details.json()
        if '_customFieldValues' in deposit_data:
            amt_recovered = deposit_data['_customFieldValues'].get('_AM_RC_01', 0) or 0
            writeoff_bal = deposit_data['_customFieldValues'].get('WOB_01', 0) or 0

    # V2 API PATCH payload for updating custom fields
    amount_recoverd_payload = [
        {
            "op": "REPLACE",
            "path": "/_customFieldValues/_AM_RC_01",
            "value": actual_depo_amount
        }
    ]

    geturl = urls + "deposits/{0}".format(deposit_id)
    patch_amount_recovered = requests.patch(geturl, json=amount_recoverd_payload, headers=v2_patch_headers)

    # V2 API PATCH payload for balance to recover
    bal_recover_payload = [
        {
            "op": "REPLACE",
            "path": "/_customFieldValues/balance_to_recover",
            "value": float(writeoff_bal) - float(actual_depo_amount)
        }
    ]

    geturl = urls + "deposits/{0}".format(deposit_id)
    bal_recover_details = requests.patch(geturl, json=bal_recover_payload, headers=v2_patch_headers)

    # Get new balance to recover
    new_bal_recover = float(writeoff_bal) - float(actual_depo_amount)

    # V2 API recovery status updates
    if float(new_bal_recover) > 0.0:
        partially_recovered_payload = [
            {
                "op": "REPLACE",
                "path": "/_customFieldValues/REC_STAT",
                "value": "Partially Recovered"
            }
        ]
        geturl = urls + "deposits/{0}".format(deposit_id)
        full_recovery_details = requests.patch(geturl, json=partially_recovered_payload, headers=v2_patch_headers)
    elif float(new_bal_recover) <= 0.0:
        fully_recovered_payload = [
            {
                "op": "REPLACE",
                "path": "/_customFieldValues/REC_STAT",
                "value": "Fully Recovered"
            }
        ]
        geturl = urls + "deposits/{0}".format(deposit_id)
        partial_recovery_details = requests.patch(geturl, json=fully_recovered_payload, headers=v2_patch_headers)

    if actual_depo_amount == 0.0:
        # V2 API: Remove custom field by setting to null
        delete_field_payload = [
            {
                "op": "REMOVE",
                "path": "/_customFieldValues/REC_STAT"
            }
        ]
        geturl = urls + "deposits/{0}".format(deposit_id)
        field_delete = requests.patch(geturl, json=delete_field_payload, headers=v2_patch_headers)
        data = field_delete.json()
        print('DATA',data)
